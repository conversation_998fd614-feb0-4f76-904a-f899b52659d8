package com.honichang.netty.service.impl;

import com.honichang.netty.RobotOperationService;
import com.honichang.netty.core.ClientManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 机器人操作实现类
 */
@Service
@Slf4j
public class RobotOperationServiceImpl  implements RobotOperationService {

    @Resource
    private ClientManager clientManager;

    @Override
    public String extendableRodsUp(String robtId,String id) {

        return "";
    }
}
