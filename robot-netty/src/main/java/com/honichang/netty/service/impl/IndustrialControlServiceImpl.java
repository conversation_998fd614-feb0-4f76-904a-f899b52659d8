package com.honichang.netty.service.impl;

import com.honichang.netty.domain.*;
import com.honichang.netty.enums.CommandEnum;
import com.honichang.netty.enums.IndustrialEnum;
import com.honichang.netty.enums.RobotLogEnum;
import com.honichang.netty.enums.ServiceLogEnum;
import com.honichang.netty.service.IndustrialControlService;
import com.honichang.netty.service.IndustrialLogService;
import com.honichang.netty.service.ServerCacheService;
import com.honichang.netty.service.RobotCacheService;
import io.netty.channel.ChannelFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 工控部分接口
 * 接口均采用同步回调方式处理
 */
@Slf4j
@Service
public class IndustrialControlServiceImpl implements IndustrialControlService {

    @Resource
    private IndustrialLogService industrialLogService;
    @Resource
    private RobotCacheService robotCacheService;
    @Resource
    private ServerCacheService serverCacheService;
    //单包参数最大长度 单位 byte
    private static final int SEGMENTATION = 100 * 1024;
    //单位秒
    private static final int TIMEOUT = 3;

    @Override
    public void robotConnection(Long robotId, String ip, int port) {
        RobotInformation robotInformation = robotCacheService.getRobotInformation(robotId);
        if (robotInformation != null) {
            industrialLogService.saveRobotLog(RobotConnectionLog.builder().robotId(robotId).createTime(new Date()).type(RobotLogEnum.Change.getStatus()).message("变更设备").build());
        } else {
            industrialLogService.saveRobotLog(RobotConnectionLog.builder().robotId(robotId).createTime(new Date()).type(RobotLogEnum.Loading.getStatus()).message("加载设备").build());
        }
        robotCacheService.addRobotInformation(new RobotInformation(robotId, ip, port));
    }

    @Override
    public void robotRemove(Long robotId) {
        industrialLogService.saveRobotLog(RobotConnectionLog.builder().robotId(robotId).createTime(new Date()).type(RobotLogEnum.Delete.getStatus()).message("删除设备").build());
        robotCacheService.removeRobotInformation(robotId);
    }

    @Override
    public IndustrialControlResult firmwareUpdate(Long robotId, String filePath) {
        IndustrialControlResult result = sendMessage(robotId, "", 1L, false, IndustrialEnum.FirmwareUpdate.getInstruction());
        if (result.getCode() == 0) {
            return this.sendData(robotId, filePath, result.getCommandId(), CommandEnum.DATA.getCode());
        } else {
            return result;
        }
    }

    @Override
    public IndustrialControlResult firmwareUpdateConfirm(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.FirmwareUpdateConfirm.getInstruction());
    }


    @Override
    public IndustrialControlResult musicUpdate(Long robotId, Long fileId, String filePath) {
        IndustrialControlResult result = sendMessage(robotId, fileId.toString(), 1L, false, IndustrialEnum.MusicUpdate.getInstruction());
        if (result.getCode() == 0) {
            return this.sendData(robotId, filePath, result.getCommandId(), CommandEnum.DATA.getCode());
        } else {
            return result;
        }
    }

    @Override
    public IndustrialControlResult musicPlay(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.MusicPlay.getInstruction());
    }

    @Override
    public IndustrialControlResult musicPause(Long robotId, Long fileId) {
        return sendMessage(robotId, fileId.toString(), 0L, true, IndustrialEnum.MusicPause.getInstruction());
    }

    @Override
    public IndustrialControlResult restartRobot(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.RestartRobot.getInstruction());
    }

    @Override
    public IndustrialControlResult taskDistribution(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.TaskDistribution.getInstruction());
    }


    @Override
    public IndustrialControlResult cacelTask(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.CacelTask.getInstruction());
    }

    @Override
    public IndustrialControlResult getRobotStatus(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.GetRobotStatus.getInstruction());
    }

    @Override
    public IndustrialControlResult getRobotVariable(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.GetRobotVariable.getInstruction());
    }

    @Override
    public IndustrialControlResult setRobotVariable(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.SetRobotVariable.getInstruction());
    }

    @Override
    public IndustrialControlResult setRobotMode(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.SetRobotMode.getInstruction());
    }

    @Override
    public IndustrialControlResult setRobotPosition(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.SetRobotPosition.getInstruction());
    }

    @Override
    public IndustrialControlResult confirmRobotPosition(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.ConfirmRobotPosition.getInstruction());
    }

    @Override
    public IndustrialControlResult pausetNavigation(Long robotId) {
        return sendMessage(robotId, "Pause", 0L, true, IndustrialEnum.PauseOrResumeOrAbortNavigation.getInstruction());
    }

    @Override
    public IndustrialControlResult resumeNavigation(Long robotId) {
        return sendMessage(robotId, "Resume", 0L, true, IndustrialEnum.PauseOrResumeOrAbortNavigation.getInstruction());
    }

    @Override
    public IndustrialControlResult abortNavigation(Long robotId) {
        return sendMessage(robotId, "Abort", 0L, true, IndustrialEnum.PauseOrResumeOrAbortNavigation.getInstruction());
    }


    @Override
    public IndustrialControlResult startNavigationDirection(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.StartNavigationDirection.getInstruction());
    }

    @Override
    public IndustrialControlResult openAndCloseChargingPort(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.OpenAndCloseChargingPort.getInstruction());
    }

    @Override
    public IndustrialControlResult gimbalReset(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.GimbalReset.getInstruction());
    }

    @Override
    public IndustrialControlResult telescopicPole(Long robotId, String params) {
        return sendMessage(robotId, params, 0L, true, IndustrialEnum.TelescopicPole.getInstruction());
    }


    /**
     * 下发消息
     *
     * @param robotId     机器人id
     * @param params      参数
     * @param ackId       ack标识>0有ack确认消息
     * @param resultFlag  是否需要返回值
     * @param commandName 下发接口指令
     */
    private IndustrialControlResult sendMessage(Long robotId, String params, Long ackId, boolean resultFlag, String commandName) {
        IndustrialControlResult result = null;
        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(null).commandName(commandName).time(new Date()).message(params).type(ServiceLogEnum.InterfaceCall.getStatus()).build());
        RobotInformation rim = robotCacheService.getRobotInformation(robotId);
        if (rim == null || rim.getConnectionStatus() != 2) {
            result = IndustrialControlResult.builder().robotId(robotId).code(ServiceLogEnum.InterfaceBack.getStatus()).message(rim == null ? "设备信息不存在" : "设备不可用").build();
        } else {
            RequestMessage req = new RequestMessage(robotCacheService.getCommandId(rim.getRobotId()), ackId, commandName, resultFlag, params);
            try {
                industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(req.getCommandId()).commandName(commandName).time(new Date()).message(req.getMessage()).type(ServiceLogEnum.DataDistribution.getStatus()).build());
                ChannelFuture future = rim.getChannel().writeAndFlush(req.getMessage()).sync();
                if (future.isSuccess()) {
                    MessageCache messageCache = new MessageCache(robotId, req.getCommandId(), rim.getChannel().id().toString(), commandName, params, new CompletableFuture<>());
                    serverCacheService.addMessage(messageCache);
                    try {
                        ResponseMessage message = messageCache.getResponseFuture().get(TIMEOUT, TimeUnit.SECONDS);
                        result = IndustrialControlResult.builder().robotId(robotId).code(0).commandId(req.getCommandId()).params(message.getParams()).message("收到工控回复").build();
                    } catch (ExecutionException e) {
                        result = IndustrialControlResult.builder().robotId(robotId).code(2).message("异步任务消息异常").build();
                    } catch (TimeoutException e) {
                        result = IndustrialControlResult.builder().robotId(robotId).code(2).message("异步任务消息超时").build();
                    } finally {
                        serverCacheService.removeMessage(messageCache);
                        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(req.getCommandId()).commandName(commandName).time(new Date()).message(result.getMessage()).type(ServiceLogEnum.InterfaceBack.getStatus()).build());
                    }
                    return result;
                } else {
                    result = IndustrialControlResult.builder().robotId(robotId).code(2).message("消息发送失败，原因：" + future.cause()).build();
                }
            } catch (InterruptedException interruptedException) {
                result = IndustrialControlResult.builder().robotId(robotId).code(2).message("消息发送异常，原因：" + interruptedException.getMessage()).build();
            }
        }
        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandName(commandName).time(new Date()).message(result.getMessage()).type(ServiceLogEnum.InterfaceBack.getStatus()).build());
        return result;
    }


    /**
     * 发送文件
     *
     * @param robotId     设备ID
     * @param filePath    文件绝对路径
     * @param commandName 指令
     */
    private IndustrialControlResult sendData(Long robotId, String filePath, Long commId, String commandName) {
        IndustrialControlResult result = null;
        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(null).commandName(commandName).time(new Date()).message("调用下发文件接口").type(0).build());
        RobotInformation robotInformation = robotCacheService.getRobotInformation(robotId);
        if (robotInformation == null) {
            result = IndustrialControlResult.builder().robotId(robotId).code(2).message("设备信息不存在").build();
        } else if (robotInformation.getConnectionStatus() != 2) {
            result = IndustrialControlResult.builder().robotId(robotId).code(2).message("设备不可用").build();
        } else {
            File file = new File(filePath);
            long fileSize = file.length();
            try (FileInputStream fileInputStream = new FileInputStream(file); BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream)) {
                byte[] buffer = new byte[SEGMENTATION];
                int bytesRead;
                long totalBytesRead = 0;
                while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                    totalBytesRead += bytesRead;
                    StringBuilder binaryString = new StringBuilder();
                    for (int i = 0; i < bytesRead; i++) {
                        binaryString.append(String.format("%8s", Integer.toBinaryString(buffer[i] & 0xFF)).replace(' ', '0'));
                    }
                    RequestMessage requestMessage = new RequestMessage(robotId, 0l, commandName, false, binaryString.toString());
                    // 判断是否是最后一段
                    if (totalBytesRead >= fileSize) {
                        requestMessage.setResult(true);
                    }
                    try {
                        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(commId).commandName(commandName).time(new Date()).message(requestMessage.getMessage()).type(1).build());
                        ChannelFuture future = robotInformation.getChannel().writeAndFlush(requestMessage.getMessage()).sync();
                        if (!future.isSuccess()) {
                            result = IndustrialControlResult.builder().robotId(robotId).code(2).message("消息发送失败，原因：" + future.cause()).build();
                            break;
                        }
                    } catch (InterruptedException interruptedException) {
                        result = IndustrialControlResult.builder().robotId(robotId).code(2).message("消息发送失败，原因：" + interruptedException.getMessage()).build();
                        industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(commId).commandName(commandName).time(new Date()).message(result.getMessage()).type(1).build());
                        bufferedInputStream.close();
                        break;
                    }
                }
            } catch (IOException e) {
                result = IndustrialControlResult.builder().robotId(robotId).code(2).message("消息下发异常,原因：" + e.getMessage()).build();
                industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandId(commId).commandName(commandName).time(new Date()).message("消息下发异常,原因：" + e.getMessage()).type(1).build());
            }
            if (result == null) {
                result = IndustrialControlResult.builder().robotId(robotId).code(0).message("文件下发成功").build();
            }
            industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(robotId).commandName(commandName).time(new Date()).message(result.getMessage()).type(4).build());
        }
        return result;
    }


}
