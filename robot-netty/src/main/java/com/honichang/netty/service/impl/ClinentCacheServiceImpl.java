package com.honichang.netty.service.impl;

import ch.qos.logback.core.net.server.Client;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.honichang.netty.domain.MessageCache;
import com.honichang.netty.service.ClientCacheService;
import com.honichang.netty.service.ServerCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ClinentCacheServiceImpl implements ClientCacheService {
    Cache<String, MessageCache> messageCache = Caffeine.newBuilder().maximumSize(1000000l)//最大值
            .expireAfterWrite(10L, TimeUnit.SECONDS)//10秒没有写入自定淘汰 释放缓存
            .removalListener(this::removal).build();

    @Override
    public MessageCache getMessage(Long robotId, Long commandId, String channelId) {
        return messageCache.asMap().get(getKey(robotId, commandId, channelId));
    }

    @Override
    public void addMessage(MessageCache cache) {
        messageCache.asMap().compute(getKey(cache.getRobotId(), cache.getCommandId(), cache.getChannelId()), (key, itemCache) -> cache);
    }

    @Override
    public void removeMessage(MessageCache cache) {
        messageCache.asMap().compute(getKey(cache.getRobotId(), cache.getCommandId(), cache.getChannelId()), (key, itemCache) -> null);
    }

    @Override
    public void updateAckTime(String messageId, Date ackTime) {
        messageCache.asMap().compute(messageId, (key, itemCache) -> {
            if (itemCache != null) {
                itemCache.setAckTime(ackTime);
            }
            return itemCache;
        });
    }

    @Override
    public void updateResultTime(String messageId, Date resultTime) {
        messageCache.asMap().compute(messageId, (key, itemCache) -> {
            if (itemCache != null) {
                itemCache.setAckTime(resultTime);
            }
            return itemCache;
        });
    }


    /**
     * 缓存关键key
     *
     * @param robotId   设备
     * @param commandId 消息id
     * @param channelId 通道id
     * @return "rId" + robotId + "cmId" + commandId + "chId" + channelId
     */
    private String getKey(Long robotId, Long commandId, String channelId) {
        return "rId" + robotId + "cmId" + commandId + "chId" + channelId;
    }

    /**
     * 超时三秒 淘汰消息
     *
     * @param key   机器人id+commandId
     * @param value 淘汰内容
     * @param cause 状态
     */
    private void removal(String key, MessageCache value, RemovalCause cause) {
        if (cause == RemovalCause.EXPIRED) {
            log.error("消息超时未处理, 消息被淘汰");
        }
    }
}
