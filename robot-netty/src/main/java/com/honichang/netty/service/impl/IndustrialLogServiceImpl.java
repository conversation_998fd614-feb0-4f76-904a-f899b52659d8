package com.honichang.netty.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.honichang.netty.domain.IndustrialLog;
import com.honichang.netty.domain.ResponseMessage;
import com.honichang.netty.domain.RobotConnectionLog;
import com.honichang.netty.enums.RobotLogEnum;
import com.honichang.netty.enums.ServiceLogEnum;
import com.honichang.netty.service.IndustrialLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class IndustrialLogServiceImpl implements IndustrialLogService {

    @Async("nettyLogExecutor")
    @Override
    public void saveServiceLog(IndustrialLog industrialLog) {
        log.info("【工控接口日志】--{}: {}", ServiceLogEnum.getStatusByDescription(industrialLog.getType()), JSONObject.toJSONString(industrialLog));
    }

    @Async("nettyLogExecutor")
    @Override
    public void saveServiceLog(ResponseMessage responseMessage) {
        IndustrialLog industrialLog = IndustrialLog.builder()
                .robotId(responseMessage.getRobotId())
                .commandId(responseMessage.getCommandId())
                .commandName(responseMessage.getCommandName()).time(new Date()).type(ServiceLogEnum.IndustrialPush.getStatus()).message(responseMessage.getMessage()).build();
        log.info("【工控接口日志】--{}: {}", ServiceLogEnum.getStatusByDescription(industrialLog.getType()), JSONObject.toJSONString(industrialLog));
    }

    @Async("nettyLogExecutor")
    @Override
    public void saveRobotLog(RobotConnectionLog robotConnectionLog) {
        log.info("【机器人日志】--{}: {}", RobotLogEnum.getStatusByDescription(robotConnectionLog.getType()), JSONObject.toJSONString(robotConnectionLog));
    }
}
