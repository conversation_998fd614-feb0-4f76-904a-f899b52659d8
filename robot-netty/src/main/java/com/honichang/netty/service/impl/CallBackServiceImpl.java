package com.honichang.netty.service.impl;

import com.honichang.netty.domain.IndustrialLog;
import com.honichang.netty.domain.MessageCache;
import com.honichang.netty.domain.RequestMessage;
import com.honichang.netty.domain.ResponseMessage;
import com.honichang.netty.enums.CommandEnum;
import com.honichang.netty.enums.UploadEnum;
import com.honichang.netty.service.CallBackService;
import com.honichang.netty.service.ClientCacheService;
import com.honichang.netty.service.IndustrialLogService;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 工控回调函数处理
 */
@Service
public class CallBackServiceImpl implements CallBackService {
    @Resource
    private IndustrialLogService iLogService;

    @Resource
    private ClientCacheService clientCacheService;

    /**
     * 首次接收请求
     *
     * @param res
     */
    @Async
    @Override
    public void instructionCallBack(ResponseMessage res, ChannelHandlerContext ctx) {
        //缓存消息
        MessageCache messageCache = new MessageCache(res.getRobotId(), res.getCommandId(), ctx.channel().id().toString(), res.getCommandName(), res.getParams(), null);
        clientCacheService.addMessage(messageCache);
        //日志上传
        if (res.getCommandName().equals(UploadEnum.LogUpload.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
        //固件更新成功通知
        if (res.getCommandName().equals(UploadEnum.FirmwareUpdateStatus.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
        //硬件报警
        if (res.getCommandName().equals(UploadEnum.HardwareWarning.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
        //实时数据
        if (res.getCommandName().equals(UploadEnum.RealTimeData.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
        //任务点位结果推送上传
        if (res.getCommandName().equals(UploadEnum.TaskUploadResults.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }

        //获取定位卡与基站之间的距离
        if (res.getCommandName().equals(UploadEnum.GetBaseDistance.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
        //UWB卡签到
        if (res.getCommandName().equals(UploadEnum.UwbCardIn.getInstruction())) {
            if (res.getAckId() > 0) {
                sendAckMessage(res, ctx);
            }
            if (res.getResult()) {
                //返回结果
            }
        }
    }

    /***
     * 结果发送
     * @param res
     */
    private void sendAckMessage(ResponseMessage res, ChannelHandlerContext ctx) {
        RequestMessage requestMessage = new RequestMessage(res.getCommandId(), res.getAckId(), CommandEnum.ACK.getCode(), res.getResult(), res.getParams());
        ctx.writeAndFlush(requestMessage.getMessage());
        iLogService.saveServiceLog(IndustrialLog.builder()
                .robotId(res.getRobotId())
                .commandId(res.getCommandId())
                .commandName(res.getCommandName()).time(new Date()).type(3).message(res.getMessage()).build());
    }
}
