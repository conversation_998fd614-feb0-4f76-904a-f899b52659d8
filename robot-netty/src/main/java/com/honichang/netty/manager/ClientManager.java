package com.honichang.netty.manager;

import com.honichang.netty.confg.EventLoopProperties;
import com.honichang.netty.handler.MessageDecoder;
import com.honichang.netty.handler.MessageHandler;
import com.honichang.netty.handler.HeartbeatHandler;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * netty客户端管理类
 */
@Component
@Slf4j
public class ClientManager {
    private final Bootstrap bootstrap;
    /***线程管理调度***/
    private final EventLoopGroup group;
    /***客户端缓存***/
    private final Map<String, Channel> channelMap;

    public ClientManager(EventLoopProperties eventLoopProperties) {
        this.bootstrap =  new Bootstrap();
        this.channelMap  = new ConcurrentHashMap<>();
        this.group = new NioEventLoopGroup(eventLoopProperties.getClientGroupThreads(),new DefaultThreadFactory(eventLoopProperties.getThreadNamePrefix(), true));
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, eventLoopProperties.getTaskTimeoutMs())
                .handler(new io.netty.channel.ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        // 添加对byte数组的编解码，netty提供了很多编解码器，你们可以根据需要选择
                        ch.pipeline().addLast(new MessageDecoder());
                        ch.pipeline().addLast(new ByteArrayEncoder());
                        ch.pipeline().addLast(new MessageHandler());
                        ch.pipeline().addLast(new HeartbeatHandler());
                    }
                });
    }

    /**
     * 连接设备
     * @param robtId 设备id
     * @param host ip
     * @param port 端口
     */
    public Channel connect(String robtId, String host, int port) throws InterruptedException {
        if (channelMap.containsKey(robtId)) {
            return channelMap.get(robtId);
        }
        ChannelFuture future = bootstrap.connect(host, port).sync();  // 阻塞，直到连接完成
        if (future.isSuccess()) {
            channelMap.put(robtId, future.channel());
            log.info("设备连接成功！");
        } else {
            log.error("设备连接失败！");
        }
        return future.channel();
    }
}
