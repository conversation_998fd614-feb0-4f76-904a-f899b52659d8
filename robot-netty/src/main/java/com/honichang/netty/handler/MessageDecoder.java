package com.honichang.netty.handler;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 解包
 */
@Slf4j
public class MessageDecoder extends ByteToMessageDecoder {

    /***接口识别关键字****/
    private final String KeyWord="{\"jsonSize\":";

    @Override
    protected void decode(ChannelHandlerContext channelHandlerContext, ByteBuf byteBuf, List<Object> list) throws Exception {
       int readLength = byteBuf.readableBytes();
        //不满足最小读取数量 跳过
        if (readLength < 10) {
            return ;
        }
        ByteBuf cp = byteBuf.duplicate();
        byte[] bytes = new byte[cp.readableBytes()];
        cp.readBytes(bytes);
        String message = new String(bytes, StandardCharsets.UTF_8);
        Integer jsonSize = getJsonSize(message);

        int start = this.getKeyWordsByteIndex(message).intValue();
        if (start>0){
            byteBuf.skipBytes(start);
        }
        if (jsonSize!=null&&jsonSize<=readLength){
            //消费原始字节
            byte[] byteArray = new byte[jsonSize];
            byteBuf.readBytes(byteArray);
            message = new String(byteArray, StandardCharsets.UTF_8);
            list.add(message);
        }

    }
    /**
     * 获取包体长度
     * @param message 消息数据
     * @return
     */
    private Integer getJsonSize(String message){
        // 创建Pattern对象
        Pattern pattern = Pattern.compile("\\{\"jsonSize\":(\\d+),");
        // 创建matcher对象
        Matcher matcher = pattern.matcher(message);
        if (matcher.find()) {
            System.out.println(matcher.group(1));
            if (!matcher.group(1).isEmpty()){
                return Integer.parseInt(matcher.group(1));
            }
        }
        return null;
    }

    /**
     * 获取关键字在字节中首次出现位置
     * @param message 消息体内容
     * @return 关键字在字节数组中的位置，若未找到则返回 null
     */
    private Integer getKeyWordsByteIndex(String message) {
        // 将字符串转换为字节数组
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        byte[] targetBytes = KeyWord.getBytes(StandardCharsets.UTF_8);
        // 遍历字节数组，找到目标子字符串的字节位置
        for (int i = 0; i <= messageBytes.length - targetBytes.length; i++) {
            // 检查目标字节数组是否与当前字节数组匹配
            if (Arrays.equals(Arrays.copyOfRange(messageBytes, i, i + targetBytes.length), targetBytes)) {
                return i;
            }
        }
        return null;
    }

}
