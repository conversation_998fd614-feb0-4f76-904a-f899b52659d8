package com.honichang.netty.handler;


import com.honichang.netty.domain.MessageCache;
import com.honichang.netty.domain.ResponseMessage;
import com.honichang.netty.enums.CommandEnum;
import com.honichang.netty.enums.IndustrialEnum;
import com.honichang.netty.service.IndustrialLogService;
import com.honichang.netty.service.ServerCacheService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * 服务端消息处理器
 * 处理入站数据的适配器类，用于处理客户端的行为
 */
@Slf4j
public class ServerHandler extends ChannelInboundHandlerAdapter {

    private final ServerCacheService serverCacheService;

    private final IndustrialLogService iLogService;

    public ServerHandler(ServerCacheService serverCacheService, IndustrialLogService iLogService) {
        this.serverCacheService = serverCacheService;
        this.iLogService = iLogService;
    }

    /**
     * 读取到客户端发来的消息
     *
     * @param ctx ChannelHandlerContext
     * @param msg msg
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        //拦截
        ResponseMessage responseMessage = (ResponseMessage) msg;
        iLogService.saveServiceLog(responseMessage);
        MessageCache messageCache = serverCacheService.getMessage(responseMessage.getRobotId(), responseMessage.getCommandId(), ctx.channel().id().toString());
        //服务端主动下发的消息回调
        if (messageCache != null) {
            if (responseMessage.getCommandName().equals(CommandEnum.ACK.getCode())) {
                serverCacheService.updateAckTime(messageCache.getMessageId(), responseMessage.getCreateTime());
                if (messageCache.getCommandName().equals(IndustrialEnum.FirmwareUpdate.getInstruction()) || messageCache.getCommandName().equals(IndustrialEnum.MusicPlay.getInstruction())) {
                    messageCache.getResponseFuture().complete(responseMessage);
                }
            }
            if (responseMessage.getCommandName().equals(CommandEnum.RESULT.getCode())) {
                serverCacheService.updateResultTime(messageCache.getMessageId(), responseMessage.getCreateTime());
                if (messageCache.getResponseFuture() != null) {
                    messageCache.getResponseFuture().complete(responseMessage);
                }
            }
        }
    }
}
