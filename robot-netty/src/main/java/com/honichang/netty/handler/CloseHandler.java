package com.honichang.netty.handler;

import com.honichang.dispactch.service.DispatchCallbackService;
import com.honichang.netty.domain.RobotConnectionLog;
import com.honichang.netty.enums.ConnectionEnum;
import com.honichang.netty.enums.RobotLogEnum;
import com.honichang.netty.service.IndustrialLogService;
import com.honichang.netty.service.RobotCacheService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.AttributeKey;

import java.util.Date;

public class CloseHandler  extends ChannelInboundHandlerAdapter {

    private final RobotCacheService robotCacheService;

    private final IndustrialLogService iLogService;

    private final DispatchCallbackService dcbService;


    public CloseHandler(RobotCacheService robotCacheService, IndustrialLogService iLogService, DispatchCallbackService dcbService) {
        this.robotCacheService = robotCacheService;
        this.iLogService = iLogService;
        this.dcbService = dcbService;
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) {
        Long robotId = (Long) ctx.channel().attr(AttributeKey.valueOf("robotId")).get();
        if (robotId != null) {
            robotCacheService.updateRobotInformationStatus(robotId, ConnectionEnum.FAILED.getInstruction());
            iLogService.saveRobotLog(RobotConnectionLog.builder().robotId(robotId).createTime(new Date()).type(RobotLogEnum.Disconnect.getStatus()).message("设备断开连接").build());
            dcbService.setRobotConnectionStatus(robotId, false);
        }
    }
}
