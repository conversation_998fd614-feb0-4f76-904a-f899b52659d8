package com.honichang.netty.handler;


import com.honichang.netty.domain.MessageCache;
import com.honichang.netty.domain.ResponseMessage;
import com.honichang.netty.enums.UploadEnum;
import com.honichang.netty.service.CallBackService;
import com.honichang.netty.service.ClientCacheService;
import com.honichang.netty.service.IndustrialLogService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * 客户端消息处理器
 * 处理入站数据的适配器类，用于处理客户端的行为
 */
@Slf4j
public class ClientHandler extends ChannelInboundHandlerAdapter {


    private final IndustrialLogService iLogService;

    private final ClientCacheService clientCacheService;
    private final CallBackService callBackService;

    public ClientHandler(IndustrialLogService iLogService, CallBackService callBackService, ClientCacheService clientCacheService) {

        this.iLogService = iLogService;
        this.callBackService = callBackService;
        this.clientCacheService = clientCacheService;
    }

    /**
     * 读取到客户端发来的消息
     *
     * @param ctx ChannelHandlerContext
     * @param msg msg
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        //拦截
        ResponseMessage res = (ResponseMessage) msg;
        UploadEnum em = Arrays.stream(UploadEnum.values()).filter(uploadEnum -> uploadEnum.getInstruction().equals(res.getCommandName())).findFirst().orElse(null);
        if (em != null) {
            iLogService.saveServiceLog(res);
            callBackService.instructionCallBack(res, ctx);
        }

        MessageCache mc = clientCacheService.getMessage(res.getRobotId(), res.getCommandId(), ctx.channel().id().toString());
        if (mc != null) {
            iLogService.saveServiceLog(res);
            callBackService.instructionCallBack(res, ctx);
        }

    }
}
