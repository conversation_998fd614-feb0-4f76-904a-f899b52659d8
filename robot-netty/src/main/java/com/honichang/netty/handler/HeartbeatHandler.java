package com.honichang.netty.handler;

import com.honichang.netty.domain.IndustrialLog;
import com.honichang.netty.domain.RequestMessage;
import com.honichang.netty.enums.IndustrialEnum;
import com.honichang.netty.service.IndustrialControlService;
import com.honichang.netty.service.IndustrialLogService;
import com.honichang.netty.service.RobotCacheService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 心跳处理器
 */
@Slf4j
public class HeartbeatHandler extends ChannelInboundHandlerAdapter {
//    private static final int HEARTBEAT_INTERVAL = 2; // 2秒发送一次
//
//    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
//    private final RobotCacheService robotCacheService;
//    private final IndustrialControlService industrialControlService;
//    private final IndustrialLogService industrialLogService;
//
//    public HeartbeatHandler(RobotCacheService robotCacheService, IndustrialControlService industrialControlService, IndustrialLogService industrialLogService) {
//        this.robotCacheService = robotCacheService;
//        this.industrialControlService = industrialControlService;
//        this.industrialLogService = industrialLogService;
//    }
//
//    @Override
//    public void channelActive(ChannelHandlerContext ctx) throws Exception {
//        super.channelActive(ctx);
//        // 定时发送心跳
//        executorService.scheduleAtFixedRate(() -> {
//            Long rebotId = (Long) ctx.channel().attr(AttributeKey.valueOf("robotId")).get();
//            Long commId = robotCacheService.getCommandId(rebotId);
//            RequestMessage requestMessage = new RequestMessage(commId, rebotId, IndustrialEnum.GetRobotInformation.getInstruction(), true, "");
//            industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(rebotId).commandId(commId).commandName(IndustrialEnum.GetRobotStatus.getInstruction()).time(new Date()).message(requestMessage.getMessage()).type(1).build());
//            try {
//                ctx.writeAndFlush(requestMessage.getMessage().getBytes());
//            } catch (Exception e) {
//                industrialLogService.saveServiceLog(IndustrialLog.builder().robotId(rebotId).commandId(commId).commandName(IndustrialEnum.GetRobotStatus.getInstruction()).time(new Date()).message("消息发送异常:" + e.getMessage()).type(2).build());
//            }
//        }, 0, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
//
//    }
}
