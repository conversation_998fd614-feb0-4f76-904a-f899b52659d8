package com.honichang.netty.core;


import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class MessageTemplate {
    private final ClientManager clientManager;
    /**缓存通道信息**/
    private final Map<String,Channel> channelMap = new ConcurrentHashMap<>();

    public MessageTemplate(ClientManager clientManager) {
        this.clientManager = clientManager;
//        try {
//            this.connect("123123","127.0.0.1",56050);
//        }catch (Exception e){
//            e.printStackTrace();
//        }

    }

    /**
     * 连接设备
     * @param robtId 机器人id
     * @param host ip
     * @param port 端口
     * @return true成功 false失败
     */
    public boolean connect(String robtId, String host, int port) throws InterruptedException {
        boolean status= false;
        if (channelMap.containsKey(robtId)){
            this.close(robtId);
            log.info("机器人,已有连接,关闭当前连接,并重新发起！ robtId:{},host:{},port:{}",robtId,host,port);
        }
        ChannelFuture future = clientManager.getBootstrap().connect(host, port).sync();  // 阻塞，直到连接完成
        if (future.isSuccess()) {
            status = true;
            channelMap.put(robtId, future.channel());
            log.info("机器人,连接成功！ robtId:{},host:{},port:{}",robtId,host,port);
        } else {
            log.info("机器人,连接失败！ robtId:{},host:{},port:{}",robtId,host,port);
        }
        return status;
    }

    /**
     * 关闭连接设备
     * @param robtId 机器人id
     */
    public void close(String robtId){
        channelMap.get(robtId).close();
        channelMap.remove(robtId);
    }
}
