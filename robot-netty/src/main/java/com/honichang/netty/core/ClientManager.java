package com.honichang.netty.core;

import com.honichang.netty.confg.EventLoopProperties;
import com.honichang.netty.handler.HeartbeatHandler;
import com.honichang.netty.handler.MessageDecoder;
import com.honichang.netty.handler.MessageHandler;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * netty客户端管理类
 */
@Component
@Slf4j
@Data
public class ClientManager {
    /***netty引导***/
    private final Bootstrap bootstrap = new Bootstrap();
    /***线程管理调度***/
    private final EventLoopGroup group;

    public ClientManager(EventLoopProperties eventLoopProperties) {
        this.group = new NioEventLoopGroup(eventLoopProperties.getClientGroupThreads(),new DefaultThreadFactory(eventLoopProperties.getThreadNamePrefix(), true));
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, eventLoopProperties.getTaskTimeoutMs())
                .handler(new io.netty.channel.ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        // 添加对byte数组的编解码，netty提供了很多编解码器，你们可以根据需要选择
                        ch.pipeline().addLast(new MessageDecoder());
                        ch.pipeline().addLast(new ByteArrayEncoder());
                        ch.pipeline().addLast(new MessageHandler());
                        ch.pipeline().addLast(new HeartbeatHandler());
                    }
                });
    }
}
