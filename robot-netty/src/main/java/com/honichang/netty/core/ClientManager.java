package com.honichang.netty.core;

import com.honichang.dispactch.service.DispatchCallbackService;
import com.honichang.netty.confg.EventLoopProperties;
import com.honichang.netty.handler.*;
import com.honichang.netty.service.*;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * netty客户端管理类
 */
@Component
@Slf4j
@Data
public class ClientManager {
    /***netty引导***/
    private final Bootstrap bootstrap = new Bootstrap();
    /***线程管理调度***/
    private final EventLoopGroup group;

    public ClientManager(EventLoopProperties eventLoopProperties, RobotCacheService robotCacheService,
                         ServerCacheService serverCacheService, IndustrialLogService industrialLogService,
                         DispatchCallbackService dispatchCallbackService,
                         CallBackService callBackService, ClientCacheService clientCacheService) {
        this.group = new NioEventLoopGroup(eventLoopProperties.getClientGroupThreads(), new DefaultThreadFactory(eventLoopProperties.getThreadNamePrefix(), true));
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, eventLoopProperties.getTaskTimeoutMs())
                .handler(new io.netty.channel.ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        // 添加对byte数组的编解码，netty提供了很多编解码器，你们可以根据需要选择
                        ch.pipeline().addLast(new MessageDecoder());
                        ch.pipeline().addLast(new MessageEncoder());
                        ch.pipeline().addLast(new ServerHandler(serverCacheService, industrialLogService));
                        ch.pipeline().addLast(new ClientHandler( industrialLogService, callBackService,clientCacheService));
                        ch.pipeline().addLast(new CloseHandler(robotCacheService, industrialLogService, dispatchCallbackService));
                    }
                });
    }
}
