package com.honichang.netty.controller;

import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.netty.domain.IndustrialControlResult;
import com.honichang.netty.enums.IndustrialEnum;
import com.honichang.netty.service.IndustrialControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/industrial/test")
public class IndustrialTestController extends BaseController {

    @Autowired
    private IndustrialControlService industrialControlService;

    @GetMapping("/getServiceList")
    public AjaxResult getServiceList() {
        List<Map<String, String>> list = new ArrayList<>();
        for (IndustrialEnum item : IndustrialEnum.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("key", item.getInstruction());
            map.put("value", item.getDescription());
            list.add(map);
        }
        return AjaxResult.success(list);
    }

    @PostMapping("/sendMessage")
    public AjaxResult sendMessage(@RequestBody IndustrialTest t) {
        IndustrialControlResult industrialControlResult = null;

        if (t.getService().equals(IndustrialEnum.MusicPlay.getInstruction())) {//播放音乐

            industrialControlResult = industrialControlService.musicPlay(t.getRobotId(), t.getContent());
        }

        if (t.getService().equals(IndustrialEnum.MusicPause.getInstruction())) {//停止播放
            industrialControlResult = industrialControlService.musicPause(t.getRobotId(), Long.parseLong(t.getContent()));
        }

        if (t.getService().equals(IndustrialEnum.RestartRobot.getInstruction())) {//重启机器人
            industrialControlResult = industrialControlService.restartRobot(t.getRobotId(), t.getContent());
        }
        if (t.getService().equals(IndustrialEnum.TaskDistribution.getInstruction())) {//批量任务下发
            industrialControlResult = industrialControlService.taskDistribution(t.getRobotId(), t.getContent());
        }
        if (t.getService().equals(IndustrialEnum.CacelTask.getInstruction())) {//取消当前导航任务
            industrialControlResult = industrialControlService.cacelTask(t.getRobotId(), t.getContent());
        }
        if (t.getService().equals(IndustrialEnum.GetRobotStatus.getInstruction())) {
            industrialControlResult = industrialControlService.getRobotStatus(t.getRobotId(), t.getContent());
        }
        if (t.getService().equals(IndustrialEnum.GetRobotVariable.getInstruction())) {
            industrialControlResult = industrialControlService.getRobotVariable(t.getRobotId(), t.getContent());
        }
        if (t.getService().equals(IndustrialEnum.SetRobotVariable.getInstruction())) {

        }
        if (t.getService().equals(IndustrialEnum.SetRobotMode.getInstruction())) {

        }
        if (t.getService().equals(IndustrialEnum.SetRobotPosition.getInstruction())) {

        }

        if (t.getService().equals(IndustrialEnum.ConfirmRobotPosition.getInstruction())) {

        }

        if (t.getService().equals(IndustrialEnum.SetOccupiedPath.getInstruction())) {

        }
        if (t.getService().equals(IndustrialEnum.StartNavigationDirection.getInstruction())) {

        }
        return AjaxResult.success(industrialControlResult);
    }
}
