package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysDept;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.DictUtils;
import com.honichang.machine.utils.PdfPageUtil;
import com.honichang.point.domain.*;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.service.*;
import com.honichang.system.service.ISysDeptService;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/taskInstance")
public class TaskInstanceController extends BaseController {

    @Resource
    private TaskInstanceService taskInstanceService;
    @Resource
    private TaskDefService taskDefService;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;
    @Resource
    private MediaFileService mediaFileService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody TaskInstance taskInstance) {
        taskInstance.setCreateBy(getUsername());
        taskInstance.setCreateTime(new Date());
        return toAjax(taskInstanceService.save(taskInstance));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(taskInstanceService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody TaskInstance taskInstance) {
        taskInstance.setUpdateBy(getUsername());
        taskInstance.setUpdateTime(new Date());
        return toAjax(taskInstanceService.updateById(taskInstance));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(TaskInstance taskInstance) {
        startPage();
        LambdaQueryWrapper<TaskInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskInstance::getDelFlag, "0");
        List<TaskInstance> list = taskInstanceService.list(wrapper);
        for (TaskInstance instance : list) {
            TaskDef taskDef = taskDefService.getById(instance.getTaskId());
            instance.setTaskName(taskDef.getTaskName());
            SysDept dept = deptService.selectDeptById(taskDef.getDeptId());
            instance.setDeptName(dept.getDeptName());

            if (instance.getFinishTime() != null) {
                String timeDistance = DateUtils.timeDistance(instance.getFinishTime(), instance.getStartTime());
                instance.setDuration(timeDistance);
            }
        }
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(TaskInstance taskInstance) {
        LambdaQueryWrapper<TaskInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskInstance::getDelFlag, "0");
        return AjaxResult.success(taskInstanceService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(taskInstanceService.getById(id));
    }

    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRobot logRobot) {

    }

    @RequestMapping("/auditData/{ids}")
    public AjaxResult auditData(@PathVariable Integer[] ids) {
        LambdaQueryWrapper<TaskInstanceNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TaskInstanceNode::getTaskInstanceId, ids);
        List<TaskInstanceNode> nodeList = taskInstanceNodeService.list(queryWrapper);
        for (TaskInstanceNode taskInstanceNode : nodeList) {
            LambdaQueryWrapper<MediaFile> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(MediaFile::getBizType, "task_instance_node");
            wrapper1.eq(MediaFile::getBizId, taskInstanceNode.getId());
            List<MediaFile> mediaFileList = mediaFileService.list(wrapper1);
            if (mediaFileList.size() > 0) {
                taskInstanceNode.setImgUrl(mediaFileList.get(0).getThumbnailUrl());
            }

            LambdaQueryWrapper<TaskInstanceNodeResult> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, taskInstanceNode.getId());
            List<TaskInstanceNodeResult> nodeResults = taskInstanceNodeResultService.list(wrapper);

            taskInstanceNode.setTaskInstanceNodeResultList(nodeResults);


        }

        return AjaxResult.success(nodeList);
    }

    @RequestMapping("/auditDataEdit")
    public AjaxResult auditDataEdit(@RequestBody List<TaskInstanceNode> taskInstanceNodeList) {

        return toAjax(taskInstanceService.auditDataEdit(taskInstanceNodeList));
    }

    @Resource
    private AlarmAiService alarmAiService;

    @RequestMapping("/getViewTheReport/{id}")
    public AjaxResult getViewTheReport(@PathVariable Integer id) {
        TaskInstance taskInstance = taskInstanceService.getById(id);
        TaskDef taskDef = taskDefService.getById(taskInstance.getTaskId());
        taskInstance.setTaskName(taskDef.getTaskName());
        SysDept dept = deptService.selectDeptById(taskDef.getDeptId());
        taskInstance.setDeptName(dept.getDeptName());

        LambdaQueryWrapper<AlarmAi> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AlarmAi::getTaskInstanceId, id);
        List<AlarmAi> alarmAiList = alarmAiService.list(wrapper);
        for (AlarmAi alarmAi : alarmAiList) {
            LambdaQueryWrapper<MediaFile> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(MediaFile::getBizType, "alarm_ai");
            wrapper1.eq(MediaFile::getBizId, alarmAi.getId());
            List<MediaFile> mediaFileList = mediaFileService.list(wrapper1);
            alarmAi.setImgUrl(mediaFileList.get(0).getThumbnailUrl());
        }
        taskInstance.setAlarmAiList(alarmAiList);

        LambdaQueryWrapper<TaskInstanceNode> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(TaskInstanceNode::getTaskInstanceId, id);
        List<TaskInstanceNode> nodeList = taskInstanceNodeService.list(wrapper2);
        for (TaskInstanceNode taskInstanceNode : nodeList) {
            LambdaQueryWrapper<MediaFile> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(MediaFile::getBizType, "task_instance_node");
            wrapper1.eq(MediaFile::getBizId, taskInstanceNode.getId());
            List<MediaFile> mediaFileList = mediaFileService.list(wrapper1);
            if (mediaFileList.size() > 0) {
                taskInstanceNode.setImgUrl(mediaFileList.get(0).getThumbnailUrl());
            }

            LambdaQueryWrapper<TaskInstanceNodeResult> taskInstanceNodeResult = new LambdaQueryWrapper<>();
            taskInstanceNodeResult.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, taskInstanceNode.getId());
            List<TaskInstanceNodeResult> nodeResultList = taskInstanceNodeResultService.list(taskInstanceNodeResult);
            taskInstanceNode.setTaskInstanceNodeResultList(nodeResultList);

        }
        taskInstance.setTaskInstanceNodeList(nodeList);

        return AjaxResult.success(taskInstance);
    }

    @PostMapping("/exportPdf")
    public void exportPdf(HttpServletResponse response, @RequestBody TaskInstance taskInstance) {

        try {
            // 设置编码格式
            response.setContentType("application/pdf");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("1", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".pdf");

            AjaxResult viewTheReport = getViewTheReport(Math.toIntExact(taskInstance.getId()));
            TaskInstance data = (TaskInstance) viewTheReport.get("data");
            pdfOut(response, data);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

    }

    private void pdfOut(HttpServletResponse response, TaskInstance taskInstance) {

        try {
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font titlefont = new Font(bfChinese, 16, Font.BOLD);
            Font headfont = new Font(bfChinese, 14, Font.BOLD);
            Font keyfont = new Font(bfChinese, 10, Font.BOLD);
            Font textfont = new Font(bfChinese, 15, Font.NORMAL);
            Font content = new Font(bfChinese, 10, Font.NORMAL);

            //创建字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            //使用字体并给出颜色
            Font font = new Font(bf, 20, Font.BOLD, BaseColor.BLACK);


            Document document = new Document(new RectangleReadOnly(842F, 595F));
            //设置页边距
            document.setMargins(30, 30, 30, 30);

//            FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\1.pdf");
//            PdfWriter writer = PdfWriter.getInstance(document, fileOutputStream);
            PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());

            //添加页码
            writer.setPageEvent(new PdfPageUtil());
            //打开生成的pdf文件
            document.open();

            // 设置表格的列宽和列数
            PdfPTable table = new PdfPTable(4);
            table.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table.setWidthPercentage(100.0F);
            table.setHeaderRows(1);
            table.getDefaultCell().setHorizontalAlignment(1);
            PdfPCell cell = null;
            //第一行
            cell = new PdfPCell(new Paragraph("任务名称", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(taskInstance.getTaskName(), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph("任务状态", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(DictUtils.getDictLabel("task_instance_status", taskInstance.getStatus()), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph("所属部门", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(taskInstance.getDeptName(), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setColspan(3);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph("开始时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInstance.getStartTime()), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph("结束时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInstance.getFinishTime()), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph("环境信息", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table.addCell(cell);

            cell = new PdfPCell(new Paragraph(taskInstance.getEnvReport(), content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setColspan(3);
            table.addCell(cell);


            // 设置表格的列宽和列数
            PdfPTable table1 = new PdfPTable(6);
            table1.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table1.setWidthPercentage(100.0F);
            table1.setHeaderRows(1);
            table1.getDefaultCell().setHorizontalAlignment(1);

            cell = new PdfPCell(new Paragraph("已巡检", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getPointAlarmNum() + taskInstance.getPointHealthNum() + taskInstance.getAbnormalNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph("正常点位", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getPointHealthNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph("告警点位", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getPointAlarmNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph("不可达", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(30);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getUnreachableNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph("未识别", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getAbnormalNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph("智能报警", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            cell = new PdfPCell(new Paragraph((taskInstance.getAiAlarmNum()) + "", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table1.addCell(cell);

            // 设置表格的列宽和列数
            PdfPTable table2 = new PdfPTable(8);
            table2.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table2.setWidthPercentage(100.0F);
            table2.setHeaderRows(1);
            table2.getDefaultCell().setHorizontalAlignment(1);

            cell = new PdfPCell(new Paragraph("序号", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(20);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("报警名称", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别类型", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别结果", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("报警等级", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("报警时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("备注", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            cell = new PdfPCell(new Paragraph("现场照片", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table2.addCell(cell);

            for (int i = 0; i < taskInstance.getAlarmAiList().size(); i++) {

                PdfPCell cell1 = new PdfPCell(new Paragraph((i + 1) + "", content));
                PdfPCell cell2 = new PdfPCell(new Paragraph(taskInstance.getAlarmAiList().get(i).getAlarmName(), content));
                PdfPCell cell3 = new PdfPCell(new Paragraph(DictUtils.getDictLabel("iden_type", taskInstance.getAlarmAiList().get(i).getIdenType() + ""), content));
                PdfPCell cell4 = new PdfPCell(new Paragraph(taskInstance.getAlarmAiList().get(i).getIdenResult(), content));
                PdfPCell cell5 = new PdfPCell(new Paragraph(taskInstance.getAlarmAiList().get(i).getAlarmLevel(), content));
                PdfPCell cell6 = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInstance.getAlarmAiList().get(i).getTriggerTime()), content));
                PdfPCell cell7 = new PdfPCell(new Paragraph(taskInstance.getAlarmAiList().get(i).getRemark(), content));
                Image image = Image.getInstance(RuoYiConfig.getProfile() + taskInstance.getAlarmAiList().get(i).getImgUrl());
                image.scaleToFit(50, 50);
                PdfPCell cell8 = new PdfPCell(image);

                //单元格对齐方式
                cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell1.setFixedHeight(20);
                //单元格垂直对齐方式
                cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell2.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell3.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell6.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell6.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell7.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell8.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell8.setVerticalAlignment(Element.ALIGN_MIDDLE);

                table2.addCell(cell1);
                table2.addCell(cell2);
                table2.addCell(cell3);
                table2.addCell(cell4);
                table2.addCell(cell5);
                table2.addCell(cell6);
                table2.addCell(cell7);
                table2.addCell(cell8);
            }


            PdfPTable table3 = new PdfPTable(7);
            table3.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table3.setWidthPercentage(100.0F);
            table3.setHeaderRows(1);
            table3.getDefaultCell().setHorizontalAlignment(1);

            cell = new PdfPCell(new Paragraph("序号", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(20);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别类型", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("点位名称", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别结果", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("报警等级", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("采集时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            cell = new PdfPCell(new Paragraph("现场照片", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table3.addCell(cell);

            //0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警
            List<TaskInstanceNode> alarmList = taskInstance.getTaskInstanceNodeList().stream().filter(f -> f.getStatus().equals("9")).collect(Collectors.toList());
            for (int i = 0; i < alarmList.size(); i++) {
                PdfPCell cell1 = new PdfPCell(new Paragraph((i + 1) + "", content));
                PdfPCell cell2 = new PdfPCell(new Paragraph(alarmList.get(i).getIdenModelName(), content));
                PdfPCell cell3 = new PdfPCell(new Paragraph(alarmList.get(i).getPointName(), content));
                List<TaskInstanceNodeResult> resultList = alarmList.get(i).getTaskInstanceNodeResultList();
                StringBuffer resultVal = new StringBuffer();
                for (TaskInstanceNodeResult result : resultList) {
                    resultVal.append(result.getIdenModelParamName());
                    resultVal.append("=");
                    if (result.getFloatValue() != null) {
                        resultVal.append(result.getFloatValue());
                    } else {
                        resultVal.append(result.getEnumValue());
                    }
                }
                PdfPCell cell4 = new PdfPCell(new Paragraph(resultVal.toString(), content));
                PdfPCell cell5 = new PdfPCell(new Paragraph(alarmList.get(i).getRuleResult() + " " + alarmList.get(i).getRuleExpressionText(), content));
                PdfPCell cell6 = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, alarmList.get(i).getIdenTime()), content));
                Image image = Image.getInstance(RuoYiConfig.getProfile() + alarmList.get(i).getImgUrl());
                image.scaleToFit(50, 50);
                PdfPCell cell7 = new PdfPCell(image);

                //单元格对齐方式
                cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell1.setFixedHeight(20);
                //单元格垂直对齐方式
                cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell2.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell3.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell6.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell6.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell7.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                table3.addCell(cell1);
                table3.addCell(cell2);
                table3.addCell(cell3);
                table3.addCell(cell4);
                table3.addCell(cell5);
                table3.addCell(cell6);
                table3.addCell(cell7);
            }

            PdfPTable table4 = new PdfPTable(7);
            table4.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table4.setWidthPercentage(100.0F);
            table4.setHeaderRows(1);
            table4.getDefaultCell().setHorizontalAlignment(1);

            cell = new PdfPCell(new Paragraph("序号", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(20);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别类型", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("点位名称", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("状态", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("采集时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别结果", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            cell = new PdfPCell(new Paragraph("现场照片", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table4.addCell(cell);

            //0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警
            List<TaskInstanceNode> abnormalList = taskInstance.getTaskInstanceNodeList().stream().filter(f -> f.getStatus().equals("7")).collect(Collectors.toList());
            for (int i = 0; i < abnormalList.size(); i++) {

                PdfPCell cell1 = new PdfPCell(new Paragraph((i + 1) + "", content));
                PdfPCell cell2 = new PdfPCell(new Paragraph(abnormalList.get(i).getIdenModelName(), content));
                PdfPCell cell3 = new PdfPCell(new Paragraph(abnormalList.get(i).getPointName(), content));
                PdfPCell cell4 = new PdfPCell(new Paragraph(abnormalList.get(i).getRuleResult() + " " + abnormalList.get(i).getRuleExpressionText(), content));
                PdfPCell cell5 = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, abnormalList.get(i).getIdenTime()), content));
                List<TaskInstanceNodeResult> resultList = abnormalList.get(i).getTaskInstanceNodeResultList();
                StringBuffer resultVal = new StringBuffer();
                for (TaskInstanceNodeResult result : resultList) {
                    resultVal.append(result.getIdenModelParamName());
                    resultVal.append("=");
                    if (result.getFloatValue() != null) {
                        resultVal.append(result.getFloatValue());
                    } else {
                        resultVal.append(result.getEnumValue());
                    }
                }
                PdfPCell cell6 = new PdfPCell(new Paragraph(resultVal.toString(), content));
                Image image = Image.getInstance(RuoYiConfig.getProfile() + abnormalList.get(i).getImgUrl());
                image.scaleToFit(50, 50);
                PdfPCell cell7 = new PdfPCell(image);

                //单元格对齐方式
                cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell1.setFixedHeight(20);
                //单元格垂直对齐方式
                cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell2.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell3.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell6.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell6.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell7.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                table4.addCell(cell1);
                table4.addCell(cell2);
                table4.addCell(cell3);
                table4.addCell(cell4);
                table4.addCell(cell5);
                table4.addCell(cell6);
                table4.addCell(cell7);
            }

            PdfPTable table5 = new PdfPTable(7);
            table5.setSpacingBefore(20f);
            // 设置表格宽度为100%
            table5.setWidthPercentage(100.0F);
            table5.setHeaderRows(1);
            table5.getDefaultCell().setHorizontalAlignment(1);

            cell = new PdfPCell(new Paragraph("序号", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setFixedHeight(20);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别类型", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("点位名称", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("识别结果", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("状态", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("采集时间", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            cell = new PdfPCell(new Paragraph("现场照片", content));
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            table5.addCell(cell);

            //0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警
            List<TaskInstanceNode> normallList = taskInstance.getTaskInstanceNodeList().stream().filter(f -> f.getStatus().equals("3")).collect(Collectors.toList());
            for (int i = 0; i < normallList.size(); i++) {

                PdfPCell cell1 = new PdfPCell(new Paragraph((i + 1) + "", content));
                PdfPCell cell2 = new PdfPCell(new Paragraph(normallList.get(i).getIdenModelName(), content));
                PdfPCell cell3 = new PdfPCell(new Paragraph(normallList.get(i).getPointName(), content));
                List<TaskInstanceNodeResult> resultList = normallList.get(i).getTaskInstanceNodeResultList();
                StringBuffer resultVal = new StringBuffer();
                for (TaskInstanceNodeResult result : resultList) {
                    resultVal.append(result.getIdenModelParamName());
                    resultVal.append("=");
                    if (result.getFloatValue() != null) {
                        resultVal.append(result.getFloatValue());
                    } else {
                        resultVal.append(result.getEnumValue());
                    }
                }
                PdfPCell cell4 = new PdfPCell(new Paragraph(resultVal.toString(), content));
                PdfPCell cell5 = new PdfPCell(new Paragraph(normallList.get(i).getRuleResult() + " " + normallList.get(i).getRuleExpressionText(), content));
                PdfPCell cell6 = new PdfPCell(new Paragraph(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, normallList.get(i).getIdenTime()), content));
                Image image = Image.getInstance(RuoYiConfig.getProfile() + normallList.get(i).getImgUrl());
                image.scaleToFit(50, 50);
                PdfPCell cell7 = new PdfPCell(image);

                //单元格对齐方式
                cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell1.setFixedHeight(20);
                //单元格垂直对齐方式
                cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell2.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell3.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell3.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell4.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell4.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell5.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell5.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell6.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell6.setVerticalAlignment(Element.ALIGN_MIDDLE);

                cell7.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell7.setVerticalAlignment(Element.ALIGN_MIDDLE);

                table5.addCell(cell1);
                table5.addCell(cell2);
                table5.addCell(cell3);
                table5.addCell(cell4);
                table5.addCell(cell5);
                table5.addCell(cell6);
                table5.addCell(cell7);
            }

            document.add(table);

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("▋ 巡检结果", content));
            document.add(table1);

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("▋ 智能报警", content));
            document.add(table2);

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("▋ 告警点位", content));
            document.add(table3);

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("▋ 异常识别", content));
            document.add(table4);

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("▋ 正常点位", content));
            document.add(table5);

            //关闭文档
            document.close();
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


    }

}
