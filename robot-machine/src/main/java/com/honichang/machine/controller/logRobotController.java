package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.poi.ExcelUtil;
import com.honichang.point.domain.LogRobot;
import com.honichang.point.domain.Robot;
import com.honichang.point.service.LogIndustrialControlService;
import com.honichang.point.service.LogRobotService;
import com.honichang.point.service.RobotService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/logRobot")
public class logRobotController extends BaseController {

    @Resource
    private LogRobotService logRobotService;
    @Resource
    private RobotService robotService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody LogRobot logRobot) {
        logRobot.setCreateBy(getUsername());
        logRobot.setCreateTime(new Date());
        return toAjax(logRobotService.save(logRobot));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(logRobotService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody LogRobot logRobot) {
        logRobot.setUpdateBy(getUsername());
        logRobot.setUpdateTime(new Date());
        return toAjax(logRobotService.updateById(logRobot));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(LogRobot logRobot) {
        startPage();
        LambdaQueryWrapper<LogRobot> wrapper = new LambdaQueryWrapper<>();
        if (logRobot.getContent() != null && !logRobot.getContent().equals("")) {
            wrapper.like(LogRobot::getContent, logRobot.getContent());
        }
        wrapper.eq(LogRobot::getDelFlag, "0");
        List<LogRobot> list = logRobotService.list(wrapper);
        for (LogRobot lr : list) {
            Robot robot = robotService.getById(lr.getRobotId());
            lr.setRobotName(robot.getRobotName());
        }
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(LogRobot logRobot) {
        LambdaQueryWrapper<LogRobot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogRobot::getDelFlag, "0");
        return AjaxResult.success(logRobotService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(logRobotService.getById(id));
    }

    @PostMapping("/export")
    public void export(HttpServletResponse response, LogRobot logRobot)
    {
        LambdaQueryWrapper<LogRobot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogRobot::getDelFlag, "0");
        List<LogRobot> list = logRobotService.list(wrapper);
        for (LogRobot lr : list) {
            Robot robot = robotService.getById(lr.getRobotId());
            lr.setRobotName(robot.getRobotName());
        }
        ExcelUtil<LogRobot> util = new ExcelUtil<LogRobot>(LogRobot.class);
        util.exportExcel(response, list, "机器人日志");
    }

}
