package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.point.domain.Robot;
import com.honichang.point.domain.RobotDynamicAttr;
import com.honichang.point.service.RobotDynamicAttrService;
import com.honichang.point.service.RobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/robot/view")
public class RobotViewController extends BaseController {

    @Autowired
    private RobotService robotService;

    @Autowired
    private RobotDynamicAttrService robotDynamicAttrService;



    @GetMapping("/list")
    public AjaxResult list() {
        List<Robot> list = robotService.getRobotList();
        return AjaxResult.success(list);
    }

    @GetMapping("/getRunStatus")
    public AjaxResult getRunStatus(RobotDynamicAttr robotDynamicAttr) {
        LambdaQueryWrapper<RobotDynamicAttr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobotDynamicAttr::getDelFlag, "0");
        queryWrapper.eq(RobotDynamicAttr::getRobotId, robotDynamicAttr.getRobotId());
        robotDynamicAttr = robotDynamicAttrService.getOne(queryWrapper);
        return AjaxResult.success(robotDynamicAttr);
    }
}
