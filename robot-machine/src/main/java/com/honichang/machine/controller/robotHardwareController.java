package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.RobotService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/robotHardware")
public class robotHardwareController extends BaseController {

    @Resource
    private RobotHardwareService robotHardwareService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody RobotHardware robotHardware) {
        robotHardware.setCreateBy(getUsername());
        robotHardware.setCreateTime(new Date());
        return toAjax(robotHardwareService.save(robotHardware));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(robotHardwareService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody RobotHardware robotHardware) {
        robotHardware.setUpdateBy(getUsername());
        robotHardware.setUpdateTime(new Date());
        return toAjax(robotHardwareService.updateById(robotHardware));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(RobotHardware robotHardware) {
        startPage();
        LambdaQueryWrapper<RobotHardware> wrapper = new LambdaQueryWrapper<>();
        if (robotHardware.getHardwareName() != null && !robotHardware.getHardwareName().equals("")) {
            wrapper.like(RobotHardware::getHardwareName, robotHardware.getHardwareName());
        }
        wrapper.eq(RobotHardware::getDelFlag, "0");
        List<RobotHardware> list = robotHardwareService.list(wrapper);
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(RobotHardware robotHardware) {
        LambdaQueryWrapper<RobotHardware> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RobotHardware::getDelFlag, "0");
        return AjaxResult.success(robotHardwareService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(robotHardwareService.getById(id));
    }

}
