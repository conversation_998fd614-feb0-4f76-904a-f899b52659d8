package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.utils.CompuUtils;
import com.honichang.common.utils.TimeConverter;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.point.domain.Robot;
import com.honichang.point.domain.RobotDynamicAttr;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.RobotDynamicAttrService;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.RobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/intelligentRobot/intelligentRobotView")
public class IntelligentRobotViewController extends BaseController {

    @Autowired
    private RobotService robotService;

    @Autowired
    private RobotDynamicAttrService robotDynamicAttrService;

    @Autowired
    private RobotHardwareService robotHardwareService;

    @GetMapping("/list")
    public AjaxResult list() {
        List<Robot> list = robotService.getRobotList();
        list.stream().forEach(robot -> {
            RobotContext robotContext = RobotContext.get(robot.getId());
            if(robotContext != null){

                robot.setSn(robotContext.getSn());
                robot.setTotalRunning(robotContext.getTotalRunning() != null && robotContext.getTotalRunning().intValue() > 0 ? TimeConverter.convertMinutes(robotContext.getTotalRunning()) : "");
                robot.setTotalOdom(robotContext.getTotalOdom() != null ? (robotContext.getTotalRunning().intValue() > 1000 ? CompuUtils.divide(2, new BigDecimal(robotContext.getTotalRunning()), new BigDecimal(1000))+"KM" : robotContext.getTotalRunning().intValue()+"米") : "");
                robot.setBattery(robotContext.getBattery() != null ? robotContext.getBattery()+"%" : "");
                robot.setVelocity(robotContext.getVelocity() != null ? robotContext.getVelocity().toString() : "");
                robot.setReliability(robotContext.getReliability() != null ? robotContext.getReliability().toString() : "");

            }

            LambdaQueryWrapper<RobotHardware> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotHardware::getRobotId, robot.getId());
            robot.setRobotHardwareList(robotHardwareService.list(queryWrapper));

        });
        return AjaxResult.success(list);
    }

    @GetMapping("/getRunStatus")
    public AjaxResult getRunStatus(RobotDynamicAttr robotDynamicAttr) {
        LambdaQueryWrapper<RobotDynamicAttr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobotDynamicAttr::getDelFlag, "0");
        queryWrapper.eq(RobotDynamicAttr::getRobotId, robotDynamicAttr.getRobotId());
        robotDynamicAttr = robotDynamicAttrService.getOne(queryWrapper);
        return AjaxResult.success(robotDynamicAttr);
    }
}
