package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.file.FileUploadUtils;
import com.honichang.common.utils.file.MimeTypeUtils;
import com.honichang.point.domain.ConfRobotVoice;
import com.honichang.point.domain.ConfRobotVoicePublish;
import com.honichang.point.domain.Robot;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.ConfRobotVoicePublishService;
import com.honichang.point.service.ConfRobotVoiceService;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.RobotService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RestController
@RequestMapping("/confRobotVoice")
public class confRobotVoiceController extends BaseController {

    @Resource
    private ConfRobotVoiceService confRobotVoiceService;
    @Resource
    private ConfRobotVoicePublishService confRobotVoicePublishService;
    @Resource
    private RobotService robotService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody ConfRobotVoice confRobotVoice) {
        confRobotVoice.setCreateBy(getUsername());
        confRobotVoice.setCreateTime(new Date());
        return toAjax(confRobotVoiceService.add(confRobotVoice));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(confRobotVoiceService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody ConfRobotVoice confRobotVoice) {
        confRobotVoice.setUpdateBy(getUsername());
        confRobotVoice.setUpdateTime(new Date());
        return toAjax(confRobotVoiceService.edit(confRobotVoice));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(ConfRobotVoice confRobotVoice) {
        startPage();
        LambdaQueryWrapper<ConfRobotVoice> wrapper = new LambdaQueryWrapper<>();
        if (confRobotVoice.getDisplay() != null && !confRobotVoice.getDisplay().equals("")) {
            wrapper.like(ConfRobotVoice::getDisplay, confRobotVoice.getDisplay());
        }
        wrapper.eq(ConfRobotVoice::getDelFlag, "0");
        List<ConfRobotVoice> list = confRobotVoiceService.list(wrapper);

        for (ConfRobotVoice robotVoice : list) {
            LambdaQueryWrapper<ConfRobotVoicePublish> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(ConfRobotVoicePublish::getRobotVoidId, robotVoice.getId());
            List<ConfRobotVoicePublish> publishList = confRobotVoicePublishService.list(wrapper1);
            List<Long> robotIds = publishList.stream().map(ConfRobotVoicePublish::getRobotId).collect(Collectors.toList());
            long robotCount = robotService.count();
            List<String> robotNames = robotService.listByIds(robotIds).stream().map(Robot::getRobotName).collect(Collectors.toList());
            robotVoice.setRobotIds(robotIds);
            if (robotNames.size() == robotCount) {
                ArrayList<String> list1 = new ArrayList<>();
                list1.add("全部");
                robotVoice.setRobotNames(list1);
            } else {
                robotVoice.setRobotNames(robotNames);
            }
        }

        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(ConfRobotVoice confRobotVoice) {
        LambdaQueryWrapper<ConfRobotVoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfRobotVoice::getDelFlag, "0");
        return AjaxResult.success(confRobotVoiceService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        ConfRobotVoice robotVoice = confRobotVoiceService.getById(id);
        LambdaQueryWrapper<ConfRobotVoicePublish> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(ConfRobotVoicePublish::getRobotVoidId, robotVoice.getId());
        List<ConfRobotVoicePublish> publishList = confRobotVoicePublishService.list(wrapper1);
        List<Long> robotIds = publishList.stream().map(ConfRobotVoicePublish::getRobotId).collect(Collectors.toList());
        long robotCount = robotService.count();
        if (robotIds.size() == robotCount) {
            ArrayList<Long> list1 = new ArrayList<>();
            list1.add(0l);
            robotVoice.setRobotIds(list1);
        } else {
            robotVoice.setRobotIds(robotIds);
        }
        return AjaxResult.success(robotVoice);
    }

    @RequestMapping("/issue")
    public AjaxResult issue(@RequestBody ConfRobotVoice confRobotVoice) {

        return toAjax(true);
    }

}
