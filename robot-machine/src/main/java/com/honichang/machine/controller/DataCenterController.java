package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.point.domain.*;
import com.honichang.point.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/dataCenter/dataCenter")
public class DataCenterController extends BaseController {


    @Autowired
    private BizPointService bizPointService;
    @Autowired
    private TaskInstanceNodeService taskInstanceNodeService;
    @Autowired
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Autowired
    private MediaFileService mediaFileService;

    @Autowired
    private IdenModelParamService idenModelParamService;
    @Autowired
    private IdenTypeParamService idenTypeParamService;


    @RequestMapping("/getBizPointList")
    public AjaxResult getBizPointList(BizPoint bizPoint) {
        List<BizPoint> list = bizPointService.getBizPointList(bizPoint);
        return AjaxResult.success(list);
    }

    @RequestMapping("/getIdenModelParam")
    public AjaxResult getIdenModelParam(BizPoint bizPoint) {
        //参数
        LambdaQueryWrapper<IdenModelParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
        List<IdenModelParam> list = idenModelParamService.list(queryWrapper);
        for (IdenModelParam item : list) {
            IdenTypeParam idenTypeParam = idenTypeParamService.getById(item.getIdenTypeParamId());
            item.setIdenTypeParam(idenTypeParam);
        }
        return AjaxResult.success(list);
    }


    @RequestMapping("/getHistoryCurve")
    public AjaxResult getHistoryCurve(HistoryCurveDto historyCurveDto) {

        List<TaskInstanceNodeResult> list = taskInstanceNodeResultService.getHistoryCurve(historyCurveDto);
        return AjaxResult.success(list);
    }


    @RequestMapping("/getTaskInstanceNode")
    public AjaxResult getTaskInstanceNode(TaskInstanceNode taskInstanceNode) {
        TaskInstanceNode node = taskInstanceNodeService.getById(taskInstanceNode.getId());
        //参数
        LambdaQueryWrapper<TaskInstanceNodeResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, node.getId());
        List<TaskInstanceNodeResult> list = taskInstanceNodeResultService.list(queryWrapper);
        for (TaskInstanceNodeResult item : list) {
            IdenModelParam idenModelParam = idenModelParamService.getById(item.getIdenModelParamId());
            IdenTypeParam idenTypeParam = idenTypeParamService.getById(idenModelParam.getIdenTypeParamId());
            item.setIdenModelParam(idenModelParam);
            item.setIdenTypeParam(idenTypeParam);
        }
        node.setTaskInstanceNodeResultList(list);
        //图片
        LambdaQueryWrapper<MediaFile> mediaFileWrapper = new LambdaQueryWrapper<>();
        mediaFileWrapper.eq(MediaFile::getBizType, "task_instance_node");
        mediaFileWrapper.eq(MediaFile::getBizId, node.getId());
        List<MediaFile> mediaFileList = mediaFileService.list(mediaFileWrapper);
        node.setMediaFileList(mediaFileList);

        return AjaxResult.success(node);
    }

    @PostMapping("/approvalInstanceNode")
    public AjaxResult approvalInstanceNode(@RequestBody TaskInstanceNode taskInstanceNode) {
        LambdaUpdateWrapper<TaskInstanceNode> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(TaskInstanceNode::getId, taskInstanceNode.getId());
        queryWrapper.set(TaskInstanceNode::getRemark, taskInstanceNode.getRemark());
        queryWrapper.set(TaskInstanceNode::getAuditStatus, taskInstanceNode.getAuditStatus());
        queryWrapper.set(TaskInstanceNode::getUpdateBy, getUserId());
        queryWrapper.set(TaskInstanceNode::getUpdateTime, new Date());
        taskInstanceNodeService.update(queryWrapper);
        if (taskInstanceNode.getTaskInstanceNodeResultList() != null && taskInstanceNode.getTaskInstanceNodeResultList().size() > 0) {
            taskInstanceNode.getTaskInstanceNodeResultList().forEach(item -> {
                LambdaUpdateWrapper<TaskInstanceNodeResult> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TaskInstanceNodeResult::getId, item.getId());
                updateWrapper.set(TaskInstanceNodeResult::getActualFloatValue, item.getActualFloatValue());
                updateWrapper.set(TaskInstanceNodeResult::getActualEnumValue, item.getActualEnumValue());
                updateWrapper.set(TaskInstanceNodeResult::getCorrectFlag, item.getCorrectFlag());
                updateWrapper.set(TaskInstanceNodeResult::getUpdateBy, getUserId());
                updateWrapper.set(TaskInstanceNodeResult::getUpdateTime, new Date());
                taskInstanceNodeResultService.update(updateWrapper);
            });
        }
        return AjaxResult.success();
    }

}
