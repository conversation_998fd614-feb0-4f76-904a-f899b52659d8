package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.DateUtils;
import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.machine.domain.HistoryDto;
import com.honichang.machine.domain.HistoryTableDto;
import com.honichang.point.domain.*;
import com.honichang.point.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dataCenter/dataCenter")
public class DataCenterController extends BaseController {


    @Autowired
    private BizPointService bizPointService;
    @Autowired
    private TaskInstanceNodeService taskInstanceNodeService;
    @Autowired
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Autowired
    private MediaFileService mediaFileService;

    @Autowired
    private IdenModelParamService idenModelParamService;
    @Autowired
    private IdenTypeParamService idenTypeParamService;


    @RequestMapping("/getBizPointList")
    public AjaxResult getBizPointList(BizPoint bizPoint) {
        List<BizPoint> list = bizPointService.getBizPointList(bizPoint);
        return AjaxResult.success(list);
    }

    @RequestMapping("/getIdenModelParam")
    public AjaxResult getIdenModelParam(BizPoint bizPoint) {
        //参数
        LambdaQueryWrapper<IdenModelParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
        List<IdenModelParam> list = idenModelParamService.list(queryWrapper);
        for (IdenModelParam item : list) {
            IdenTypeParam idenTypeParam = idenTypeParamService.getById(item.getIdenTypeParamId());
            item.setIdenTypeParam(idenTypeParam);
        }
        return AjaxResult.success(list);
    }


    @RequestMapping("/getHistoryCurve")
    public AjaxResult getHistoryCurve(HistoryCurveDto historyCurveDto) {

        List<TaskInstanceNodeResult> list = taskInstanceNodeResultService.getHistoryCurve(historyCurveDto);
        return AjaxResult.success(list);
    }


    @RequestMapping("/getTaskInstanceNode")
    public AjaxResult getTaskInstanceNode(TaskInstanceNode taskInstanceNode) {
        TaskInstanceNode node = taskInstanceNodeService.getById(taskInstanceNode.getId());
        //参数
        LambdaQueryWrapper<TaskInstanceNodeResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, node.getId());
        List<TaskInstanceNodeResult> list = taskInstanceNodeResultService.list(queryWrapper);
        for (TaskInstanceNodeResult item : list) {
            IdenModelParam idenModelParam = idenModelParamService.getById(item.getIdenModelParamId());
            IdenTypeParam idenTypeParam = idenTypeParamService.getById(idenModelParam.getIdenTypeParamId());
            item.setIdenModelParam(idenModelParam);
            item.setIdenTypeParam(idenTypeParam);
        }
        node.setTaskInstanceNodeResultList(list);
        //图片
        LambdaQueryWrapper<MediaFile> mediaFileWrapper = new LambdaQueryWrapper<>();
        mediaFileWrapper.eq(MediaFile::getBizType, "task_instance_node");
        mediaFileWrapper.eq(MediaFile::getBizId, node.getId());
        List<MediaFile> mediaFileList = mediaFileService.list(mediaFileWrapper);
        node.setMediaFileList(mediaFileList);

        return AjaxResult.success(node);
    }

    @PostMapping("/approvalInstanceNode")
    public AjaxResult approvalInstanceNode(@RequestBody TaskInstanceNode taskInstanceNode) {
        LambdaUpdateWrapper<TaskInstanceNode> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(TaskInstanceNode::getId, taskInstanceNode.getId());
        queryWrapper.set(TaskInstanceNode::getRemark, taskInstanceNode.getRemark());
        queryWrapper.set(TaskInstanceNode::getAuditStatus, taskInstanceNode.getAuditStatus());
        queryWrapper.set(TaskInstanceNode::getUpdateBy, getUserId());
        queryWrapper.set(TaskInstanceNode::getUpdateTime, new Date());
        taskInstanceNodeService.update(queryWrapper);
        if (taskInstanceNode.getTaskInstanceNodeResultList() != null && taskInstanceNode.getTaskInstanceNodeResultList().size() > 0) {
            taskInstanceNode.getTaskInstanceNodeResultList().forEach(item -> {
                LambdaUpdateWrapper<TaskInstanceNodeResult> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TaskInstanceNodeResult::getId, item.getId());
                updateWrapper.set(TaskInstanceNodeResult::getActualFloatValue, item.getActualFloatValue());
                updateWrapper.set(TaskInstanceNodeResult::getActualEnumValue, item.getActualEnumValue());
                updateWrapper.set(TaskInstanceNodeResult::getCorrectFlag, item.getCorrectFlag());
                updateWrapper.set(TaskInstanceNodeResult::getUpdateBy, getUserId());
                updateWrapper.set(TaskInstanceNodeResult::getUpdateTime, new Date());
                taskInstanceNodeResultService.update(updateWrapper);
            });
        }
        return AjaxResult.success();
    }


    @RequestMapping("/getHistory")
    public HistoryTableDto getHistory(HistoryDto historyDto) {
        HistoryTableDto historyTableDto = new HistoryTableDto();
        BizPoint bizPoint = bizPointService.getById(historyDto.getBizPointId());
        LambdaQueryWrapper<IdenModelParam> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
        List<IdenModelParam> idenModelParamList = idenModelParamService.list(lambdaQueryWrapper);
        Map<Long, IdenModelParam> idenModelParamMap = idenModelParamList.stream().collect(Collectors.toMap(IdenModelParam::getId, param -> param));
        List<String> tableHeader = idenModelParamList.stream().map(IdenModelParam::getParamName).collect(Collectors.toList());
        historyTableDto.setTableHeader(tableHeader.toArray(new String[tableHeader.size()]));
        startPage();
        List<TaskInstanceNode> list = taskInstanceNodeService.getHistory(historyDto);
        TableDataInfo tableDataInfo = getDataTable(list);
        historyTableDto.setTotal(tableDataInfo.getTotal());
        if (tableDataInfo.getRows().size() > 0) {
            for (Object item : tableDataInfo.getRows()) {
                TaskInstanceNode taskInstanceNode = (TaskInstanceNode) item;
                LambdaQueryWrapper<TaskInstanceNodeResult> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, taskInstanceNode.getId());
                List<TaskInstanceNodeResult> resultList = taskInstanceNodeResultService.list(queryWrapper);
                Map<String, Object> map = new HashMap<>();
                map.put("时间", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInstanceNode.getIdenTime()));
                for (TaskInstanceNodeResult nodeResult : resultList) {
                    IdenModelParam idenModelParam = idenModelParamMap.get(nodeResult.getIdenModelParamId());
                    if (idenModelParam != null) {
                        map.put(idenModelParam.getParamName(), nodeResult.getDataType() == 1 ? nodeResult.getActualFloatValue() : nodeResult.getActualEnumValue());
                        break;
                    }
                }
                historyTableDto.getList().add(map);
            }
        }
        return historyTableDto;
    }

    @RequestMapping("/exportHistory")
    public HistoryTableDto exportHistory(HistoryDto historyDto) {
        HistoryTableDto historyTableDto = new HistoryTableDto();
        BizPoint bizPoint = bizPointService.getById(historyDto.getBizPointId());
        LambdaQueryWrapper<IdenModelParam> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
        List<IdenModelParam> idenModelParamList = idenModelParamService.list(lambdaQueryWrapper);
        List<TaskInstanceNode> list = taskInstanceNodeService.getHistory(historyDto);
        if (list.size() > 0) {
            //写入头
            List<String> tableHeader = new ArrayList<>();
            tableHeader.add("时间");
            idenModelParamList.stream().forEach(item -> tableHeader.add(item.getParamName()));
            historyTableDto.getList().add(tableHeader);
            for (TaskInstanceNode taskInstanceNode : list) {
                List<Object> child = new ArrayList<>();
                child.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, taskInstanceNode.getIdenTime()));
                idenModelParamList.forEach(idenModelParam ->{
                    LambdaQueryWrapper<TaskInstanceNodeResult> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(TaskInstanceNodeResult::getTaskInstanceNodeId, taskInstanceNode.getId());
                    queryWrapper.eq(TaskInstanceNodeResult::getIdenModelParamId, idenModelParam.getId());
                    TaskInstanceNodeResult one = taskInstanceNodeResultService.getOne(queryWrapper);
                    if (one != null) {
                        child.add(one.getDataType() == 1 ? one.getActualFloatValue() : one.getActualEnumValue());
                    }else{
                        child.add("");
                    }
                });
                historyTableDto.getList().add(child);
            }
        }
        return historyTableDto;
    }

}
