package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.poi.ExcelUtil;
import com.honichang.point.domain.LogIndustrialControl;
import com.honichang.point.domain.Robot;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.LogIndustrialControlService;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.RobotService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/logIndustrialControl")
public class logIndustrialControlController extends BaseController {

    @Resource
    private LogIndustrialControlService logIndustrialControlService;
    @Resource
    private RobotService robotService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody LogIndustrialControl logIndustrialControl) {
        logIndustrialControl.setCreateBy(getUsername());
        logIndustrialControl.setCreateTime(new Date());
        return toAjax(logIndustrialControlService.save(logIndustrialControl));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(logIndustrialControlService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody LogIndustrialControl logIndustrialControl) {
        logIndustrialControl.setUpdateBy(getUsername());
        logIndustrialControl.setUpdateTime(new Date());
        return toAjax(logIndustrialControlService.updateById(logIndustrialControl));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(LogIndustrialControl logIndustrialControl) {
        startPage();
        LambdaQueryWrapper<LogIndustrialControl> wrapper = new LambdaQueryWrapper<>();
        if (logIndustrialControl.getContent() != null && !logIndustrialControl.getContent().equals("")) {
            wrapper.like(LogIndustrialControl::getContent, logIndustrialControl.getContent());
        }
        wrapper.eq(LogIndustrialControl::getDelFlag, "0");
        List<LogIndustrialControl> list = logIndustrialControlService.list(wrapper);
        for (LogIndustrialControl industrialControl : list) {
            Robot robot = robotService.getById(industrialControl.getRobotId());
            industrialControl.setRobotName(robot.getRobotName());
        }
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(LogIndustrialControl logIndustrialControl) {
        LambdaQueryWrapper<LogIndustrialControl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogIndustrialControl::getDelFlag, "0");
        return AjaxResult.success(logIndustrialControlService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(logIndustrialControlService.getById(id));
    }

    @PostMapping("/export")
    public void export(HttpServletResponse response, LogIndustrialControl logIndustrialControl)
    {
        LambdaQueryWrapper<LogIndustrialControl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogIndustrialControl::getDelFlag, "0");
        List<LogIndustrialControl> list = logIndustrialControlService.list(wrapper);
        for (LogIndustrialControl industrialControl : list) {
            Robot robot = robotService.getById(industrialControl.getRobotId());
            industrialControl.setRobotName(robot.getRobotName());
        }
        ExcelUtil<LogIndustrialControl> util = new ExcelUtil<LogIndustrialControl>(LogIndustrialControl.class);
        util.exportExcel(response, list, "工控日志");
    }

}
