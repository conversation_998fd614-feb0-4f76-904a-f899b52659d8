package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.file.FileUploadUtils;
import com.honichang.common.utils.file.MimeTypeUtils;
import com.honichang.common.utils.poi.ExcelUtil;

import com.honichang.point.domain.Robot;

import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.RobotService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/robot")
public class robotController extends BaseController {

    @Resource
    private RobotService robotService;
    @Resource
    private RobotHardwareService robotHardwareService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody Robot robot) {
        robot.setCreateBy(getUsername());
        robot.setCreateTime(new Date());
        return toAjax(robotService.save(robot));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(robotService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody Robot robot) {
        robot.setUpdateBy(getUsername());
        robot.setUpdateTime(new Date());
        return toAjax(robotService.updateById(robot));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(Robot robot) {
        startPage();
        LambdaQueryWrapper<Robot> wrapper = new LambdaQueryWrapper<>();
        if (robot.getRobotName() != null && !robot.getRobotName().equals("")) {
            wrapper.like(Robot::getRobotName, robot.getRobotName());
        }
        wrapper.eq(Robot::getDelFlag, "0");
        List<Robot> list = robotService.list(wrapper);
        for (Robot rb : list) {
            LambdaQueryWrapper<RobotHardware> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(RobotHardware::getRobotId, rb.getId());
            rb.setRobotHardwareList(robotHardwareService.list(wrapper1));
        }
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(Robot robot) {
        LambdaQueryWrapper<Robot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Robot::getDelFlag, "0");
        return AjaxResult.success(robotService.list(wrapper));
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(robotService.getById(id));
    }

    @PostMapping("/uploadFirmware")
    public AjaxResult uploadFirmware(MultipartFile file, Integer[] ids) {
        if (file.isEmpty()) {
            return error("上传固件异常，请联系管理员");
        }

        return AjaxResult.success();
    }

    @PostMapping("/restart")
    public AjaxResult restart(@RequestBody Robot robot) {

        return toAjax(true);
    }
}
