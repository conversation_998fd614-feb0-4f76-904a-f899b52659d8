package com.honichang.machine.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.LogEscort;
import com.honichang.point.domain.TaskEscort;
import com.honichang.point.domain.dto.TaskEscortDto;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.LogEscortService;
import com.honichang.point.service.TaskEscortService;
import com.honichang.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.*;


@RestController
@RequestMapping("robot/taskEscort")
public class TaskEscortController extends BaseController {

    @Autowired
    private TaskEscortService taskEscortService;
    @Autowired
    private LogEscortService logEscortService;
    @Autowired
    private BizPointService bizPointService;
    @Autowired
    private ISysUserService sysUserService;
    @Resource
    private DispatchService dispatchService;

    @GetMapping("/getDict")
    public AjaxResult getDict() {
        //机器人
        List<RobotContext> robots = RobotContext.values().stream().collect(Collectors.toList());
        robots.sort(Comparator.comparing(p -> Optional.ofNullable(p.getBattery()).orElse(new Double(-1)), Comparator.reverseOrder()));
        //点位
        LambdaQueryWrapper<BizPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizPoint::getDelFlag, '0');
        List<BizPoint> bizPoints = bizPointService.list(queryWrapper);
        List<SysUser> users = sysUserService.getUserList();

        Map<String, Object> map = new HashMap<>();
        map.put("robots", robots);
        map.put("bizPoints", bizPoints);
        map.put("users", users);
        return AjaxResult.success(map);
    }

    @GetMapping("/list")
    public TableDataInfo list(TaskEscortDto taskEscortDto) {
        Long userId = getUserId();
        startPage();
        LambdaQueryWrapper<TaskEscort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskEscort::getDelFlag, '0');
        queryWrapper.orderByDesc(TaskEscort::getExecTime);
        if (taskEscortDto.getTaskName() != null && !taskEscortDto.getTaskName().trim().equals("")) {
            queryWrapper.like(TaskEscort::getTaskName, taskEscortDto.getTaskName());
        }
        if (StringUtils.isNotEmpty(taskEscortDto.getStatus())) {
            queryWrapper.eq(TaskEscort::getStatus, taskEscortDto.getStatus());
        }
        if (taskEscortDto.getRobotId() != null) {
            queryWrapper.eq(TaskEscort::getRobotId, taskEscortDto.getRobotId());
        }
        if (StringUtils.isNotEmpty(taskEscortDto.getStartTime())) {
            queryWrapper.ge(TaskEscort::getExecTime, taskEscortDto.getStartTime());
        }
        if (StringUtils.isNotEmpty(taskEscortDto.getEndTime())) {
            queryWrapper.le(TaskEscort::getExecTime, taskEscortDto.getEndTime());
        }
        if (userId != 1) {
            queryWrapper.eq(TaskEscort::getCreateBy, userId.toString());
        }

        List<TaskEscort> list = taskEscortService.list(queryWrapper);
        return getDataTable(list);
    }

    @Log(title = "陪同任务", businessType = BusinessType.INSERT)
    @PostMapping("/addTaskEscort")
    public AjaxResult addTaskEscort(@RequestBody TaskEscort taskEscort) {
        if (taskEscort.getExecType() == 1) {
            taskEscort.setExecTime(new Date());
            // 不要改变001状态！
            // 因为CoreScheduler.distributeTasksForRobots会检查001状态，并判断时间，然后会自动触发
            // 改成101反而让核心调度器忽略了它！！
//            taskEscort.setStatus("101");
        } else {
            if (taskEscort.getExecTime() == null) {
                return AjaxResult.error("预约执行时间不能为空");
            }
            if (taskEscort.getExecTime().before(new Date())) {
                return AjaxResult.error("预约执行时间不能早于当前时间");
            }
        }
        if (taskEscortService.checkTimeConflict(taskEscort)) {
            return AjaxResult.error("该机器人执行陪同任务时间段有未执行完的陪同任务");
        }
        taskEscort.setStatus(NOT_STARTED.getCode());
        RobotContext robotContext = RobotContext.get(taskEscort.getRobotId());
        if (taskEscort.getExecType() == 1) {
            if (robotContext.isOnline() == false) {
                return AjaxResult.error("当前机器人已离线无法执行任务");
            }
            if (Double.compare(robotContext.getBattery(), 0.8) < 0) {
                return AjaxResult.error("当前机器人电量不足80%无法执行任务");
            }
        }
        taskEscort.setCreateBy(getUserId().toString());
        taskEscort.setCreateTime(new Date());
        taskEscortService.save(taskEscort);
        return AjaxResult.success();
    }

    @Log(title = "陪同任务", businessType = BusinessType.UPDATE)
    @PostMapping("/editTaskEscort")
    public AjaxResult editTaskEscort(@RequestBody TaskEscort te) {
        if (taskEscortService.checkTimeConflict(te)) {
            return AjaxResult.error("该机器人执行陪同任务时间段有未执行完的陪同任务");
        }
        try {
            taskEscortService.updateTaskEscort(te);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    @Log(title = "陪同任务", businessType = BusinessType.DELETE)
    @PostMapping("/removeTaskEscort")
    public AjaxResult removeTaskEscort(@RequestBody TaskEscort taskEscort) {
        taskEscortService.removeTaskEscort(taskEscort);
        return AjaxResult.success();
    }

    @Log(title = "陪同任务", businessType = BusinessType.UPDATE)
    @PostMapping("/startTask")
    public AjaxResult startTask(@RequestBody TaskEscort taskEscort) {
        TaskEscort ts = taskEscortService.getById(taskEscort.getId());
        if (ts == null) {
            return AjaxResult.error("陪同任务不存在");
        }

        RobotContext robotContext = RobotContext.get(ts.getRobotId());
        if (Double.compare(robotContext.getBattery(), 0.8) < 0) {
            return AjaxResult.error("当前机器人电量不足80%无法执行任务");
        }

        // 这里会判断机器人是否在线等状态
        taskEscortService.startToVisit(ts.getRobotId(), ts);
        return AjaxResult.success();
    }


    @GetMapping("/getLogs")
    public TableDataInfo getLogs(LogEscort logEscort) {
        startPage();
        LambdaQueryWrapper<LogEscort> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(LogEscort::getTaskEscortId, logEscort.getTaskEscortId());
        logWrapper.orderByDesc(LogEscort::getCreateTime);
        List<LogEscort> list = logEscortService.list(logWrapper);
        return getDataTable(list);

    }

    @Log(title = "陪同任务", businessType = BusinessType.UPDATE)
    @PostMapping("/closeAlarm")
    public AjaxResult closeAlarm(@RequestBody TaskEscort taskEscort) {
        boolean exists = TaskEscortContext.exists(taskEscort.getId());
        if (!exists) {
            return AjaxResult.error("陪同任务未开始");
        }
        TaskEscort entity = TaskEscortContext.get(taskEscort.getId()).getTaskEscort();
        if (!Objects.equals(entity.getStatus(), ARRIVED_DESTINATION.getCode())) {
            return AjaxResult.error("未抵达目的地，无法暂停报警");
        }
        if (taskEscort.getPauseMin() == null || taskEscort.getPauseMin() <= 0) {
            return AjaxResult.error("暂停分钟数输入错误");
        }
        LocalDateTime localDateTime = LocalDateTime.now().plusMinutes(taskEscort.getPauseMin());
        entity.setStatus(PAUSED_ALARM.getCode()); // 302-暂停报警
        entity.setPauseMin(taskEscort.getPauseMin());
        entity.setRecoveryTime(DateUtils.toDate(localDateTime));
        entity.setUpdateBy(getUserId().toString());
        entity.setUpdateTime(new Date());
        taskEscortService.updateById(entity);

        return AjaxResult.success();
    }

    @Log(title = "陪同任务", businessType = BusinessType.UPDATE)
    @PostMapping("/openAlarm")
    public AjaxResult openAlarm(@RequestBody TaskEscort taskEscort) {
        boolean exists = TaskEscortContext.exists(taskEscort.getId());
        if (!exists) {
            // ignore
            return AjaxResult.success();
        }
        TaskEscort entity = TaskEscortContext.get(taskEscort.getId()).getTaskEscort();
        if (!Objects.equals(entity.getStatus(), PAUSED_ALARM.getCode())) {
            // ignore
            return AjaxResult.success();
        }
        taskEscortService.restoreAlarm(entity);
        return AjaxResult.success();
    }


    @Log(title = "陪同任务", businessType = BusinessType.UPDATE)
    @PostMapping("/endTask")
    public AjaxResult endTask(@RequestBody TaskEscort taskEscort) {
        taskEscortService.abortByTaskEscortId(taskEscort.getId(), "手动终止");
        return AjaxResult.success();
    }
}
