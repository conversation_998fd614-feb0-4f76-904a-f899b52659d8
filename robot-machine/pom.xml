<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>robot</artifactId>
        <groupId>com.honichang</groupId>
        <version>3.8.9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>robot-machine</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>${tdengine.driver.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.honichang</groupId>
            <artifactId>robot-framework</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.honichang</groupId>
            <artifactId>robot-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

    </dependencies>
</project>
