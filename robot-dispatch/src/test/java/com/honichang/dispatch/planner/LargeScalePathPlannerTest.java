package com.honichang.dispatch.planner;

import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.dispatch.util.TestMapUtil;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.TaskInstanceNode;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 大规模路径规划器测试
 *
 * <AUTHOR>
 */
public class LargeScalePathPlannerTest {

    private static final long TEST_ROBOT_ID = 1L;
    private static final long TEST_MAP_ID = 1L;
    private static final int LARGE_SCALE_POINT_COUNT = 3025;

    @BeforeAll
    public static void setUp() {
        // 创建大规模测试数据
        createLargeScaleTestData();
    }

    @AfterAll
    public static void tearDown() {
        // 清理资源
        LargeScalePathPlanner.shutdown();
        MapContext.clear();
        RobotContext.clear();
    }

    /**
     * 测试小规模路径规划（100个点位）
     */
    @Test
    public void testSmallScalePathPlanning() {
        System.out.println("=== 测试小规模路径规划（100个点位） ===");

        List<TaskNode> taskNodes = createTaskNodes(100);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();

        long startTime = System.currentTimeMillis();

        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);

        long endTime = System.currentTimeMillis();

        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");

        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == 100, "总点位数应该等于输入点位数");

        // 验证路径的连续性
        validatePathContinuity(taskChain, paths);
    }

    /**
     * 测试中等规模路径规划（1000个点位）
     */
    @Test
    public void testMediumScalePathPlanning() {
        System.out.println("=== 测试中等规模路径规划（1000个点位） ===");

        List<TaskNode> taskNodes = createTaskNodes(1000);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();

        long startTime = System.currentTimeMillis();

        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);

        long endTime = System.currentTimeMillis();

        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");

        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == 1000, "总点位数应该等于输入点位数");
        assertTrue(endTime - startTime < 30000, "1000个点位的规划时间应该在30秒内");
    }

    /**
     * 测试大规模路径规划（10000个点位）
     */
    @Test
    public void testLargeScalePathPlanning() {
        System.out.println("=== 测试大规模路径规划（10000个点位） ===");

        List<TaskNode> taskNodes = createTaskNodes(LARGE_SCALE_POINT_COUNT);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();

        long startTime = System.currentTimeMillis();

        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);

        long endTime = System.currentTimeMillis();

        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");

        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == LARGE_SCALE_POINT_COUNT, "总点位数应该等于输入点位数");
        assertTrue(endTime - startTime < 120000, "10000个点位的规划时间应该在2分钟内");

        // 验证路径的连续性
        validatePathContinuity(taskChain, paths);
    }

    /**
     * 测试并发安全性
     */
    @Test
    public void testConcurrentSafety() {
        System.out.println("=== 测试并发安全性 ===");

        List<Thread> threads = new ArrayList<>();
        List<Exception> exceptions = new ArrayList<>();

        // 创建多个线程同时进行路径规划
        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            Thread thread = new Thread(() -> {
                try {
                    List<TaskNode> taskNodes = createTaskNodes(500);
                    TaskChain<TaskNode> taskChain = new TaskChain<>();
                    List<Long> unreachableNodes = new ArrayList<>();
                    PathChain<Path> paths = new PathChain<>();

                    LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID + threadId, taskNodes, taskChain, unreachableNodes, paths);

                    System.out.println("线程" + threadId + "完成，可达点位: " + taskChain.size());
                } catch (Exception e) {
                    synchronized (exceptions) {
                        exceptions.add(e);
                    }
                }
            });
            threads.add(thread);
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join(60000); // 最多等待60秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 验证没有异常
        assertTrue(exceptions.isEmpty(), "并发执行不应该产生异常: " + exceptions);
    }

    @Test
    public void testInitMap() {
//        createLargeScaleTestData();
    }

    /**
     * 创建大规模测试数据
     */
    private static void createLargeScaleTestData() {
        System.out.println("创建大规模测试数据...");

        // 创建地图上下文
        MapContext mapContext = new MapContext();
        mapContext.setMapId(TEST_MAP_ID);
        mapContext.setName("大规模测试地图");
        mapContext.setWidth(250.0);
        mapContext.setHeight(250.0);

        // 创建3000个点位
        List<Position> points = new ArrayList<>();
        List<Path> paths = new ArrayList<>();

        Random random = new Random(42);

        // 一段路径是5个点，N端路径的点就是 5*N-1
        int pathId = 0;
        for (int i = 0; i < 53; i++) {
            for (int j = 0; j < 53; j++) {
                double x = i * 4;
                double y = j * 4;
                double angle = random.nextDouble() * 2 * Math.PI; // 0-2π

                Position point = null;
                if (i % 4 != 0 || j % 4 == 0) {
                    point = new Position(
                            "" + (i * 55 + j),
                            x, y, angle,
                            j % 4 == 0 ? PointClassNameEnum.CROSSROADS.getCode() : PointClassNameEnum.IDEN_POINT.getCode(),
                            "点位" + (i * 55 + j) + "(" + i + "," + j + "), coordinate=(" + x + "," + y + ")"
                    );
                    points.add(point);
                }

                // 创建路径
                if ((i > 0 || j > 0) && point != null) {
                    if (j % 4 == 0 && j > 0 && i % 4 == 0) {
                        // 大路径↓
                        __newPath(pathId, points, paths, i * 55 + j, i * 55 + j - 4, DirectionEnum.FORWARD, 16);
                        __newPath(pathId++, points, paths, i * 55 + j - 4, i * 55 + j, DirectionEnum.BACKWARD, 16);
                    } else if (j > 0) {
                        // 小路径↓
                        __newPath(pathId, points, paths, i * 55 + j, i * 55 + j - 1, DirectionEnum.FORWARD, 4);
                        __newPath(pathId++, points, paths, i * 55 + j - 1, i * 55 + j, DirectionEnum.BACKWARD, 4);
                    }

                    if (i > 0 && j % 4 == 0) {
                        // 小路径←
                        __newPath(pathId, points, paths, i * 55 + j, (i - 1) * 55 + j, DirectionEnum.FORWARD, 4);
                        __newPath(pathId++, points, paths, (i - 1) * 55 + j, i * 55 + j, DirectionEnum.BACKWARD, 4);
                    }
                }
            }
        }

        mapContext.setPoints(points);
        mapContext.setPaths(paths);

        TestMapUtil.drawImage(mapContext, 8000, 8000, 30, "d:/home/<USER>");

        // 缓存地图上下文
        MapContext.put(mapContext);

        // 创建机器人上下文
        RobotContext robotContext = new RobotContext();
        robotContext.setRobotId(TEST_ROBOT_ID);
        robotContext.setMapId(TEST_MAP_ID);
        robotContext.setRobotName("测试机器人");
        robotContext.setPosition(new Position("ROBOT_START", 500.0, 500.0, 0.0, "", "机器人起始位置"));
        robotContext.setTheta(0.0);
        robotContext.setStatus("1"); // 工作状态
        robotContext.setWorkMode(101); // 自动模式

        RobotContext.put(robotContext);

        System.out.println("大规模测试数据创建完成，共" + LARGE_SCALE_POINT_COUNT + "个点位");
    }


    private static void __newPath(int pathId, List<Position> points, List<Path> paths, int startXmapId, int endXmapId, DirectionEnum direction, double length) {
        Path p1 = new Path();
        p1.setPathId((long) pathId);
        p1.setXmapId(p1.getPathId().toString());
        p1.setInstanceName(p1.getXmapId());
        p1.setStartPos(__getPoint(points, "" + startXmapId));
        p1.setEndPos(__getPoint(points, "" + endXmapId));
        p1.setDirection(direction.getCode());
        p1.setRouteType("straight_line");
        p1.setLength(length);
        p1.setMapId(TEST_MAP_ID);
        paths.add(p1);
    }

    private static IPosition __getPoint(List<Position> points, String xmapId) {
        return points.stream().filter(point -> point.getXmapId().equals(xmapId)).findFirst().orElse(null);
    }

    /**
     * 创建指定数量的任务节点
     */
    private List<TaskNode> createTaskNodes(int count) {
        List<TaskNode> taskNodes = new ArrayList<>();

        MapContext mc = MapContext.get(TEST_MAP_ID);
        List<Position> points = mc.getPoints();

        Set<Position> inList = new HashSet<>();
        Random random = new Random(42);

        for (int i = 0; i < count; i++) {
            TaskNode taskNode = new TaskNode();
            TaskInstanceNode instanceNode = new TaskInstanceNode();
            BizPoint bizPoint = new BizPoint();
            instanceNode.setBizPoint(bizPoint);

            // 取points内的随机点位
            Position point = points.get(random.nextInt(points.size()));
            while (inList.contains(point)) {
                point = points.get(random.nextInt(points.size()));
            }
            inList.add(point);
            bizPoint.setXmapId(point.getXmapId());

            taskNode.setTaskInstanceNode(instanceNode);
            instanceNode.setId((long) i);
            instanceNode.setTaskId(1L);
            instanceNode.getBizPoint().setClassName(PointClassNameEnum.IDEN_POINT.getCode());
            instanceNode.setPointName("访问点位" + i);
            instanceNode.setStatus("0"); // 待执行
            instanceNode.setCreateTime(new Date());

            taskNodes.add(taskNode);
        }

        return taskNodes;
    }

    /**
     * 验证路径的连续性
     */
    private void validatePathContinuity(TaskChain<TaskNode> taskChain, PathChain<Path> paths) {
        if (taskChain.size() == 0) return;

        System.out.println("验证路径连续性...");

        // 验证任务节点的顺序是否合理
        List<TaskNode> nodes = taskChain.getAll();
        for (int i = 0; i < nodes.size() - 1; i++) {
            TaskNode current = nodes.get(i);
            TaskNode next = nodes.get(i + 1);

            assertNotNull(current.getTaskInstanceNode().getBizPoint().getXmapId(), "任务节点应该有有效的点位ID");
            assertNotNull(next.getTaskInstanceNode().getBizPoint().getXmapId(), "任务节点应该有有效的点位ID");
        }

        System.out.println("路径连续性验证通过");
    }
}
