package com.honichang.dispatch.planner;

import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.TaskInstanceNode;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 大规模路径规划器测试
 * 
 * <AUTHOR>
 */
public class LargeScalePathPlannerTest {
    
    private static final long TEST_ROBOT_ID = 1L;
    private static final long TEST_MAP_ID = 1L;
    private static final int LARGE_SCALE_POINT_COUNT = 10000;
    
    @BeforeAll
    public static void setUp() {
        // 创建大规模测试数据
        createLargeScaleTestData();
    }
    
    @AfterAll
    public static void tearDown() {
        // 清理资源
        LargeScalePathPlanner.shutdown();
        MapContext.clear();
        RobotContext.clear();
    }
    
    /**
     * 测试小规模路径规划（100个点位）
     */
    @Test
    public void testSmallScalePathPlanning() {
        System.out.println("=== 测试小规模路径规划（100个点位） ===");
        
        List<TaskNode> taskNodes = createTaskNodes(100);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();
        
        long startTime = System.currentTimeMillis();
        
        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");
        
        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == 100, "总点位数应该等于输入点位数");
    }
    
    /**
     * 测试中等规模路径规划（1000个点位）
     */
    @Test
    public void testMediumScalePathPlanning() {
        System.out.println("=== 测试中等规模路径规划（1000个点位） ===");
        
        List<TaskNode> taskNodes = createTaskNodes(1000);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();
        
        long startTime = System.currentTimeMillis();
        
        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");
        
        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == 1000, "总点位数应该等于输入点位数");
        assertTrue(endTime - startTime < 30000, "1000个点位的规划时间应该在30秒内");
    }
    
    /**
     * 测试大规模路径规划（10000个点位）
     */
    @Test
    public void testLargeScalePathPlanning() {
        System.out.println("=== 测试大规模路径规划（10000个点位） ===");
        
        List<TaskNode> taskNodes = createTaskNodes(LARGE_SCALE_POINT_COUNT);
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();
        
        long startTime = System.currentTimeMillis();
        
        LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("规划完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("可达点位数: " + taskChain.size());
        System.out.println("不可达点位数: " + unreachableNodes.size());
        System.out.println("总路径长度: " + paths.getLength() + "米");
        
        // 验证结果
        assertTrue(taskChain.size() > 0, "应该有可达的任务点位");
        assertTrue(taskChain.size() + unreachableNodes.size() == LARGE_SCALE_POINT_COUNT, "总点位数应该等于输入点位数");
        assertTrue(endTime - startTime < 120000, "10000个点位的规划时间应该在2分钟内");
        
        // 验证路径的连续性
        validatePathContinuity(taskChain, paths);
    }
    
    /**
     * 测试并发安全性
     */
    @Test
    public void testConcurrentSafety() {
        System.out.println("=== 测试并发安全性 ===");
        
        List<Thread> threads = new ArrayList<>();
        List<Exception> exceptions = new ArrayList<>();
        
        // 创建多个线程同时进行路径规划
        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            Thread thread = new Thread(() -> {
                try {
                    List<TaskNode> taskNodes = createTaskNodes(500);
                    TaskChain<TaskNode> taskChain = new TaskChain<>();
                    List<Long> unreachableNodes = new ArrayList<>();
                    PathChain<Path> paths = new PathChain<>();
                    
                    LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID + threadId, taskNodes, taskChain, unreachableNodes, paths);
                    
                    System.out.println("线程" + threadId + "完成，可达点位: " + taskChain.size());
                } catch (Exception e) {
                    synchronized (exceptions) {
                        exceptions.add(e);
                    }
                }
            });
            threads.add(thread);
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join(60000); // 最多等待60秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 验证没有异常
        assertTrue(exceptions.isEmpty(), "并发执行不应该产生异常: " + exceptions);
    }
    
    /**
     * 创建大规模测试数据
     */
    private static void createLargeScaleTestData() {
        System.out.println("创建大规模测试数据...");
        
        // 创建地图上下文
        MapContext mapContext = new MapContext();
        mapContext.setMapId(TEST_MAP_ID);
        mapContext.setName("大规模测试地图");
        mapContext.setWidth(1000.0);
        mapContext.setHeight(1000.0);
        
        // 创建10000个点位
        List<Position> points = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重复性
        
        for (int i = 0; i < LARGE_SCALE_POINT_COUNT; i++) {
            double x = random.nextDouble() * 1000; // 0-1000米
            double y = random.nextDouble() * 1000; // 0-1000米
            double angle = random.nextDouble() * 2 * Math.PI; // 0-2π
            
            Position point = new Position(
                "POINT_" + String.format("%05d", i),
                x, y, angle,
                PointClassNameEnum.IDEN_POINT.getCode(),
                "测试点位" + i
            );
            points.add(point);
        }
        
        mapContext.setPoints(points);
        
        // 创建路径（简化：每个点位都可以到达相邻的点位）
        List<com.honichang.dispactch.model.Path> paths = new ArrayList<>();
        // 为了简化测试，这里不创建具体的路径，实际应用中需要根据地图结构创建
        mapContext.setPaths(paths);
        
        // 缓存地图上下文
        MapContext.put(mapContext);
        
        // 创建机器人上下文
        RobotContext robotContext = new RobotContext();
        robotContext.setRobotId(TEST_ROBOT_ID);
        robotContext.setMapId(TEST_MAP_ID);
        robotContext.setRobotName("测试机器人");
        robotContext.setPosition(new Position("ROBOT_START", 500.0, 500.0, 0.0, "", "机器人起始位置"));
        robotContext.setTheta(0.0);
        robotContext.setStatus("1"); // 工作状态
        robotContext.setWorkMode(101); // 自动模式
        
        RobotContext.put(robotContext);
        
        System.out.println("大规模测试数据创建完成，共" + LARGE_SCALE_POINT_COUNT + "个点位");
    }
    
    /**
     * 创建指定数量的任务节点
     */
    private List<TaskNode> createTaskNodes(int count) {
        List<TaskNode> taskNodes = new ArrayList<>();
        Random random = new Random(42);
        
        for (int i = 0; i < count; i++) {
            TaskNode taskNode = new TaskNode();
            TaskInstanceNode instanceNode = new TaskInstanceNode();
            BizPoint bizPoint = new BizPoint();
            instanceNode.setBizPoint(bizPoint);

            taskNode.setTaskInstanceNode(instanceNode);
            instanceNode.setId((long) i);
            instanceNode.setTaskId(1L);
            instanceNode.getBizPoint().setClassName(PointClassNameEnum.IDEN_POINT.getCode());
            instanceNode.setPointName("访问点位" + i);
            instanceNode.getBizPoint().setXmapId("POINT_" + String.format("%05d", random.nextInt(LARGE_SCALE_POINT_COUNT)));
            instanceNode.setStatus("0"); // 待执行
            instanceNode.setCreateTime(new Date());
            
            taskNodes.add(taskNode);
        }
        
        return taskNodes;
    }
    
    /**
     * 验证路径的连续性
     */
    private void validatePathContinuity(TaskChain<TaskNode> taskChain, PathChain<Path> paths) {
        if (taskChain.size() == 0) return;
        
        System.out.println("验证路径连续性...");
        
        // 验证任务节点的顺序是否合理
        List<TaskNode> nodes = taskChain.getAll();
        for (int i = 0; i < nodes.size() - 1; i++) {
            TaskNode current = nodes.get(i);
            TaskNode next = nodes.get(i + 1);
            
            assertNotNull(current.getTaskInstanceNode().getBizPoint().getXmapId(), "任务节点应该有有效的点位ID");
            assertNotNull(next.getTaskInstanceNode().getBizPoint().getXmapId(), "任务节点应该有有效的点位ID");
        }
        
        System.out.println("路径连续性验证通过");
    }
}
