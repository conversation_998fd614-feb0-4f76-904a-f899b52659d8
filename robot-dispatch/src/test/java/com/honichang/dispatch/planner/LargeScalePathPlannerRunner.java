package com.honichang.dispatch.planner;

import com.honichang.dispatch.context.MapContext;
import com.honichang.dispatch.context.RobotContext;
import com.honichang.dispactch.model.TaskChain;
import com.honichang.dispatch.model.TaskNode;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 大规模路径规划器测试运行器
 * 可以直接运行测试大规模路径规划算法
 * 
 * <AUTHOR>
 */
public class LargeScalePathPlannerRunner {
    
    private static final long TEST_ROBOT_ID = 1L;
    private static final long TEST_MAP_ID = 1L;
    
    public static void main(String[] args) {
        System.out.println("=== 大规模路径规划器测试 ===\n");
        
        try {
            // 创建测试数据
            createTestData();
            
            // 测试不同规模的路径规划
            testPathPlanning(100, "小规模");
            testPathPlanning(1000, "中等规模");
            testPathPlanning(10000, "大规模");
            
            // 性能对比测试
            performanceComparison();
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            LargeScalePathPlanner.shutdown();
            System.out.println("\n=== 测试完成 ===");
        }
    }
    
    /**
     * 测试指定规模的路径规划
     */
    private static void testPathPlanning(int pointCount, String scaleName) {
        System.out.println("--- " + scaleName + "测试（" + pointCount + "个点位）---");
        
        try {
            // 创建任务节点
            List<TaskNode> taskNodes = createTaskNodes(pointCount);
            TaskChain<TaskNode> taskChain = new TaskChain<>();
            List<Long> unreachableNodes = new ArrayList<>();
            PathChain<Path> paths = new PathChain<>();
            
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 执行路径规划
            LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);
            
            // 记录结束时间
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 输出结果
            System.out.println("规划耗时: " + duration + "ms");
            System.out.println("可达点位: " + taskChain.size() + "/" + pointCount);
            System.out.println("不可达点位: " + unreachableNodes.size());
            System.out.println("总路径长度: " + String.format("%.2f", paths.getLength()) + "米");
            System.out.println("平均每个点位耗时: " + String.format("%.2f", (double) duration / pointCount) + "ms");
            
            // 验证结果正确性
            validateResult(taskNodes, taskChain, unreachableNodes);
            
            System.out.println("✓ " + scaleName + "测试通过\n");
            
        } catch (Exception e) {
            System.err.println("✗ " + scaleName + "测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 性能对比测试
     */
    private static void performanceComparison() {
        System.out.println("--- 性能对比测试 ---");
        
        int[] testSizes = {100, 500, 1000, 2000, 5000, 10000};
        
        System.out.println("点位数量\t耗时(ms)\t平均耗时(ms/点)\t可达率");
        System.out.println("-------\t-------\t-----------\t-----");
        
        for (int size : testSizes) {
            try {
                List<TaskNode> taskNodes = createTaskNodes(size);
                TaskChain<TaskNode> taskChain = new TaskChain<>();
                List<Long> unreachableNodes = new ArrayList<>();
                PathChain<Path> paths = new PathChain<>();
                
                long startTime = System.currentTimeMillis();
                LargeScalePathPlanner.planTaskPath(TEST_ROBOT_ID, taskNodes, taskChain, unreachableNodes, paths);
                long endTime = System.currentTimeMillis();
                
                long duration = endTime - startTime;
                double avgTime = (double) duration / size;
                double reachableRate = (double) taskChain.size() / size * 100;
                
                System.out.printf("%d\t\t%d\t\t%.2f\t\t\t%.1f%%\n", 
                    size, duration, avgTime, reachableRate);
                
            } catch (Exception e) {
                System.err.println(size + "\t\t失败: " + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    /**
     * 创建测试数据
     */
    private static void createTestData() {
        System.out.println("创建测试数据...");
        
        // 创建地图上下文
        MapContext mapContext = new MapContext();
        mapContext.setMapId(TEST_MAP_ID);
        mapContext.setName("测试地图");
        mapContext.setWidth(1000.0);
        mapContext.setHeight(1000.0);
        
        // 创建10000个点位（网格分布）
        List<Position> points = new ArrayList<>();
        int gridSize = 100; // 100x100的网格
        double spacing = 10.0; // 点位间距10米
        
        for (int i = 0; i < gridSize; i++) {
            for (int j = 0; j < gridSize; j++) {
                double x = i * spacing;
                double y = j * spacing;
                
                Position point = new Position(
                    "POINT_" + String.format("%05d", i * gridSize + j),
                    x, y, 0.0,
                    PointClassNameEnum.IDEN_POINT.getCode(),
                    "点位(" + i + "," + j + ")"
                );
                points.add(point);
            }
        }
        
        mapContext.setPoints(points);
        
        // 创建路径（网格中相邻点位之间的连接）
        List<com.honichang.dispactch.model.Path> paths = new ArrayList<>();
        // 为了简化，这里不创建具体路径，实际应用中需要根据地图结构创建
        mapContext.setPaths(paths);
        
        // 缓存地图上下文
        MapContext.put(mapContext);
        
        // 创建机器人上下文
        RobotContext robotContext = new RobotContext();
        robotContext.setRobotId(TEST_ROBOT_ID);
        robotContext.setMapId(TEST_MAP_ID);
        robotContext.setRobotName("测试机器人");
        robotContext.setPosition(new Position("ROBOT_START", 0.0, 0.0, 0.0, "", "机器人起始位置"));
        robotContext.setTheta(0.0);
        robotContext.setStatus("1");
        robotContext.setWorkMode(101);
        
        RobotContext.put(robotContext);
        
        System.out.println("测试数据创建完成，共" + points.size() + "个点位");
    }
    
    /**
     * 创建指定数量的任务节点
     */
    private static List<TaskNode> createTaskNodes(int count) {
        List<TaskNode> taskNodes = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重复性
        
        for (int i = 0; i < count; i++) {
            TaskNode taskNode = new TaskNode();
            taskNode.setId((long) i);
            taskNode.setTaskId(1L);
            taskNode.setTaskDefId(1L);
            taskNode.setNodeType("VISIT");
            taskNode.setNodeName("访问点位" + i);
            
            // 随机选择一个点位
            int pointIndex = random.nextInt(10000);
            taskNode.setXmapId("POINT_" + String.format("%05d", pointIndex));
            
            taskNode.setStatus(0); // 待执行
            taskNode.setSequence(i);
            taskNode.setCreateTime(new Date());
            
            taskNodes.add(taskNode);
        }
        
        return taskNodes;
    }
    
    /**
     * 验证结果正确性
     */
    private static void validateResult(List<TaskNode> inputNodes, TaskChain<TaskNode> resultChain, List<Long> unreachableNodes) {
        // 验证总数量
        int totalResult = resultChain.size() + unreachableNodes.size();
        if (totalResult != inputNodes.size()) {
            throw new RuntimeException("结果数量不匹配: 输入" + inputNodes.size() + "，输出" + totalResult);
        }
        
        // 验证没有重复
        List<Long> allResultIds = new ArrayList<>();
        for (TaskNode node : resultChain.getAll()) {
            allResultIds.add(node.getId());
        }
        allResultIds.addAll(unreachableNodes);
        
        if (allResultIds.size() != allResultIds.stream().distinct().count()) {
            throw new RuntimeException("结果中存在重复的节点ID");
        }
        
        // 验证所有输入节点都在结果中
        for (TaskNode inputNode : inputNodes) {
            if (!allResultIds.contains(inputNode.getId())) {
                throw new RuntimeException("输入节点" + inputNode.getId() + "在结果中丢失");
            }
        }
    }
}
