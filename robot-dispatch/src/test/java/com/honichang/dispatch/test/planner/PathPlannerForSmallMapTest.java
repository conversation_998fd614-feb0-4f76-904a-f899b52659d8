package com.honichang.dispatch.test.planner;

import com.honichang.common.core.concurrent.FixedThreadPool;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.util.ResultToString;
import com.honichang.dispatch.planner.PathPlanner;
import com.honichang.dispatch.planner.RobotTaskPlanner;
import com.honichang.dispatch.test.common.MockMapData;
import com.honichang.exception.NoReachablePathException;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class PathPlannerForSmallMapTest {

    @BeforeAll
    public static void setUp() {
        // 创建测试数据
        MockMapData.createTestMapDataSmall();
    }

    @AfterAll
    public static void tearDown() {
        // 清理资源
        MapContext.clear();
        RobotContext.clear();
        FixedThreadPool.shutdown();
    }

    @Test
    public void test顺序点位无障碍() throws NoReachablePathException {
        RobotTaskPlannerTest test1 = new RobotTaskPlannerTest();
        Position start = MapContext.getXmapPoint("8", 1);
        TaskChain<TaskNode> taskChain = test1.createTaskChainFix(start,
                new int[]{49, 48, 47, 70, 68, 69, 26, 27, 28});
        TaskDirectiveChain fullPath = RobotTaskPlanner.planPathsByTaskChain(1, start, taskChain);
        System.out.println(ResultToString.toString(fullPath));
    }

    @Test
    public void test顺序点位有障碍1() throws NoReachablePathException {
        RobotTaskPlannerTest test1 = new RobotTaskPlannerTest();
        Position start = MapContext.getXmapPoint("8", 1);

        MapContext mc = MapContext.get(1);
        List<Obstacle> obstacles = new ArrayList<>();
        obstacles.add(new Obstacle(1L, "27", "障碍1", 0.0, 0.0, 1));
        obstacles.add(new Obstacle(2L, "42", "障碍2", 0.0, 0.0, 1));
        mc.addObstacles(obstacles);
        mc.preloadMatrix();

        TaskChain<TaskNode> taskChain = test1.createTaskChainFix(start,
                new int[]{49, 48, 47, 70, 68, 69, 26, 27, 28});
        TaskDirectiveChain fullPath = RobotTaskPlanner.planPathsByTaskChain(1, start, taskChain);
        System.out.println(ResultToString.toString(fullPath));
    }

    @Test
    public void test顺序点位有障碍2() throws NoReachablePathException {
        RobotTaskPlannerTest test1 = new RobotTaskPlannerTest();
        Position start = MapContext.getXmapPoint("8", 1);

        MapContext mc = MapContext.get(1);
        List<Obstacle> obstacles = new ArrayList<>();
        obstacles.add(new Obstacle(1L, "65", "障碍1", 0.0, 0.0, 1));
        obstacles.add(new Obstacle(2L, "99", "障碍2", 0.0, 0.0, 1));
        obstacles.add(new Obstacle(3L, "136", "障碍2", 0.0, 0.0, 1));
        mc.addObstacles(obstacles);
        mc.preloadMatrix();

        TaskChain<TaskNode> taskChain = test1.createTaskChainFix(start,
                new int[]{236, 111, 110, 133, 131, 132, 154, 152, 196, 217, 216, 215});
        TaskDirectiveChain fullPath = RobotTaskPlanner.planPathsByTaskChain(1, start, taskChain);
        System.out.println(ResultToString.toString(fullPath));
    }

    @Test
    public void testCalcPathChainOptimal() throws NoReachablePathException {
        // 走5步
        IPosition start = MapContext.getXmapPoint("12", 1);
        IPosition end = MapContext.getXmapPoint("51", 1);
        Optional<PathChain<IPath>> pathChain = PathPlanner.calcPathChainOptimal(1, start, end, false);
        Assertions.assertTrue(pathChain.isPresent());
        Assertions.assertEquals(5, pathChain.get().size());

        // 走7步
        start = MapContext.getXmapPoint("51", 1);
        end = MapContext.getXmapPoint("136", 1);
        pathChain = PathPlanner.calcPathChainOptimal(1, start, end, false);
        Assertions.assertTrue(pathChain.isPresent());
        Assertions.assertEquals(7, pathChain.get().size());
    }
}
