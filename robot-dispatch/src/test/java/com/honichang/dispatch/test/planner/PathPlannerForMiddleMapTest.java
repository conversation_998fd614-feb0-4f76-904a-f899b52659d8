package com.honichang.dispatch.test.planner;

import com.honichang.common.core.concurrent.FixedThreadPool;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispatch.planner.PathPlanner;
import com.honichang.dispatch.planner.RobotTaskPlanner;
import com.honichang.dispatch.test.common.MockMapData;
import com.honichang.dispactch.util.ResultToString;
import com.honichang.exception.NoReachablePathException;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.Optional;

public class PathPlannerForMiddleMapTest {

    @BeforeAll
    public static void setUp() {
        // 创建测试数据
        MockMapData.createTestMapDataMiddle();
    }

    @AfterAll
    public static void tearDown() {
        // 清理资源
        MapContext.clear();
        RobotContext.clear();
        FixedThreadPool.shutdown();
    }

    /**
     * 随机抽点排任务+路径测试
     */
    @Test
    public void testPlanPathsByTaskChainRandom() throws NoReachablePathException {
        RobotTaskPlannerTest test1 = new RobotTaskPlannerTest();
        Position start = MapContext.getXmapPoint("8", 1);
        TaskChain<TaskNode> taskChain = test1.createTaskChainRandom(start, 100);
        TaskDirectiveChain fullPath = RobotTaskPlanner.planPathsByTaskChain(1, start, taskChain);
        System.out.println(ResultToString.toString(fullPath));
    }

    @Test
    public void testPlanPathsByFixTaskChain() throws NoReachablePathException {
        RobotTaskPlannerTest test1 = new RobotTaskPlannerTest();
        Position start = MapContext.getXmapPoint("8", 1);
        TaskChain<TaskNode> taskChain = test1.createTaskChainFix(start, new int[]{377, 416, 536, 828, 992, 1197, 1148, 1230, 1367, 918, 752, 791, 630, 717, 840, 806, 729, 978, 940, 465});
        TaskDirectiveChain fullPath = RobotTaskPlanner.planPathsByTaskChain(1, start, taskChain);
        System.out.println(ResultToString.toString(fullPath));
    }

    @Test
    public void testCalcPathChainOptimal() throws NoReachablePathException {
        // 走6步
        IPosition start = MapContext.getXmapPoint("828", 1);
        IPosition end = MapContext.getXmapPoint("705", 1);
        Optional<PathChain<IPath>> pathChain = PathPlanner.calcPathChainOptimal(1, start, end, false);
        Assertions.assertTrue(pathChain.isPresent());
        Assertions.assertEquals(6, pathChain.get().size());

        // 走14步
        start = MapContext.getXmapPoint("783", 1);
        end = MapContext.getXmapPoint("1220", 1);
        pathChain = PathPlanner.calcPathChainOptimal(1, start, end, false);
        Assertions.assertTrue(pathChain.isPresent());
        Assertions.assertEquals(14, pathChain.get().size());
    }
}
