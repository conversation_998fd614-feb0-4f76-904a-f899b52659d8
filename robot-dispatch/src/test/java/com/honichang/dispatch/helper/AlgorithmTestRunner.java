package com.honichang.dispatch.helper;

import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;

import java.util.Arrays;
import java.util.List;

/**
 * 算法测试运行器
 * 用于验证十字路口方向选择算法的正确性
 * 
 * <AUTHOR>
 */
public class AlgorithmTestRunner {
    
    public static void main(String[] args) {
        System.out.println("=== 十字路口方向选择算法测试 ===\n");

        System.out.println("计算两点的角度：" + AlgorithmHelper.calcAngleBetweenPoints(-18, 20, -18, 16));
        System.exit(0);
        
        // 测试场景1：期望方向与路径正向一致
        testForwardCase();
        
        // 测试场景2：期望方向与路径正向相反
        testBackwardCase();
        
        // 测试场景3：期望方向垂直于路径
        testPerpendicularCase();
        
        // 测试场景4：路径反向
        testReversePathCase();
        
        // 测试场景5：空路径
        testEmptyPathsCase();
        
        // 测试场景6：复杂角度
        testComplexAnglesCase();
        
        System.out.println("=== 所有测试完成 ===");
    }
    
    /**
     * 测试场景1：期望方向与路径正向一致，应该选择正走
     */
    private static void testForwardCase() {
        System.out.println("测试场景1：期望方向与路径正向一致");
        
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        
        // 目标点在右侧 (10, 0)，期望朝向向右
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 创建从十字路口到目标点的路径（向右）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, paths);
        
        System.out.println("期望结果：1（正走），实际结果：" + result);
        System.out.println("测试" + (result == 1 ? "通过" : "失败") + "\n");
    }
    
    /**
     * 测试场景2：期望方向与路径正向相反，应该选择倒走
     */
    private static void testBackwardCase() {
        System.out.println("测试场景2：期望方向与路径正向相反");
        
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        
        // 目标点在左侧 (-10, 0)，期望朝向向左
        IPosition targetPoint = new Position("TARGET_001", -10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 创建从十字路口向右的路径
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(new Position("END_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "路径终点"));
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, paths);
        
        System.out.println("期望结果：2（倒走），实际结果：" + result);
        System.out.println("测试" + (result == 2 ? "通过" : "失败") + "\n");
    }
    
    /**
     * 测试场景3：期望方向垂直于路径
     */
    private static void testPerpendicularCase() {
        System.out.println("测试场景3：期望方向垂直于路径");
        
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        
        // 目标点在上方 (0, 10)，期望朝向向上
        IPosition targetPoint = new Position("TARGET_001", 0.0, 10.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 创建从十字路口向右的路径
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(new Position("END_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "路径终点"));
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, paths);
        
        System.out.println("期望结果：1（正走，角度差相等时选择正走），实际结果：" + result);
        System.out.println("测试" + (result == 1 ? "通过" : "失败") + "\n");
    }
    
    /**
     * 测试场景4：路径从目标点指向十字路口
     */
    private static void testReversePathCase() {
        System.out.println("测试场景4：路径从目标点指向十字路口");
        
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        
        // 目标点在右侧 (10, 0)，期望朝向向右
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 创建从目标点到十字路口的路径（向左）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(targetPoint);  // 起点是目标点
        path.setEndPos(crossPoint);     // 终点是十字路口
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, paths);
        
        System.out.println("期望结果：2（倒走），实际结果：" + result);
        System.out.println("测试" + (result == 2 ? "通过" : "失败") + "\n");
    }
    
    /**
     * 测试场景5：空路径列表
     */
    private static void testEmptyPathsCase() {
        System.out.println("测试场景5：空路径列表");
        
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, null);
        
        System.out.println("期望结果：1（默认正走），实际结果：" + result);
        System.out.println("测试" + (result == 1 ? "通过" : "失败") + "\n");
    }
    
    /**
     * 测试场景6：复杂角度计算
     */
    private static void testComplexAnglesCase() {
        System.out.println("测试场景6：复杂角度计算");
        
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        
        // 目标点在右上方 (10, 10)，期望角度是π/4
        IPosition targetPoint = new Position("TARGET_001", 10.0, 10.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");
        
        // 创建从十字路口到目标点的路径
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(crossPoint, targetPoint, paths);
        
        System.out.println("期望结果：1（正走），实际结果：" + result);
        System.out.println("测试" + (result == 1 ? "通过" : "失败") + "\n");
    }
}
