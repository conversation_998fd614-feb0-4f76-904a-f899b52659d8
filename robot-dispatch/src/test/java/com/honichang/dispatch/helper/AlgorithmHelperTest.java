package com.honichang.dispatch.helper;

import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 算法助手测试类
 *
 * <AUTHOR>
 */
public class AlgorithmHelperTest {

    /**
     * 点位朝向：Y轴负90度
     */
    private static final double Y_NEG_90 = -Math.PI / 2;

    /**
     * 点位朝向：Y轴正90度
     */
    private static final double Y_POS_90 = Math.PI / 2;

    /**
     * 点位朝向：Y轴正270度（等同于Y轴负90度）
     */
    private static final double Y_POS_270 = 3 * Math.PI / 2;

    /**
     * 点位朝向：X轴正180度
     */
    private static final double X_POS_180 = Math.PI;

    /**
     * 点位朝向：X轴正0度
     */
    private static final double X_POS_0 = 0.0;

    /**
     * 测试场景1：期望方向与路径正向一致，应该选择正走
     */
    @Test
    public void testCalcDirection_ForwardCase() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001",
                -18, 20, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");

        // 目标点在下方 (10, 0)，期望朝向向下
        // 负90度与正270度是等价的
        IPosition[] targetPoint = new IPosition[]{
                // 下方点位&朝向角向下 -90
                new Position("TARGET_001",
                        -18, 16, Y_NEG_90, PointClassNameEnum.IDEN_POINT.getCode(), "目标点"),
                // 下方点位&朝向角向下 270
                new Position("TARGET_001",
                        -18, 16, Y_POS_270, PointClassNameEnum.IDEN_POINT.getCode(), "目标点"),
                // 右侧点位&朝向角向右 0
                new Position("TARGET_001",
                        -14, 20, X_POS_0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点")
        };

        // 创建从十字路口到目标点的路径（向下）
        for (IPosition point : targetPoint) {
            Path path = new Path();
            path.setXmapId("PATH_001");
            path.setStartPos(crossPoint);
            path.setEndPos(point);
            path.setDirection(DirectionEnum.FORWARD.getCode());
            path.setDirection("forward");

            List<IPath> paths = Arrays.asList(path);

            // 执行算法
//            int result = AlgorithmHelper.calcDirection(crossPoint, point, paths);

            // 应该返回1（正走）
//            assertEquals(1, result, "期望方向与路径正向一致时应该选择正走");
        }
    }

    /**
     * 测试场景2：期望方向与路径正向相反，应该选择倒走
     * 十字路口在上，目标点正下方，目标点的朝向是Y轴正90度，测试结果应该是倒走
     */
    @Test
    public void testCalcDirection_BackwardCase() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001",
                -18, 20, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");

        IPosition[] targetPoint = new IPosition[]{
                // 下方点位&朝向角向上 90（倒走）
                new Position("TARGET_001",
                        -18, 16, Y_POS_90, PointClassNameEnum.IDEN_POINT.getCode(), "目标点"),
                // 右侧点位&朝向角向左 -270
                new Position("TARGET_001",
                        -14, 20, X_POS_180, PointClassNameEnum.IDEN_POINT.getCode(), "目标点")
        };

        for (IPosition point : targetPoint) {
            // 创建从十字路口向右的路径
            Path path = new Path();
            path.setXmapId("PATH_001");
            path.setStartPos(crossPoint);
            path.setEndPos(point);
            path.setDirection("forward");

            List<IPath> paths = Arrays.asList(path);

            // 执行算法
//            int result = AlgorithmHelper.calcDirection(crossPoint, point, paths);

            // 期望朝向向左，路径正向向右，应该选择倒走
//            assertEquals(2, result, "期望方向与路径正向相反时应该选择倒走");
        }
    }

    /**
     * 测试场景5：空路径列表的情况
     */
    @Test
    public void testCalcDirection_EmptyPaths() {
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, PointClassNameEnum.CROSSROADS.getCode(), "十字路口");
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, PointClassNameEnum.IDEN_POINT.getCode(), "目标点");

        // 执行算法，期望抛出异常
        try {
//            AlgorithmHelper.calcDirection(crossPoint, targetPoint, null);
        } catch (Exception e) {
            // 期望抛出异常
            assertEquals("无法从十字路口找到通往目标点的路径！", e.getMessage(), "空路径列表时应该抛出异常");
        }
    }

}
