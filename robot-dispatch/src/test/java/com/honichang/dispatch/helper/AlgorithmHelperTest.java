package com.honichang.dispatch.helper;

import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 算法助手测试类
 * 
 * <AUTHOR>
 */
public class AlgorithmHelperTest {
    
    /**
     * 测试场景1：机器人朝向与期望方向一致，应该选择正走
     */
    @Test
    public void testCalcDirection_ForwardCase() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        
        // 目标点在右侧 (10, 0)
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");
        
        // 机器人当前朝向向右 (0弧度)
        double robotAngle = 0.0;
        
        // 创建从十字路口到目标点的路径（向右）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);
        
        // 应该返回1（正走）
        assertEquals(1, result, "机器人朝向与目标方向一致时应该选择正走");
    }
    
    /**
     * 测试场景2：机器人朝向与期望方向相反，应该选择倒走
     */
    @Test
    public void testCalcDirection_BackwardCase() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        
        // 目标点在右侧 (10, 0)
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");
        
        // 机器人当前朝向向左 (π弧度)
        double robotAngle = Math.PI;
        
        // 创建从十字路口到目标点的路径（向右）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);
        
        // 应该返回2（倒走）
        assertEquals(2, result, "机器人朝向与目标方向相反时应该选择倒走");
    }
    
    /**
     * 测试场景3：机器人朝向垂直于目标方向，应该根据期望朝向选择
     */
    @Test
    public void testCalcDirection_PerpendicularCase() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        
        // 目标点在右侧 (10, 0)
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");
        
        // 机器人当前朝向向上 (π/2弧度)
        double robotAngle = Math.PI / 2;
        
        // 创建从十字路口到目标点的路径（向右）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);
        
        // 由于期望朝向是向右(0弧度)，与路径正向一致，应该选择正走
        assertEquals(1, result, "机器人朝向垂直时应该根据期望朝向选择正走");
    }
    
    /**
     * 测试场景4：路径从目标点指向十字路口的情况
     */
    @Test
    public void testCalcDirection_ReversePath() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        
        // 目标点在右侧 (10, 0)
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");
        
        // 机器人当前朝向向右 (0弧度)
        double robotAngle = 0.0;
        
        // 创建从目标点到十字路口的路径（向左）
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(targetPoint);  // 起点是目标点
        path.setEndPos(crossPoint);     // 终点是十字路口
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);
        
        // 从十字路口出发，路径的正向是向左，但机器人朝向向右，期望也是向右
        // 所以应该选择倒走
        assertEquals(2, result, "路径反向时应该选择倒走");
    }
    
    /**
     * 测试场景5：空路径列表的情况
     */
    @Test
    public void testCalcDirection_EmptyPaths() {
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");
        double robotAngle = 0.0;
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, null);
        
        // 应该返回默认值1（正走）
        assertEquals(1, result, "空路径列表时应该返回默认值正走");
    }
    
    /**
     * 测试场景6：复杂角度计算
     */
    @Test
    public void testCalcDirection_ComplexAngles() {
        // 十字路口在原点 (0, 0)
        IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");
        
        // 目标点在右上方 (10, 10)，期望角度是π/4
        IPosition targetPoint = new Position("TARGET_001", 10.0, 10.0, 0.0, "目标点");
        
        // 机器人当前朝向右上方 (π/4弧度)
        double robotAngle = Math.PI / 4;
        
        // 创建从十字路口到目标点的路径
        Path path = new Path();
        path.setXmapId("PATH_001");
        path.setStartPos(crossPoint);
        path.setEndPos(targetPoint);
        path.setDirection("forward");
        
        List<IPath> paths = Arrays.asList(path);
        
        // 执行算法
        int result = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);
        
        // 机器人朝向与期望朝向都是π/4，应该选择正走
        assertEquals(1, result, "复杂角度下机器人朝向与期望一致时应该选择正走");
    }
}
