package com.honichang.dispatch;

import com.honichang.dispatch.common.core.FixedThreadPool;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

public class TestFixPoolTest {

    private static FixedThreadPool fixedThreadPool;

    // 测试前先加载FixedThreadPool.init()
    @BeforeAll
    public static void init() {
        fixedThreadPool = new FixedThreadPool();
        fixedThreadPool.init();
    }

    // 测试完销毁FixedThreadPool.destroy()
    @AfterAll
    public static void destroy() {
        fixedThreadPool.destroy();
        fixedThreadPool = null;
    }

    @Test
    public void testInvokeAll() {
        // 1. 准备任务列表
        List<Callable<String>> tasks = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            tasks.add(() -> {
                // 执行任务逻辑
                System.out.println("Task " + taskId + " is running");
                return "Result of task " + taskId;
            });
        }

        // 2. 执行所有任务并等待完成
        try {
            List<Future<String>> futures = FixedThreadPool.invokeAll(tasks);

            // 3. 处理结果（可选）
            for (Future<String> future : futures) {
                try {
                    String result = future.get();
                    System.out.println("Task result: " + result);
                } catch (ExecutionException e) {
                    System.err.println("Task failed: " + e.getCause());
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Tasks interrupted");
        }
    }

    @Test
    public void testCountDownLatch() {int taskCount = 10;
        CountDownLatch latch = new CountDownLatch(taskCount);

        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            FixedThreadPool.execute(() -> {
                try {
                    // 执行任务逻辑
                    System.out.println("Task " + taskId + " is running");
                } finally {
                    latch.countDown(); // 确保无论如何都会计数
                }
            });
        }

        try {
            latch.await(); // 等待所有任务完成
            System.out.println("All tasks completed");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Waiting interrupted");
        }
    }
}
