package com.honichang.dispatch.planner;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.helper.IDistMatrix;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.bo.ChargingPileFreeOrLessBo;
import com.honichang.dispactch.model.bo.ChargingPilePathPlan;
import com.honichang.dispactch.model.bo.ParkPositionBo;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.exception.NoEmptyParkException;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.RobotChargingPile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * 路径规划器
 *
 * <AUTHOR>
 */
@Slf4j
public class PathPlanner {


    /**
     * 规划前往充电桩 或 充电桩最近空闲停车位的路径<br/>
     * 【重要】如果需要停车位，那么一旦调用了这个接口，将会预约这个停车位
     *
     * @param robotId 机器人id
     * @param piles   最少排队的充电桩 或 空闲的充电桩信息
     * @return 充电桩路径规划结果
     * @throws NoReachablePathException 无可达路径异常
     */
    @NonNull
    public static ChargingPilePathPlan planPathToPileOrNearParking(long robotId, @NonNull ChargingPileFreeOrLessBo piles) throws NoReachablePathException, NoEmptyParkException {
        if (piles.isFree()) {
            return planPathToCharger(robotId, piles);
        } else {
            // 寻找充电桩附近的停车位
            return planPathToParking(robotId, piles);
        }
    }


    /**
     * 规划前往最近充电桩的最近空闲停车位的路径
     * 【重要】一旦调用了这个接口，将会预约这个停车位
     *
     * @param robotId 机器人id
     * @param piles   最少排队的充电桩列表 或 空闲的充电桩信息
     * @return 充电桩路径规划结果
     * @throws NoEmptyParkException     无空闲停车位异常
     * @throws NoReachablePathException 无可达路径异常
     * @throws ServiceException         其他异常
     */
    @NonNull
    public static ChargingPilePathPlan planPathToParking(long robotId, @NonNull ChargingPileFreeOrLessBo piles) throws NoReachablePathException, NoEmptyParkException {

        // 先找【最近的充电桩】
        ChargingPilePathPlan chargingPilePathPlan = planPathToCharger(robotId, piles);
        if (Chain.isEmpty(chargingPilePathPlan.getPathChain())) {
            // 最近充电桩就在脚下，是不可能的！
            // 因为如果就在脚下说明他已经在这里充电了
            return chargingPilePathPlan;
        }

        RobotContext rc = RobotContext.get(robotId);
        // 找最近充电桩附件的【停车位】
        Optional<PathChain<IPath>> pathChain = planPathToParking(robotId, rc.getPosition(),
                chargingPilePathPlan.getPathChain().getTail().getEndPos());

        chargingPilePathPlan.setPathChain(pathChain.orElse(new PathChain<>()));
        chargingPilePathPlan.setPile(false);

        return chargingPilePathPlan;
    }


    /**
     * 规划前往目标点附件的停车位
     * 【重要】一旦调用了这个接口，将会预约这个停车位
     *
     * @param robotId 机器人id
     * @param start   起点
     * @param target  目标点
     * @return 路径规划结果：null说明还在原地不动
     * @throws NoEmptyParkException     无空闲停车位异常
     * @throws NoReachablePathException 无可达路径异常
     * @throws ServiceException         其他异常
     */
    @NonNull
    public static Optional<PathChain<IPath>> planPathToParking(long robotId, @NonNull IPosition start, @NonNull IPosition target) throws NoEmptyParkException, NoReachablePathException {
        // 1. 获取上下文
        RobotContext rc = RobotContext.get(robotId);
        MapContext mc = MapContext.get(rc.getMapId());
        Collection<ParkPositionBo> parkingPoints = mc.getParkPoints();

        // 2. 检查停车位是否存在
        if (parkingPoints == null || parkingPoints.isEmpty()) {
            throw new NoEmptyParkException();
        }

        // 【前半段】
        // 3.检查起点到目标点是否可达，如果不可达后面都不用计算了
        if (MapContext.isUnreachable(mc.getMapId(), start.getXmapId(), target.getXmapId())) {
            throw new NoReachablePathException();
        }

        synchronized (mc.getParkPoints()) {
            // 4. 过滤并排序可用停车位
            parkingPoints = parkingPoints.stream()
                    .filter(p -> p.getRobotId() == null || p.getRobotId() == robotId)
                    .collect(Collectors.toList());

            if (parkingPoints.isEmpty()) {
                throw new NoEmptyParkException();
            }

            // 计算目标点到各个停车位之间的距离
            List<ParkPositionBo> list = new ArrayList<>(parkingPoints);
            list.sort((a, b) -> {
                double distA = mc.getDist(target.getXmapId(), a.getXmapId());
                double distB = mc.getDist(target.getXmapId(), b.getXmapId());
                return Double.compare(distA, distB);
            });

            ParkPositionBo park = list.get(0);
            // 【后半段】
            // 排优后，从目标点到所有停车位还是不可达
            if (MapContext.isUnreachable(mc.getMapId(), target.getXmapId(), park.getXmapId())) {
                throw new NoReachablePathException();
            }

            // 这里是从起点到目标点最近的停车位的完整路径
            // 而非目标点到停车位的路径
            Optional<PathChain<IPath>> result = calcPathChainOptimal(robotId, start, park, false);
            park.setRobotId(robotId);
            return result;
        }
    }


    /**
     * 规划前往充电桩的路径
     *
     * @param robotId 机器人id
     * @param piles   排队最少的充电桩列表
     * @return 充电桩路径规划结果, path=null说明就在脚下
     * @throws NoReachablePathException 所有充电桩都无可达路径异常
     * @throws ServiceException         充电桩点位不存在或充电桩列表为空
     */
    @NonNull
    public static ChargingPilePathPlan planPathToCharger(long robotId, @NonNull ChargingPileFreeOrLessBo piles) throws NoReachablePathException {
        if (piles.getPiles().isEmpty())
            throw new ServiceException("充电桩列表为空！", HttpStatus.ERROR);

        RobotContext rc = RobotContext.get(robotId);
        MapContext mc = MapContext.get(rc.getMapId());

        String startXmapId = rc.getPosition().getXmapId();

        // 根据MapContext的距离矩阵查找最近的充电桩
        piles.getPiles().sort((a, b) -> {
            double distA = mc.getDist(startXmapId, a.getBizPoint().getXmapId());
            double distB = mc.getDist(startXmapId, b.getBizPoint().getXmapId());
            return Double.compare(distA, distB);
        });

        // 排优后还是不可达
        RobotChargingPile nearest = piles.getPiles().get(0);

        Position pile = MapContext.getXmapPoint(nearest.getBizPoint().getXmapId(), rc.getMapId());
        if (pile == null) {
            throw new ServiceException("充电桩点位在地图中不存在：" + nearest.getBizPoint().getInstanceName(), HttpStatus.ERROR);
        }

        if (MapContext.isUnreachable(mc.getMapId(), startXmapId, nearest.getBizPoint().getXmapId())) {
            throw new NoReachablePathException();
        }

        ChargingPilePathPlan result = new ChargingPilePathPlan();
        try {
            Optional<PathChain<IPath>> pathChain = calcPathChainOptimal(robotId, rc.getPosition(), pile, false);

            result.setChargingPile(nearest);
            result.setPathChain(pathChain.orElse(new PathChain<>()));
            result.setPile(true);
        } catch (NoReachablePathException e) {
            // ignore
        }

        return result;
    }


    /**
     * 根据地图上的所有路径、障碍点<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续<br/>
     * 最优算法包含了倒走系数、路径重叠系数、节点数量系数
     *
     * @param robotId                机器人id
     * @param start                  起始点
     * @param end                    目标点
     * @param alwaysForward          是否必须正走
     * @param tempObstacleXmapPathId 临时障碍路径id（如果传入了临时障碍路径id，则会启用临时距离矩阵排除这些路径）
     * @return 按路径长度排序后的路径链结果数组，返回NULL说明还在原地不需要动
     * @throws NoReachablePathException 无可达路径异常
     */
    @NonNull
    public static Optional<PathChain<IPath>> calcPathChainOptimal(
            long robotId,
            @NonNull IPosition start,
            @NonNull IPosition end,
            boolean alwaysForward,
            String... tempObstacleXmapPathId) throws NoReachablePathException {

        // 前置
        // 开始位置可能不是地图点位，而是路径中间
        if (start.getXmapId() == null) {
            RobotContext rc = RobotContext.get(robotId);
            // 如果不在点位上，则需要判断起始两端
            List<Path> ps = MapContext.getPathsByXmapId(rc.getXmapPathId(), rc.getMapId());
            if (ps.isEmpty()) {
                throw new ServiceException("路径不存在: " + rc.getXmapPathId(), HttpStatus.ERROR);
            }
            Path robotCurrPath = ps.get(0);
            // 先假定是两个点一起走先走
            Optional<PathChain<IPath>> p1 = __calcPathChainOptimal(robotId, robotCurrPath.getStartPos(), end, alwaysForward, tempObstacleXmapPathId);
            Optional<PathChain<IPath>> p2 = __calcPathChainOptimal(robotId, robotCurrPath.getEndPos(), end, alwaysForward, tempObstacleXmapPathId);
            Optional<PathChain<IPath>> finalP;
            // 比较p1与p2
            if (!p1.isPresent() && !p2.isPresent()) {
                return Optional.empty();
            } else if (!p1.isPresent()) {
                finalP = p2;
            } else if (!p2.isPresent()) {
                finalP = p1;
            } else {
                double l1 = p1.get().getLengthWithFactor(robotId, !alwaysForward, true);
                double l2 = p2.get().getLengthWithFactor(robotId, !alwaysForward, true);
                finalP = l1 < l2 ? p1 : p2;
            }
            return finalP;
        } else {
            return __calcPathChainOptimal(robotId, start, end, alwaysForward, tempObstacleXmapPathId);
        }
    }

    @NonNull
    private static Optional<PathChain<IPath>> __calcPathChainOptimal(
            long robotId,
            @NonNull IPosition start,
            @NonNull IPosition end,
            boolean alwaysForward,
            String... tempObstacleXmapPathId) throws NoReachablePathException {

        // 1. 计算所有可能的路径链
        List<PathChain<IPath>> list = __calcPathChain(robotId, start, end, tempObstacleXmapPathId);

        if (list == null || list.isEmpty())
            return Optional.empty();
        else if (list.size() == 1)
            return Optional.of(list.get(0));

        // 2. 使用getLengthWithFactor(long currentRobotId)和size组合排序
        list.sort(Comparator.comparingDouble(p -> p.getLengthWithFactor(robotId, !alwaysForward, true)));
//        list.sort(Comparator.comparingDouble(PathChain::getLengthWithFactor));
        // 3. 返回最优路径
        return Optional.of(list.get(0));
    }

    /**
     * 给定地图上的所有路径、障碍点（包含了所属路径id）<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续<br/>
     * 临时距离矩阵会稍微影响性能
     *
     * @param robotId                机器人id
     * @param start                  起始点
     * @param end                    目标点
     * @param tempObstacleXmapPathId 临时障碍路径id（如果传入了临时障碍路径id，则会启用临时距离矩阵排除这些路径）
     * @return 按路径长度排序后的路径链结果数组，如果返回空列表说明下一个任务点还在原地不需要动
     * @throws NoReachablePathException 无可达路径异常
     */
    @Nullable
    private static List<PathChain<IPath>> __calcPathChain(
            long robotId,
            @NonNull IPosition start,
            @NonNull IPosition end,
            String... tempObstacleXmapPathId) throws NoReachablePathException {

        RobotContext rc = RobotContext.get(robotId);

        IDistMatrix dm;
        if (tempObstacleXmapPathId != null && tempObstacleXmapPathId.length > 0) {
            dm = MapContext.get(rc.getMapId()).newTempDistMatrix(tempObstacleXmapPathId);
        } else {
            dm = MapContext.get(rc.getMapId());
        }

        Queue<PathChain<IPath>> results = new ConcurrentLinkedQueue<>();

        List<? extends IPath> paths = dm.getPaths();

        // 如果路径为空
        if (paths == null || paths.isEmpty()) {
            throw new ServiceException("路径列表为空，无法计算路径！", HttpStatus.ERROR);
        }
        // 不允许起点和终点重合，返回空列表，即不需要动
        if (start.equalXY(end)) {
            if (!start.equalCo(end)) { // !!!重要，朝向角不同说明识别点设置错误!!!
                throw new ServiceException("点位配置错误：起始点和目标点重叠，但朝向角不一致，无法计算路径！");
            }
            return null;
        }

        // 获取包含起始点的，且可以往下走的路径
        List<IPath> containStartPosPath = paths.stream()
                .filter(p -> p.isStartPoint(start))
                .collect(Collectors.toList());
        // 没有包含起始点的路径，说明起始点错误
        if (containStartPosPath.isEmpty()) {
            throw new ServiceException("起始点不在任何路径上，无法计算路径！");
        }

        // 初始化holder
        PathPlanHolder holder = new PathPlanHolder(dm, rc, dm.getDist(start.getXmapId(), end.getXmapId()), start, end);

        // 从起始点开始，递归计算所有可能的路径链
        for (IPath path : containStartPosPath) {
            // 基于start点为head，预备一个路径链
            String key = holder.generateMapKey("", path);
            PathChain<IPath> root = new PathChain<>();
            root.add(path);
            holder.pathChainMap.put(key, root);

            // 递归调用
            String successKey = __tryAddIfArrive(holder, key, path);
            if (successKey != null)
                results.add(holder.pathChainMap.get(successKey));

            // 清理内存
            holder.clearCacheByParentKey(key);
        }

        if (results.isEmpty()) {
            throw new NoReachablePathException();
        }

        // 根据路径链的长度排序，越短优先
        return new ArrayList<>(results);
    }

    /**
     * 推算end目标是否可达，如果可达则添加到结果数组中，不可达则什么都不做
     *
     * @param holder    路径规划器
     * @param parentKey 父路径key
     * @param path      当前要走的路径，已经在上一层加入链中
     * @return 成功返回最后的key，失败返回null
     */
    @Nullable
    private static String __tryAddIfArrive(
            @NonNull PathPlanHolder holder,
            @NonNull String parentKey,
            @NonNull IPath path) {

        PathChain<IPath> parentChain = holder.pathChainMap.get(parentKey);
        IPath parentTail = parentChain.getTail();
        IPosition parentTailEnd = parentTail.getEndPos();

        // 暂时不需要记录十字路口
//        // 判断当前path起点是否是十字路口
//        // 如果是则需要写入holder
//        if (path.getStartPos().isCrossroads()) {
//            holder.lastCrossPoint.put(parentKey, path.getStartPos());
//        }

        // 失败跳过判定：
        // 1.如果本路径包含了障碍点，则说明路不通，跳过
        // 2.闭环回到原点，也没抵达，说明这个环路上没有目标点，不跳过就会进入无限递归（有第三条判定，不会无限递归）
        // 3.超过期望路径长度
        if (__isPathBlocked(holder, path) ||
                parentTailEnd.equalCo(holder.start) ||
                __isOutOfExpectedLength(holder, parentChain)) {
            return null;
        }

        // 成功抵达判定：则加入结果数组并返回！但仍需判断一些事情
        // 1.判断是否超过期望路径长度，超过则不考虑
        // 2.判断从最后的十字路口到终点是否是倒走
        if (parentTailEnd.equalCo(holder.end)) {
            if (DirectionEnum.BACKWARD.getCode().equals(path.getDirection())) {
                holder.finalIsBackward.put(parentKey, true);
            }
            return parentKey;
        }

        // 未抵达，则获取除尾链路径外与endPos相连的路径
        List<IPath> containEndPosPaths = holder.distMatrix.getPaths().stream()
                .filter(p -> p.isStartPoint(path.getEndPos()) && !Objects.equals(p.getXmapId(), parentTail.getXmapId()))
                .collect(Collectors.toList());
        // 如果没有包含realEndPos的其他路径，说明是死胡同，跳过
        if (containEndPosPaths.isEmpty()) {
            return null;
        }

        for (IPath p : containEndPosPaths) {
            // 查找这个路径是否已经存在在parent链中，如果已经存在说明绕回来了，自动跳过即可
            if (parentChain.exists(p)) {
                continue;
            }

            // 基于parentTailEndPos点为head，预备一个路径链
            // 如果在这里有多条分支，需要克隆，否则会污染parentChain
            PathChain<IPath> child = containEndPosPaths.size() > 1 ? parentChain.clone() : parentChain;
            child.add(p);

            String key = holder.generateMapKey(parentKey, p);
//            holder.lastCrossPoint.put(key, holder.lastCrossPoint.get(parentKey));
            holder.pathChainMap.put(key, child);

            // 递归调用
            String successKey = __tryAddIfArrive(holder, key, p);
            if (successKey != null)
                return successKey;
            else
                holder.clearCacheByParentKey(key);
        }
        return null;
    }

    private static boolean __isOutOfExpectedLength(@NonNull PathPlanHolder holder, PathChain<IPath> pathChain) {
        double passedLen = pathChain.getLength();
        double len = passedLen + holder.distMatrix.getDist(pathChain.getTail().getEndPos().getXmapId(), holder.end.getXmapId());
        return len > holder.expectedPathLength * 1.01;
    }

    private static boolean __isPathBlocked(@NonNull PathPlanHolder holder, @NonNull IPath path) {
        return holder.distMatrix.obstacleSize() > 0 &&
                ((holder.distMatrix.obstacleSize() == 1 && path.getXmapId().equals(holder.distMatrix.getObstacleXmapPathId(0))) ||
                        holder.distMatrix.containsObstacle(path.getXmapId())
                );
    }


    // region 👇辅助类

    private static class PathPlanHolder {

        private final IDistMatrix distMatrix;

        private final RobotContext rc;

        /**
         * 预计算好的期望路径长度，超过路径1%的分支路线都将短路
         */
        private final double expectedPathLength;

        /**
         * 起点
         */
        private final IPosition start;

        /**
         * 终点
         */
        private final IPosition end;

        /**
         * 每个分支的路径链
         */
        private final Map<String, PathChain<IPath>> pathChainMap = new ConcurrentHashMap<>();

//        /**
//         * 每个分支接近目标点最近的十字路口，没有为null
//         * 暂时不这样做了
//         */
//        private final Map<String, IPosition> lastCrossPoint = new ConcurrentHashMap<>();

        /**
         * 每个分支最终是否倒走
         */
        private final Map<String, Boolean> finalIsBackward = new ConcurrentHashMap<>();

        /**
         * 生成路径链key
         *
         * @param parent 父路径key
         * @param path   当前路径
         * @return 路径链key
         */
        @NonNull
        String generateMapKey(String parent, @NonNull IPath path) {
            return parent + "_" + path.hashCode();
        }

        PathPlanHolder(IDistMatrix dm, RobotContext rc, double expectedPathLength, IPosition start, IPosition end) {
            this.distMatrix = dm;
            this.rc = rc;
            this.expectedPathLength = expectedPathLength;
            this.start = start;
            this.end = end;
        }

        public void clearCacheByParentKey(String key) {
            List<String> keys = pathChainMap.keySet().stream()
                    .filter(k -> k.contains(key))
                    .collect(Collectors.toList());
            keys.forEach(pathChainMap::remove);
        }
    }

    // endregion

}
