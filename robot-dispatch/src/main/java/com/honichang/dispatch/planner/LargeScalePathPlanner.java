package com.honichang.dispatch.planner;

import com.google.common.util.concurrent.AtomicDouble;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.exception.NoReachablePathException;
import com.honichang.exception.PositioningFailedException;
import com.honichang.exception.RobotNotOnAnyPathException;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

public class LargeScalePathPlanner {


    // region 👇任务排点及路径规划

    /**
     * 给任务排列节点，并规划路径
     *
     * @param robotId          机器人id
     * @param task             任务实例
     * @param taskNodes        任务实例节点
     * @param taskChain        引用参数返回规划后的任务队列
     * @param unreachableNodes 引用参数返回不可达节点
     * @param paths            引用参数返回规划后的路径
     **/
    public static void planTaskPath(
            long robotId,
            @NonNull TaskInstance task,
            @NonNull List<TaskInstanceNode> taskNodes,
            @NonNull TaskChain<TaskNode> taskChain,
            @NonNull List<Long> unreachableNodes,
            @NonNull PathChain<Path> paths) throws RobotNotOnAnyPathException, NoReachablePathException, PositioningFailedException {

        // 获取机器人上下文
        RobotContext robotContext = RobotContext.get(robotId);

    }

    /**
     * 计算从A点到任务点的最优路径，期间不能碰到A点和其他待分配任务点
     * 如果碰到其他待分配任务点说明这个不是下个要执行的任务点或这条路径不是到这个任务点的路径
     * 返回到这个任务点最近的路径链，期间要与当前最短路径链比较，如果超过则放弃这个路径链
     * 所有分析过的路径都要缓存，其他路径碰到都要跳过
     * 返回null则为不可达任务点；返回空链说明任务点就在脚下不用动
     *
     * @param start            起始点
     * @param target           目标点
     * @param taskNodes        任务节点（待分配的）
     * @param optimalPathChain 最优路径链
     * @param passedPaths      已经分析过的路径
     * @param allPaths         所有路径
     * @param obstacles        所有障碍点
     * @return 最优路径链：null不可达，空链则目标点就在脚下
     */
    @Nullable
    private static PathChain<IPath> calcPathChainOptimal(
            @NonNull IPosition start,
            @NonNull IPosition target,
            @NonNull List<TaskInstanceNode> taskNodes,
            @NonNull OptimalPathChain optimalPathChain,
            @NonNull Set<? extends IPath> passedPaths,
            @NonNull List<? extends IPath> allPaths,
            @Nullable List<Obstacle> obstacles) {

        return null;
    }

    // endregion


    private static class OptimalPathChain {

        private final AtomicReference<PathChain<IPath>> pathChain = new AtomicReference<>(null);

        private final AtomicDouble minLength = new AtomicDouble(Double.MAX_VALUE);

        public void put(PathChain<IPath> pathChain) {
            if (pathChain.getLength() < minLength.get()) {
                minLength.set(pathChain.getLength());
                this.pathChain.set(pathChain);
            }
        }

        public PathChain<IPath> get() {
            return pathChain.get();
        }

        public double length() {
            return minLength.get();
        }
    }
}
