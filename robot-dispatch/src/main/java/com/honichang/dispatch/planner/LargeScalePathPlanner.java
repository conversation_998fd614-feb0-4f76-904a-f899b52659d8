package com.honichang.dispatch.planner;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispatch.helper.AlgorithmHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

import java.util.*;
import java.util.concurrent.*;

/**
 * 大规模路径规划器
 * 使用分层A*算法处理大规模任务点位的最优路径规划
 *
 * <AUTHOR>
 */
@Slf4j
public class LargeScalePathPlanner {

    // 线程池配置
    private static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private static final ExecutorService THREAD_POOL = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

    // 分层参数
    private static final int CLUSTER_SIZE = 50; // 每个集群的最大点位数
    private static final double CLUSTER_RADIUS = 100.0; // 集群半径（米）

    // region 👇任务排点及路径规划

    /**
     * 给任务排列节点，并规划路径
     *
     * @param robotId          机器人id
     * @param taskNodes        任务节点列表
     * @param taskChain        引用参数返回规划后的任务队列
     * @param unreachableNodes 引用参数返回不可达节点
     * @param paths            引用参数返回规划后的路径
     **/
    public static void planTaskPath(
            long robotId,
            @NonNull List<TaskNode> taskNodes,
            @NonNull TaskChain<TaskNode> taskChain,
            @NonNull List<Long> unreachableNodes,
            @NonNull PathChain<Path> paths) {

        long startTime = System.currentTimeMillis();

        try {
            // 获取机器人上下文
            RobotContext robotContext = RobotContext.get(robotId);
            log.info("开始为机器人[{}]规划{}个任务点位的最优路径", robotContext.getRobotName(), taskNodes.size());

            // 获取地图上下文
            MapContext mapContext = MapContext.get(robotContext.getMapId());

            // 获取机器人当前位置
            IPosition startPosition = AlgorithmHelper.getCurrentOrBehindPosition(robotId);

            // 使用分层A*算法进行路径规划
            HierarchicalAStar hierarchicalAStar = new HierarchicalAStar(mapContext, THREAD_POOL);
            PlanningResult result = hierarchicalAStar.planOptimalPath(startPosition, taskNodes);

            // 填充返回结果
            taskChain.clear();
            for (TaskNode node : result.getOptimalSequence()) {
                taskChain.add(node);
            }

            unreachableNodes.clear();
            unreachableNodes.addAll(result.getUnreachableNodes());

            // 将所有路径链合并到一个PathChain中
            paths.clear();
            for (PathChain<Path> pathChain : result.getOptimalPaths()) {
                for (Path path : pathChain.getAll()) {
                    paths.add(path);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("机器人[{}]路径规划完成，耗时{}ms，可达点位{}个，不可达点位{}个，总路径长度{}米",
                    robotContext.getRobotName(), endTime - startTime, result.getOptimalSequence().size(),
                    result.getUnreachableNodes().size(), result.getTotalDistance());

        } catch (Exception e) {
            log.error("机器人[{}]路径规划失败", robotId, e);
            throw new ServiceException("路径规划失败: " + e.getMessage());
        }
    }

    // endregion

    // region 👇分层A*算法实现

    /**
     * 分层A*算法实现类
     */
    private static class HierarchicalAStar {
        private final MapContext mapContext;
        private final ExecutorService threadPool;
        private final Map<String, Double> distanceCache = new ConcurrentHashMap<>();

        public HierarchicalAStar(MapContext mapContext, ExecutorService threadPool) {
            this.mapContext = mapContext;
            this.threadPool = threadPool;
        }

        /**
         * 规划最优路径
         */
        public PlanningResult planOptimalPath(IPosition startPosition, List<TaskNode> taskNodes) {
            // 第一层：聚类分组
            List<TaskCluster> clusters = clusterTaskNodes(taskNodes);
            log.info("任务点位聚类完成，共{}个集群", clusters.size());

            // 第二层：集群间路径规划
            List<TaskCluster> optimalClusterSequence = planClusterSequence(startPosition, clusters);

            // 第三层：集群内路径规划
            List<TaskNode> optimalSequence = new ArrayList<>();
            List<PathChain<Path>> optimalPaths = new ArrayList<>();
            List<Long> unreachableNodes = new ArrayList<>();
            double totalDistance = 0.0;

            IPosition currentPosition = startPosition;

            for (TaskCluster cluster : optimalClusterSequence) {
                ClusterPlanningResult clusterResult = planClusterInternalPath(currentPosition, cluster);

                optimalSequence.addAll(clusterResult.getSequence());
                optimalPaths.addAll(clusterResult.getPaths());
                unreachableNodes.addAll(clusterResult.getUnreachableNodes());
                totalDistance += clusterResult.getDistance();

                // 更新当前位置为集群最后一个点位
                if (!clusterResult.getSequence().isEmpty()) {
                    TaskNode lastNode = clusterResult.getSequence().get(clusterResult.getSequence().size() - 1);
                    currentPosition = findPositionByXmapId(lastNode.getTaskInstanceNode().getBizPoint().getXmapId());
                }
            }

            return new PlanningResult(optimalSequence, optimalPaths, unreachableNodes, totalDistance);
        }

        /**
         * 任务点位聚类
         */
        private List<TaskCluster> clusterTaskNodes(List<TaskNode> taskNodes) {
            List<TaskCluster> clusters = new ArrayList<>();
            List<TaskNode> remainingNodes = new ArrayList<>(taskNodes);

            while (!remainingNodes.isEmpty()) {
                TaskCluster cluster = new TaskCluster(mapContext);
                TaskNode seedNode = remainingNodes.remove(0);
                cluster.addNode(seedNode);

                IPosition seedPosition = findPositionByXmapId(seedNode.getTaskInstanceNode().getBizPoint().getXmapId());
                if (seedPosition == null) continue;

                // 找到种子点附近的其他点位
                Iterator<TaskNode> iterator = remainingNodes.iterator();
                while (iterator.hasNext() && cluster.size() < CLUSTER_SIZE) {
                    TaskNode node = iterator.next();
                    IPosition nodePosition = findPositionByXmapId(node.getTaskInstanceNode().getBizPoint().getXmapId());

                    if (nodePosition != null && calculateDistance(seedPosition, nodePosition) <= CLUSTER_RADIUS) {
                        cluster.addNode(node);
                        iterator.remove();
                    }
                }

                clusters.add(cluster);
            }

            return clusters;
        }

        /**
         * 规划集群间的访问顺序
         */
        private List<TaskCluster> planClusterSequence(IPosition startPosition, List<TaskCluster> clusters) {
            if (clusters.isEmpty()) return new ArrayList<>();

            // 使用贪心算法规划集群访问顺序
            List<TaskCluster> sequence = new ArrayList<>();
            List<TaskCluster> remaining = new ArrayList<>(clusters);
            IPosition currentPosition = startPosition;

            while (!remaining.isEmpty()) {
                TaskCluster nearestCluster = null;
                double minDistance = Double.MAX_VALUE;

                for (TaskCluster cluster : remaining) {
                    IPosition clusterCenter = cluster.getCenter();
                    double distance = calculateDistance(currentPosition, clusterCenter);

                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestCluster = cluster;
                    }
                }

                if (nearestCluster != null) {
                    sequence.add(nearestCluster);
                    remaining.remove(nearestCluster);
                    currentPosition = nearestCluster.getCenter();
                }
            }

            return sequence;
        }

        /**
         * 规划集群内部的最优路径
         */
        private ClusterPlanningResult planClusterInternalPath(IPosition entryPosition, TaskCluster cluster) {
            List<TaskNode> nodes = cluster.getNodes();
            if (nodes.isEmpty()) {
                return new ClusterPlanningResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), 0.0);
            }

            // 使用并行计算优化TSP问题
            List<Future<TSPResult>> futures = new ArrayList<>();
            int batchSize = Math.max(1, nodes.size() / THREAD_POOL_SIZE);

            for (int i = 0; i < nodes.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, nodes.size());
                List<TaskNode> batch = nodes.subList(i, endIndex);

                Future<TSPResult> future = threadPool.submit(() -> solveTSPForBatch(entryPosition, batch));
                futures.add(future);
            }

            // 收集结果并合并
            List<TaskNode> optimalSequence = new ArrayList<>();
            List<PathChain<Path>> paths = new ArrayList<>();
            List<Long> unreachableNodes = new ArrayList<>();
            double totalDistance = 0.0;

            try {
                for (Future<TSPResult> future : futures) {
                    TSPResult result = future.get(30, TimeUnit.SECONDS); // 30秒超时
                    optimalSequence.addAll(result.getSequence());
                    paths.addAll(result.getPaths());
                    unreachableNodes.addAll(result.getUnreachableNodes());
                    totalDistance += result.getDistance();
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.warn("集群内路径规划超时或失败，使用贪心算法", e);
                return planClusterInternalPathGreedy(entryPosition, cluster);
            }

            return new ClusterPlanningResult(optimalSequence, paths, unreachableNodes, totalDistance);
        }

        /**
         * 贪心算法规划集群内路径（备用方案）
         */
        private ClusterPlanningResult planClusterInternalPathGreedy(IPosition entryPosition, TaskCluster cluster) {
            List<TaskNode> sequence = new ArrayList<>();
            List<PathChain<Path>> paths = new ArrayList<>();
            List<Long> unreachableNodes = new ArrayList<>();
            double totalDistance = 0.0;

            List<TaskNode> remaining = new ArrayList<>(cluster.getNodes());
            IPosition currentPosition = entryPosition;

            while (!remaining.isEmpty()) {
                TaskNode nearestNode = null;
                double minDistance = Double.MAX_VALUE;

                for (TaskNode node : remaining) {
                    IPosition nodePosition = findPositionByXmapId(node.getTaskInstanceNode().getBizPoint().getXmapId());
                    if (nodePosition == null) {
                        unreachableNodes.add(node.getTaskInstanceNode().getId());
                        continue;
                    }

                    double distance = calculateDistance(currentPosition, nodePosition);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestNode = node;
                    }
                }

                if (nearestNode != null) {
                    sequence.add(nearestNode);
                    remaining.remove(nearestNode);

                    // 计算路径
                    IPosition targetPosition = findPositionByXmapId(nearestNode.getTaskInstanceNode().getBizPoint().getXmapId());
                    PathChain<Path> pathChain = calculatePath(currentPosition, targetPosition);
                    if (pathChain != null) {
                        paths.add(pathChain);
                        totalDistance += pathChain.getLength();
                    }

                    currentPosition = targetPosition;
                } else {
                    // 所有剩余节点都不可达
                    for (TaskNode node : remaining) {
                        unreachableNodes.add(node.getTaskInstanceNode().getId());
                    }
                    break;
                }
            }

            return new ClusterPlanningResult(sequence, paths, unreachableNodes, totalDistance);
        }

        /**
         * 解决小批量TSP问题
         */
        private TSPResult solveTSPForBatch(IPosition startPosition, List<TaskNode> nodes) {
            // 对于小规模问题，使用动态规划解决TSP
            if (nodes.size() <= 10) {
                return solveTSPDP(startPosition, nodes);
            } else {
                // 对于较大规模，使用贪心算法
                return solveTSPGreedy(startPosition, nodes);
            }
        }

        /**
         * 动态规划解决TSP
         */
        private TSPResult solveTSPDP(IPosition startPosition, List<TaskNode> nodes) {
            int n = nodes.size();
            if (n == 0) return new TSPResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), 0.0);

            // 构建距离矩阵
            double[][] dist = new double[n + 1][n + 1];
            List<IPosition> positions = new ArrayList<>();
            positions.add(startPosition);

            for (TaskNode node : nodes) {
                IPosition pos = findPositionByXmapId(node.getTaskInstanceNode().getBizPoint().getXmapId());
                positions.add(pos);
            }

            for (int i = 0; i <= n; i++) {
                for (int j = 0; j <= n; j++) {
                    if (positions.get(i) != null && positions.get(j) != null) {
                        dist[i][j] = calculateDistance(positions.get(i), positions.get(j));
                    } else {
                        dist[i][j] = Double.MAX_VALUE;
                    }
                }
            }

            // DP求解TSP
            double[][] dp = new double[1 << n][n];
            int[][] parent = new int[1 << n][n];

            for (int i = 0; i < (1 << n); i++) {
                Arrays.fill(dp[i], Double.MAX_VALUE);
                Arrays.fill(parent[i], -1);
            }

            // 初始化
            for (int i = 0; i < n; i++) {
                dp[1 << i][i] = dist[0][i + 1];
            }

            // 状态转移
            for (int mask = 1; mask < (1 << n); mask++) {
                for (int u = 0; u < n; u++) {
                    if ((mask & (1 << u)) == 0 || dp[mask][u] == Double.MAX_VALUE) continue;

                    for (int v = 0; v < n; v++) {
                        if ((mask & (1 << v)) != 0) continue;

                        int newMask = mask | (1 << v);
                        double newDist = dp[mask][u] + dist[u + 1][v + 1];

                        if (newDist < dp[newMask][v]) {
                            dp[newMask][v] = newDist;
                            parent[newMask][v] = u;
                        }
                    }
                }
            }

            // 找到最优解
            int finalMask = (1 << n) - 1;
            double minCost = Double.MAX_VALUE;
            int lastNode = -1;

            for (int i = 0; i < n; i++) {
                if (dp[finalMask][i] < minCost) {
                    minCost = dp[finalMask][i];
                    lastNode = i;
                }
            }

            // 重构路径
            List<TaskNode> sequence = new ArrayList<>();
            List<PathChain<Path>> paths = new ArrayList<>();
            List<Long> unreachableNodes = new ArrayList<>();

            if (minCost != Double.MAX_VALUE) {
                // 回溯路径
                List<Integer> path = new ArrayList<>();
                int mask = finalMask;
                int curr = lastNode;

                while (curr != -1) {
                    path.add(curr);
                    int prev = parent[mask][curr];
                    mask ^= (1 << curr);
                    curr = prev;
                }

                Collections.reverse(path);

                // 构建结果
                IPosition currentPos = startPosition;
                for (int nodeIndex : path) {
                    TaskNode node = nodes.get(nodeIndex);
                    sequence.add(node);

                    IPosition targetPos = findPositionByXmapId(node.getTaskInstanceNode().getBizPoint().getXmapId());
                    if (targetPos != null) {
                        PathChain<Path> pathChain = calculatePath(currentPos, targetPos);
                        if (pathChain != null) {
                            paths.add(pathChain);
                        }
                        currentPos = targetPos;
                    } else {
                        unreachableNodes.add(node.getTaskInstanceNode().getId());
                    }
                }
            } else {
                // 所有节点都不可达
                for (TaskNode node : nodes) {
                    unreachableNodes.add(node.getTaskInstanceNode().getId());
                }
            }

            return new TSPResult(sequence, paths, unreachableNodes, minCost);
        }

        /**
         * 贪心算法解决TSP
         */
        private TSPResult solveTSPGreedy(IPosition startPosition, List<TaskNode> nodes) {
            List<TaskNode> sequence = new ArrayList<>();
            List<PathChain<Path>> paths = new ArrayList<>();
            List<Long> unreachableNodes = new ArrayList<>();
            double totalDistance = 0.0;

            List<TaskNode> remaining = new ArrayList<>(nodes);
            IPosition currentPosition = startPosition;

            while (!remaining.isEmpty()) {
                TaskNode nearestNode = null;
                double minDistance = Double.MAX_VALUE;

                for (TaskNode node : remaining) {
                    IPosition nodePosition = findPositionByXmapId(node.getTaskInstanceNode().getBizPoint().getXmapId());
                    if (nodePosition == null) {
                        unreachableNodes.add(node.getTaskInstanceNode().getId());
                        continue;
                    }

                    double distance = calculateDistance(currentPosition, nodePosition);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestNode = node;
                    }
                }

                if (nearestNode != null) {
                    sequence.add(nearestNode);
                    remaining.remove(nearestNode);

                    IPosition targetPosition = findPositionByXmapId(nearestNode.getTaskInstanceNode().getBizPoint().getXmapId());
                    PathChain<Path> pathChain = calculatePath(currentPosition, targetPosition);
                    if (pathChain != null) {
                        paths.add(pathChain);
                        totalDistance += pathChain.getLength();
                    }

                    currentPosition = targetPosition;
                } else {
                    for (TaskNode node : remaining) {
                        unreachableNodes.add(node.getTaskInstanceNode().getId());
                    }
                    break;
                }
            }

            return new TSPResult(sequence, paths, unreachableNodes, totalDistance);
        }

        /**
         * 根据XmapId查找位置
         */
        private IPosition findPositionByXmapId(String xmapId) {
            if (xmapId == null || mapContext.getPoints() == null) {
                return null;
            }

            return mapContext.getPoints().stream()
                    .filter(point -> xmapId.equals(point.getXmapId()))
                    .findFirst()
                    .orElse(null);
        }

        /**
         * 计算两点间距离
         */
        private double calculateDistance(IPosition pos1, IPosition pos2) {
            if (pos1 == null || pos2 == null) {
                return Double.MAX_VALUE;
            }

            String cacheKey = pos1.getXmapId() + "-" + pos2.getXmapId();
            return distanceCache.computeIfAbsent(cacheKey, k -> {
                double dx = pos1.getX() - pos2.getX();
                double dy = pos1.getY() - pos2.getY();
                return Math.sqrt(dx * dx + dy * dy);
            });
        }

        /**
         * 计算两点间的路径 - 使用A*算法在路径网络中寻找最短路径
         */
        private PathChain<Path> calculatePath(IPosition start, IPosition target) {
            if (start == null || target == null) {
                return null;
            }

            // 如果起点和终点相同，返回空路径链
            if (start.equalCo(target)) {
                return new PathChain<>();
            }

            // 使用A*算法寻找最短路径
            List<Path> shortestPath = findShortestPathAStar(start, target);

            if (shortestPath == null || shortestPath.isEmpty()) {
                return null; // 无法到达
            }

            // 构建路径链
            PathChain<Path> pathChain = new PathChain<>();
            for (Path path : shortestPath) {
                pathChain.add(path);
            }

            return pathChain;
        }

        /**
         * 使用A*算法寻找最短路径
         */
        private List<Path> findShortestPathAStar(IPosition start, IPosition target) {
            // 构建图结构
            Map<String, List<PathEdge>> graph = buildPathGraph();

            // A*算法数据结构
            PriorityQueue<AStarNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fCost));
            Map<String, AStarNode> allNodes = new HashMap<>();
            Set<String> closedSet = new HashSet<>();

            // 初始化起始节点
            AStarNode startNode = new AStarNode(start.getXmapId(), 0, calculateDistance(start, target));
            openSet.add(startNode);
            allNodes.put(start.getXmapId(), startNode);

            while (!openSet.isEmpty()) {
                AStarNode current = openSet.poll();

                // 到达目标
                if (current.pointId.equals(target.getXmapId())) {
                    return reconstructPath(current, allNodes);
                }

                closedSet.add(current.pointId);

                // 探索邻居节点
                List<PathEdge> neighbors = graph.get(current.pointId);
                if (neighbors != null) {
                    for (PathEdge edge : neighbors) {
                        if (closedSet.contains(edge.toPointId)) {
                            continue;
                        }

                        double tentativeGCost = current.gCost + edge.path.getLength();

                        AStarNode neighbor = allNodes.get(edge.toPointId);
                        if (neighbor == null) {
                            IPosition neighborPos = findPositionByXmapId(edge.toPointId);
                            if (neighborPos == null) continue;

                            double hCost = calculateDistance(neighborPos, target);
                            neighbor = new AStarNode(edge.toPointId, tentativeGCost, hCost);
                            neighbor.parent = current;
                            neighbor.pathFromParent = edge.path;

                            allNodes.put(edge.toPointId, neighbor);
                            openSet.add(neighbor);
                        } else if (tentativeGCost < neighbor.gCost) {
                            // 找到更好的路径
                            openSet.remove(neighbor);
                            neighbor.gCost = tentativeGCost;
                            neighbor.parent = current;
                            neighbor.pathFromParent = edge.path;
                            openSet.add(neighbor);
                        }
                    }
                }
            }

            return null; // 无法到达目标
        }

        /**
         * 构建路径图
         */
        private Map<String, List<PathEdge>> buildPathGraph() {
            Map<String, List<PathEdge>> graph = new HashMap<>();

            if (mapContext.getPaths() == null) {
                return graph;
            }

            for (Path path : mapContext.getPaths()) {
                if (path.getStartPos() == null || path.getEndPos() == null) {
                    continue;
                }

                String startId = path.getStartPos().getXmapId();
                String endId = path.getEndPos().getXmapId();

                // 正向边
                graph.computeIfAbsent(startId, k -> new ArrayList<>())
                        .add(new PathEdge(endId, createForwardPath(path)));

                // 反向边（如果路径支持双向）
                if (!"forward".equals(path.getDirection())) {
                    graph.computeIfAbsent(endId, k -> new ArrayList<>())
                            .add(new PathEdge(startId, createBackwardPath(path)));
                }
            }

            return graph;
        }

        /**
         * 创建正向路径
         */
        private Path createForwardPath(Path originalPath) {
            Path forwardPath = new Path();
            forwardPath.setXmapId(originalPath.getXmapId());
            forwardPath.setStartPos(originalPath.getStartPos());
            forwardPath.setEndPos(originalPath.getEndPos());
            forwardPath.setLength(originalPath.getLength());
            forwardPath.setDirection("forward");
            forwardPath.setRouteType(originalPath.getRouteType());
            forwardPath.setRadian(originalPath.getRadian());
            forwardPath.setCtrlPos1(originalPath.getCtrlPos1());
            forwardPath.setCtrlPos2(originalPath.getCtrlPos2());
            forwardPath.setAngleCompensation(originalPath.getAngleCompensation());
            return forwardPath;
        }

        /**
         * 创建反向路径
         */
        private Path createBackwardPath(Path originalPath) {
            Path backwardPath = new Path();
            backwardPath.setXmapId(originalPath.getXmapId());
            backwardPath.setStartPos(originalPath.getEndPos()); // 起点和终点交换
            backwardPath.setEndPos(originalPath.getStartPos());
            backwardPath.setLength(originalPath.getLength());
            backwardPath.setDirection("backward");
            backwardPath.setRouteType(originalPath.getRouteType());
            backwardPath.setRadian(originalPath.getRadian());
            backwardPath.setCtrlPos1(originalPath.getCtrlPos2()); // 控制点交换
            backwardPath.setCtrlPos2(originalPath.getCtrlPos1());
            backwardPath.setAngleCompensation(-originalPath.getAngleCompensation()); // 角度补偿取反
            return backwardPath;
        }

        /**
         * 重构路径
         */
        private List<Path> reconstructPath(AStarNode targetNode, Map<String, AStarNode> allNodes) {
            List<Path> path = new ArrayList<>();
            AStarNode current = targetNode;

            while (current.parent != null) {
                if (current.pathFromParent != null) {
                    path.add(0, current.pathFromParent); // 在开头插入，保持正确顺序
                }
                current = current.parent;
            }

            return path;
        }
    }

    // endregion

    // region 👇A*算法辅助数据结构

    /**
     * A*算法节点
     */
    private static class AStarNode {
        String pointId;
        double gCost; // 从起点到当前节点的实际代价
        double hCost; // 从当前节点到终点的启发式代价
        double fCost; // gCost + hCost
        AStarNode parent;
        Path pathFromParent; // 从父节点到当前节点的路径

        public AStarNode(String pointId, double gCost, double hCost) {
            this.pointId = pointId;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
        }
    }

    /**
     * 路径边
     */
    private static class PathEdge {
        String toPointId;
        Path path;

        public PathEdge(String toPointId, Path path) {
            this.toPointId = toPointId;
            this.path = path;
        }
    }

    // endregion

    // region 👇数据结构定义

    /**
     * 任务集群
     */
    @Getter
    private static class TaskCluster {
        private final List<TaskNode> nodes = new ArrayList<>();
        private IPosition center;
        private final MapContext mapContext;

        public TaskCluster(MapContext mapContext) {
            this.mapContext = mapContext;
        }

        public void addNode(TaskNode node) {
            nodes.add(node);
            updateCenter();
        }

        public int size() {
            return nodes.size();
        }

        private void updateCenter() {
            if (nodes.isEmpty()) {
                center = null;
                return;
            }

            double sumX = 0, sumY = 0;
            int count = 0;

            for (TaskNode node : nodes) {
                String xmapId = node.getTaskInstanceNode().getBizPoint().getXmapId();
                IPosition nodePosition = findPositionInMap(xmapId);
                if (nodePosition != null) {
                    sumX += nodePosition.getX();
                    sumY += nodePosition.getY();
                    count++;
                }
            }

            if (count > 0) {
                center = new Position(
                        "CLUSTER_CENTER", sumX / count, sumY / count, 0.0, "", "集群中心");
            }
        }

        private IPosition findPositionInMap(String xmapId) {
            if (mapContext.getPoints() == null) return null;
            return mapContext.getPoints().stream()
                    .filter(point -> xmapId.equals(point.getXmapId()))
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 规划结果
     */
    @Getter
    private static class PlanningResult {
        private final List<TaskNode> optimalSequence;
        private final List<PathChain<Path>> optimalPaths;
        private final List<Long> unreachableNodes;
        private final double totalDistance;

        public PlanningResult(List<TaskNode> optimalSequence, List<PathChain<Path>> optimalPaths,
                              List<Long> unreachableNodes, double totalDistance) {
            this.optimalSequence = optimalSequence;
            this.optimalPaths = optimalPaths;
            this.unreachableNodes = unreachableNodes;
            this.totalDistance = totalDistance;
        }
    }

    /**
     * 集群规划结果
     */
    @Getter
    private static class ClusterPlanningResult {
        private final List<TaskNode> sequence;
        private final List<PathChain<Path>> paths;
        private final List<Long> unreachableNodes;
        private final double distance;

        public ClusterPlanningResult(List<TaskNode> sequence, List<PathChain<Path>> paths,
                                     List<Long> unreachableNodes, double distance) {
            this.sequence = sequence;
            this.paths = paths;
            this.unreachableNodes = unreachableNodes;
            this.distance = distance;
        }
    }

    /**
     * TSP求解结果
     */
    @Getter
    private static class TSPResult {
        private final List<TaskNode> sequence;
        private final List<PathChain<Path>> paths;
        private final List<Long> unreachableNodes;
        private final double distance;

        public TSPResult(List<TaskNode> sequence, List<PathChain<Path>> paths,
                         List<Long> unreachableNodes, double distance) {
            this.sequence = sequence;
            this.paths = paths;
            this.unreachableNodes = unreachableNodes;
            this.distance = distance;
        }
    }

    // endregion

    // region 👇线程池管理

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        if (!THREAD_POOL.isShutdown()) {
            THREAD_POOL.shutdown();
            try {
                if (!THREAD_POOL.awaitTermination(60, TimeUnit.SECONDS)) {
                    THREAD_POOL.shutdownNow();
                }
            } catch (InterruptedException e) {
                THREAD_POOL.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // endregion
}
