package com.honichang.dispatch.planner;

import com.google.common.util.concurrent.AtomicDouble;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.ChainBase;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicReference;

import static com.honichang.dispactch.model.enums.TaskNodeStatusEnum.NOT_ARRIVED;
import static com.honichang.dispactch.model.enums.TaskNodeStatusEnum.UNREACHABLE;

/**
 * 机器人任务规划器
 *
 * <AUTHOR>
 */
public class RobotTaskPlanner {

    // region 👇规划任务

    /**
     * 任务规划主函数：
     * 从起点开始计算排序
     * 如果不可达，则直接改变状态为“不可达”
     * （这是第一步）
     *
     * @return 按顺序规划好的任务点位链
     */
    @NonNull
    public static TaskChain<TaskNode> planTasksOrder(
            long robotId,
            @NonNull Position start,
            @NonNull List<TaskInstanceNode> taskInstanceNodes) {

        if (taskInstanceNodes.isEmpty())
            throw new ServiceException("任务节点列表为空！");

        final MapContext mc = MapContext.get(RobotContext.get(robotId).getMapId());

        // 先计算start到所有点的距离，然后排序
        taskInstanceNodes.sort((a, b) -> {
            double distA = mc.getDist(start.getXmapId(), a.getBizPoint().getXmapId());
            double distB = mc.getDist(start.getXmapId(), b.getBizPoint().getXmapId());
            return Double.compare(distA, distB);
        });

        // 判断不可达，同时将距离起点最近的节点放入列表（可能是多个）
        final List<TaskNode> firstNodes = new ArrayList<>();
        final double minDist = mc.getDist(start.getXmapId(), taskInstanceNodes.get(0).getBizPoint().getXmapId());

        for (TaskInstanceNode n : taskInstanceNodes) {
            if (MapContext.isUnreachable(mc.getMapId(), start.getXmapId(), n.getBizPoint().getXmapId())) {
                n.setStatus(UNREACHABLE.getCode()); // 不可达
            } else if (UNREACHABLE.getCode().equals(n.getStatus())) {
                n.setStatus(NOT_ARRIVED.getCode()); // 重置之前不可达的状态，现在可达了
            }
            if (mc.getDist(start.getXmapId(), n.getBizPoint().getXmapId()) == minDist) {
                firstNodes.add(new TaskNode(n));
            }
        }

        // 是否全部不可达
        // 全不可达直接返回即可（正常是不会的）
        if (taskInstanceNodes.stream().allMatch(n -> UNREACHABLE.getCode().equals(n.getStatus()))) {
            TaskChain<TaskNode> result = new TaskChain<>();
            taskInstanceNodes.forEach(n -> result.add(new TaskNode(n)));
            return result;
        }

        // 初始化Holder
        final PlanTaskHolder holder = new PlanTaskHolder(mc);
        // 所有任务节点放入hashMap中
        final Map<Integer, TaskNode> hashCodeNodeMap = new HashMap<>();
        taskInstanceNodes.forEach(tin -> {
            TaskNode tn = new TaskNode(tin);
            hashCodeNodeMap.put(tn.hashCode(), tn);
        });

        Queue<SuccessChain> successChain = new ConcurrentLinkedQueue<>();
        for (TaskNode fn : firstNodes) {
            // 装载第一层(level=0)的Holder
            String key = holder.generateMapKey("_", fn);
            holder.levelLengthMap.put(key,
                    new SimpleChain<>(
                            new LevelLength(0, mc.getDist(start.getXmapId(), fn.getXmapPointId()))));
            holder.shortCircuitMap.put(key, false);
            holder.taskNodeMap.put(key, new TaskChain<>(fn));
            // 这里必须是新的集合
            List<TaskNode> waitingNodes = new ArrayList<>();
            hashCodeNodeMap.values().forEach(tn -> {
                if (!tn.equals(fn) && !"8".equals(tn.getTaskInstanceNode().getStatus())) {
                    waitingNodes.add(tn);
                }
            });
            holder.waittingNodeMap.put(key, waitingNodes);

            // 开始下层递归
            SuccessChain tc = __planTasksOrder(holder, key, fn, 1);
            if (tc != null) {
                successChain.add(tc);
            }
        }

        // 比较successChain，最短路径则为最优
        if (successChain.isEmpty()) {
            throw new ServiceException("任务节点全部不可达！");
        }
        if (successChain.size() == 1) {
            return successChain.poll().taskChain;
        }

        final AtomicReference<SuccessChain> finalResult = new AtomicReference<>(null);
        final AtomicDouble minLength = new AtomicDouble(Double.MAX_VALUE);
        successChain.forEach(v -> {
            double len = v.totalLength;
            if (len < minLength.get()) {
                minLength.set(len);
                finalResult.set(v);
            }
        });

        return finalResult.get().taskChain;
    }

    /**
     * 递归规划任务点位
     *
     * @param holder     上下文参数
     * @param parentKey  父节点key
     * @param parentNode 父节点
     * @param level      当前层数
     * @return 成功返回SuccessChain，失败返回null
     */
    @Nullable
    private static SuccessChain __planTasksOrder(
            @NonNull PlanTaskHolder holder,
            @NonNull String parentKey,
            @NonNull TaskNode parentNode,
            int level) {

        // 没有完成的节点
        List<TaskNode> waitingNodes = holder.waittingNodeMap.get(parentKey);
        if (waitingNodes.isEmpty()) {
            // 说明已经全部规划完毕
            return new SuccessChain(parentKey, holder.taskNodeMap.get(parentKey), holder.getTotalLength(parentKey));
        }

        // 继续规划
        // 从parentNode开始找最近的节点
        waitingNodes.sort((a, b) -> {
            double distA = holder.mc.getDist(parentNode.getXmapPointId(), a.getXmapPointId());
            double distB = holder.mc.getDist(parentNode.getXmapPointId(), b.getXmapPointId());
            return Double.compare(distA, distB);
        });

        // 最近的点位集合
        final List<TaskNode> nearestNodes = new ArrayList<>();
        final double minDist = holder.mc.getDist(parentNode.getXmapPointId(), waitingNodes.get(0).getXmapPointId());
        waitingNodes.forEach(n -> {
            if (holder.mc.getDist(parentNode.getXmapPointId(), n.getXmapPointId()) == minDist) {
                nearestNodes.add(n);
            }
        });

        // 递归规划
        for (TaskNode nn : nearestNodes) {
            // 是否已经短路
            if (holder.shortCircuitMap.get(parentKey)) {
                break;
            }

            // 装载下一层(level=level)的Holder
            String key = holder.generateMapKey(parentKey, nn);
            // 多线程断路器
            holder.shortCircuitMap.put(key, false);

            // 仅这一层的长度追加到链中
            SimpleChain<LevelLength> llc;
            if (nearestNodes.size() > 1) {
                llc = holder.levelLengthMap.get(parentKey).clone();
            } else {
                llc = holder.levelLengthMap.get(parentKey);
            }
            llc.add(new LevelLength(level, holder.mc.getDist(parentNode.getXmapPointId(), nn.getXmapPointId())));
            holder.levelLengthMap.put(key, llc);

            // 通知层数=level的其他线程，总长度更长，可以停止了
            holder.doShortCircuit(level, key, holder.getTotalLength(parentKey) + minDist);

            // 如果这里有多个分支，则必须是新的集合
            TaskChain<TaskNode> tc;
            List<TaskNode> nwn;
            if (nearestNodes.size() > 1) {
                tc = holder.taskNodeMap.get(parentKey).clone();
                nwn = new ArrayList<>(waitingNodes);
            } else {
                tc = holder.taskNodeMap.get(parentKey);
                nwn = waitingNodes;
            }
            tc.add(nn);

            // 是否已经短路
            if (holder.shortCircuitMap.get(key)) {
                continue;
            }

            holder.taskNodeMap.put(key, tc);

            nwn.remove(nn);
            holder.waittingNodeMap.put(key, nwn);

            // 开始下层递归
            SuccessChain sc = __planTasksOrder(holder, key, nn, level + 1);
            if (sc != null) {
                return sc;
            }
        }
        return null;
    }

    /**
     * 将第一步的结果喂给第二步。<br/>
     * 拿到第一步的任务节点链，随后要挨个节点计算：<br/>
     * 1.判断从当前节点（含起点）到下一个节点的完整路径，要求长度必须等于之前距离矩阵中的长度<br/>
     * 2.判断这个路径接近下一个点位最近的十字路口点位<br/>
     * 3.判断这个十字路口切入点位路径的方向（正反）<br/>
     * 4.如果是反的则将目标点放入倒走暂存区<br/>
     * 5.继续下一个点位的循环计算，直到遇到下一个点位不再是倒走<br/>
     * 5.1.倒走结束，判断最后一个点位（正走）与倒数第二个点位之间是否有十字路口，如果没有报错（因为不能调头）<br/>
     * 5.2.倒走结束，将倒走暂存区的点位全部倒序加入到正走队列中<br/>
     * 5.3.倒走结束并排完倒走暂存区后，以倒走暂存区最后写入结果链中的点位为起点，继续找下一个最近的点位<br/>
     * 6.结果队列排完，返回完整的路径链，并最终的排点链<br/>
     *
     * @param robotId   机器人id
     * @param start     起始点位
     * @param taskChain 第一步排好点的任务节点链（引用传递，最终排点链也放入这里）
     * @return 路径链
     */
    @NonNull
    public static TaskDirectiveChain planPathsByTaskChain(
            long robotId,
            @NonNull Position start,
            @NonNull TaskChain<TaskNode> taskChain) throws NoReachablePathException {

        TaskDirectiveChain resultChain = new TaskDirectiveChain();
        resultChain.setTaskInstance(taskChain.getTaskInstance());
        if (taskChain.size() <= 0) {
            return resultChain;
        }

        RobotContext rc = RobotContext.get(robotId);

        // 倒走暂存区
        TaskDirectiveChain backTmpChain = new TaskDirectiveChain();
        boolean isBackward = false;

        // 循环
        boolean reversed = false;

        for (int i = 0; i < taskChain.size(); i++) {
            TaskNode endNode = taskChain.get(i);
            if (UNREACHABLE.getCode().equals(endNode.getTaskInstanceNode().getStatus())) {
                continue;
            }

            // 从start到第一个点位，从上一个点位到下一个点位
            TaskNode startNode = reversed ? resultChain.getTail().getTaskNode() : (i == 0 ? null : taskChain.get(i - 1));
            reversed = false;

            Position startP = startNode == null ? start : startNode.getMapPoint();
            Position endP = endNode.getMapPoint();

            Optional<PathChain<IPath>> subPathChain = PathPlanner.calcPathChainOptimal(robotId, startP, endP, false);
            if (!subPathChain.isPresent()) {
                // 还在原地，不要动
                (isBackward ? backTmpChain : resultChain)
                        .add(new TaskDirective(startP, endNode, null));
                continue;
            }

            // 是否倒走
            isBackward = DirectionEnum.BACKWARD.getCode().equals(subPathChain.get().getTail().getDirection());

            // 如果到当前节点的最后一步需要倒走
            if (isBackward) {
                backTmpChain.add(new TaskDirective(startP, endNode, subPathChain.get()));
            }

            // 如果不再倒走，需要判断倒走暂存区里是否有记录？
            // 如果有，则说明倒走结束，将倒走暂存区的点位全部翻转加入到正走队列中
            if ((endNode.equals(taskChain.getTail()) ||  // 最后一个点位（也不再倒走了）
                    !isBackward)                  // 下一个点位不再倒走
                    && !backTmpChain.isEmpty()) {

                __reverseBackwardTmpChainToResultChain(backTmpChain, resultChain, rc.getMapId());
                reversed = true;
            }

            // 正走
            if (!isBackward) {
                resultChain.add(new TaskDirective(startP, endNode, subPathChain.get()));
            } else {
                // 倒走入死胡同，直接执行抵达后工作的逻辑
                // **清空了backTmpChain**
                if (!backTmpChain.isEmpty() && MapContext.isImpasse(rc.getMapId(), subPathChain.get().getTail())) {
                    __reverseBackwardTmpChainToResultChain(backTmpChain, resultChain, rc.getMapId());
                    reversed = true;
                }
            }
        }

        return resultChain;
    }

    /**
     * 将倒走暂存区的点位全部翻转加入到正走队列中
     *
     * @param backTmpChain 倒走缓存区
     * @param resultChain  结果队列
     * @param mapId        地图ID
     */
    private static void __reverseBackwardTmpChainToResultChain(@NonNull TaskDirectiveChain backTmpChain, @NonNull TaskDirectiveChain resultChain, long mapId) {
        if (backTmpChain.isEmpty()) {
            return;
        }

        // 1.先倒着到目的地，中间不停，到最后点才开始工作
        PathChain<IPath> directToLastPoint = new PathChain<>();
        backTmpChain.forEach(btc -> directToLastPoint.append(btc.getPathChain()));

        resultChain.add(new TaskDirective(
                backTmpChain.getHead().getStart(),
                backTmpChain.getTail().getTaskNode(),
                directToLastPoint));

        // 2.然后在翻转，正着走倒走暂存区的各个点
        // 2.1.倒走缓存区的多个任务点
        for (int i = backTmpChain.size() - 1; i >= 0; i--) {
            TaskDirective td = backTmpChain.get(i);
            TaskDirective preTd = i > 0 ? backTmpChain.get(i - 1) : null;

            // backToForward是要将路径全部正过来
            // 除非backTmpChain.size()==1，否则就要……
            if (preTd != null) {
                final PathChain<IPath> backToForward = new PathChain<>();

                td.getPathChain().forEach(path -> {
                    IPath reversePath = MapContext.getReversePath(path, mapId);
                    if (reversePath == null) {
                        throw new ServiceException("路径[" + path.getXmapId() + "]不是双向路径！");
                    }

                    backToForward.add(reversePath);
                });

                // 2.2.将路径调整到正向后还要翻转顺序加入到结果队列中
                backToForward.reverse();

                resultChain.add(new TaskDirective(
                        td.getTaskNode().getMapPoint(),
                        preTd.getTaskNode(),
                        backToForward));
            }
        }

        // 3.最后清空倒走暂存区
        backTmpChain.clear();
    }

    // endregion

    // region 👇算法辅助类

    // region 👇任务排点算法辅助类

    /**
     * 用于在多节点分支判断的线程中整体传递数据，是线程安全的
     */
    private static class PlanTaskHolder {

        MapContext mc;

        /**
         * 记录每条路线的每一等级的长度
         */
        final Map<String, SimpleChain<LevelLength>> levelLengthMap = new ConcurrentHashMap<>();
        /**
         * 每条路线的线程短路标记
         */
        final Map<String, Boolean> shortCircuitMap = new ConcurrentHashMap<>();

        /**
         * 每条路线已包含的任务节点顺序
         */
        final Map<String, TaskChain<TaskNode>> taskNodeMap = new ConcurrentHashMap<>();

        /**
         * 每条路线未包含的任务节点
         */
        final Map<String, List<TaskNode>> waittingNodeMap = new ConcurrentHashMap<>();

        public PlanTaskHolder(MapContext mc) {
            this.mc = mc;
        }

        /**
         * 生成路线key
         *
         * @param parent   父节点key
         * @param taskNode 当前任务节点
         * @return 路线key
         */
        @NonNull
        String generateMapKey(String parent, @NonNull TaskNode taskNode) {
            return parent + "_" + taskNode.hashCode();
        }

        /**
         * 获取总长度
         *
         * @param mapKey 路线key
         * @return 总长度
         */
        double getTotalLength(String mapKey) {
            ChainBase<LevelLength> chain = levelLengthMap.get(mapKey);
            if (chain == null) return 0.0;
            double result = 0;
            for (int i = 0; i < chain.size(); i++) {
                result += chain.get(i).length;
            }
            return result;
        }

        /**
         * 通知层数=level的其他线程，总长度更长，可以停止了
         *
         * @param level 当前层数
         * @param key   当前路线key
         * @param len   当前总长度
         */
        public void doShortCircuit(int level, String key, double len) {
            levelLengthMap.forEach((k, c) -> {
                if (k.equals(key) || c.size() < level + 1 || shortCircuitMap.get(k)) return;
                if (c.get(level).length > len) {
                    shortCircuitMap.put(k, true);
                }
            });
        }
    }

    /**
     * 每一个分支如果成功到达末点则返回这个结构体
     */
    private static class SuccessChain {
        final String key;
        final TaskChain<TaskNode> taskChain;
        final double totalLength;

        SuccessChain(String key, TaskChain<TaskNode> taskChain, double totalLength) {
            this.key = key;
            this.taskChain = taskChain;
            this.totalLength = totalLength;
        }
    }

    /**
     * 用于SimpleChain中的泛型参数，记录当前路径链的每一个层数及其长度
     */
    static class LevelLength {
        volatile int level;
        double length;

        LevelLength(int level, double length) {
            this.level = level;
            this.length = length;
        }
    }

    // endregion

    // endregion

}
