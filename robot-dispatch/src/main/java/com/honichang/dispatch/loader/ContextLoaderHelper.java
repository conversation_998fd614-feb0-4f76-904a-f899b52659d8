package com.honichang.dispatch.loader;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableMap;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.Position;
import com.honichang.netty.service.IndustrialControlService;
import com.honichang.point.domain.MapMain;
import com.honichang.point.domain.Robot;
import com.honichang.point.service.MapMainService;
import com.honichang.point.service.RobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ContextLoaderHelper {

    @Resource
    private MapMainService mapMainService;
    @Resource
    private IndustrialControlService industrialControlService;
    @Resource
    private RobotService robotService;
    @Resource
    private ApplicationEventPublisher eventPublisher;


    public void loadContexts() {
        log.info("开始装载调度上下文数据...");
        try {
            if (__loadMapContexts()) {
                __loadRobotContexts();
                ContextLoader.isLoaded = true;
            }
        } catch (Exception e) {
            log.error("装载调度上下文数据失败", e);
            throw e;
        }
    }


    /**
     * 装载地图上下文
     *
     * @return true:成功，false:失败
     */
    private boolean __loadMapContexts() {
        log.info("开始装载地图上下文...");

        // 1. 装载地图基本信息
        List<MapMain> mapMains = mapMainService.list(new LambdaQueryWrapper<MapMain>()
                .eq(MapMain::getDelFlag, "0")
                .isNotNull(MapMain::getXmapWidth)
                .orderByDesc(MapMain::getUpdateTime));
        if (mapMains == null || mapMains.isEmpty()) {
            log.warn("未找到有效地图，跳过地图上下文装载");
            return false;
        }

        // 2. 装载地图详细信息
        for (MapMain mapMain : mapMains) {
            MapContext mapContext;
            if (MapContext.exists(mapMain.getId())) {
                mapContext = MapContext.get(mapMain.getId());
            } else {
                mapContext = new MapContext();
                mapContext.setMapId(mapMain.getId());
                MapContext.put(mapContext);
            }

            mapContext.setName(mapMain.getMapName());
            mapContext.setWidth(mapMain.getXmapWidth());
            mapContext.setHeight(mapMain.getXmapHeight());

            // 设置地图边界
            Position minPos = new Position("MIN_POS", mapMain.getMinPosX(), mapMain.getMinPosY(), 0.0, "", "左下角坐标", false, null);
            Position maxPos = new Position("MAX_POS", mapMain.getMaxPosX(), mapMain.getMaxPosY(), 0.0, "", "右上角坐标", false, null);
            mapContext.setMinPos(minPos);
            mapContext.setMaxPos(maxPos);

            // 装载明细
            mapMainService.loadPointsAndPaths(mapContext);

            // 自动计算哪些点是十字路口
            mapContext.calcCrossroads();

            log.info("装载地图[{}]完成，点位数量：{}，路径数量：{}，障碍数量：{}",
                    mapContext.getName(),
                    mapContext.getPoints() != null ? mapContext.getPoints().size() : 0,
                    mapContext.getPaths() != null ? mapContext.getPaths().size() : 0,
                    mapContext.obstacleSize());
        }

        log.info("地图上下文装载完成，共装载{}个地图", MapContext.size());
        return true;
    }

    private void __loadRobotContexts() {
        List<Robot> robots = robotService.getRobotList();
        robots.forEach(this::loadRobotContexts);
        // 初始化好后统一发送事件
        for (Robot robot : robots) {
            // 发布机器人初始化事件: RobotStatusEventHandler
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.ROBOT_INIT_EVENT,
                    "ContextLoader",
                    ImmutableMap.of("robotId", robot.getId()),
                    true));
        }
    }

    /**
     * 装载机器人上下文<p/>
     * 添加新的机器人时调用
     *
     * @param robot 机器人
     */
    public void loadRobotContexts(Robot robot) {
        RobotContext rc;
        if (!RobotContext.exists(robot.getId())) {
            rc = new RobotContext();
            rc.setRobotId(robot.getId());
            // 👇基本信息
            rc.initCommonProperties(robot);
            RobotContext.put(rc);
        } else {
            rc = RobotContext.get(robot.getId());
            // 👇基本信息
            rc.initCommonProperties(robot);
        }

        // 👇连接到下位机
        // 由netty自动获取机器人在线状态及位置状态
        // 并回调更新机器人上下文
        industrialControlService.robotConnection(robot.getId(), robot.getIp(), Integer.parseInt(robot.getPort()));

        log.debug("装载机器人[{}]上下文完成", rc.getRobotName());
    }

}
