package com.honichang.dispatch.loader;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.utils.NumberUtil;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.MapMain;
import com.honichang.point.domain.Robot;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.MapMainService;
import com.honichang.point.service.RobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 上下文装载器
 * 在服务启动时从数据库装载MapContext和RobotContext到内存缓存
 *
 * <AUTHOR>
 */
@Component
@Order(1) // 确保在其他组件之前执行
@Slf4j
public class ContextLoader implements ApplicationRunner {

    @Resource
    private MapMainService mapMainService;

    @Resource
    private RobotService robotService;

    @Resource
    private BizPointService bizPointService;

    @Override
    public void run(ApplicationArguments args) {
        log.info("开始装载调度上下文数据...");

        try {
            // 1. 装载地图上下文
            loadMapContexts();

            // 2. 装载机器人上下文
            loadRobotContexts();

            log.info("调度上下文数据装载完成");
        } catch (Exception e) {
            log.error("装载调度上下文数据失败", e);
            throw e;
        }
    }

    /**
     * 装载地图上下文
     */
    public void loadMapContexts() {
        log.info("开始装载地图上下文...");

        // 1. 装载地图基本信息
        List<MapMain> mapMains = mapMainService.list(new LambdaQueryWrapper<MapMain>()
                .eq(MapMain::getDelFlag, "0")
                .isNotNull(MapMain::getXmapWidth)
                .orderByDesc(MapMain::getUpdateTime));
        if (mapMains == null || mapMains.isEmpty()) {
            log.warn("未找到有效地图，跳过地图上下文装载");
            return;
        }

        // 2. 装载地图详细信息
        for (MapMain mapMain : mapMains) {
            MapContext mapContext = MapContext.get(mapMain.getId());
            if (mapContext == null) {
                mapContext = new MapContext();
                mapContext.setMapId(mapMain.getId());
                MapContext.put(mapContext);
            }
            mapContext.setName(mapMain.getMapName());
            mapContext.setWidth(mapMain.getXmapWidth());
            mapContext.setHeight(mapMain.getXmapHeight());

            // 设置地图边界
            Position minPos = new Position("MIN_POS", mapMain.getMinPosX(), mapMain.getMinPosY(), 0.0, "", "左下角坐标");
            Position maxPos = new Position("MAX_POS", mapMain.getMaxPosX(), mapMain.getMaxPosY(), 0.0, "", "右上角坐标");
            mapContext.setMinPos(minPos);
            mapContext.setMaxPos(maxPos);

            // 装载明细
            mapMainService.loadPointsAndPaths(mapContext);

            log.info("装载地图[{}]完成，点位数量：{}，路径数量：{}，障碍数量：{}",
                    mapContext.getName(),
                    mapContext.getPoints() != null ? mapContext.getPoints().size() : 0,
                    mapContext.getPaths() != null ? mapContext.getPaths().size() : 0,
                    mapContext.getObstacles() != null ? mapContext.getObstacles().size() : 0);
        }

        log.info("地图上下文装载完成，共装载{}个地图", MapContext.size());
    }

    /**
     * 装载机器人上下文
     */
    private void loadRobotContexts() {
        log.info("开始装载机器人上下文...");

        List<Robot> robots = robotService.getRobotList();

        for (Robot robot : robots) {
            RobotContext robotContext;
            if (!RobotContext.exists(robot.getId())) {
                robotContext = new RobotContext();
                robotContext.setRobotId(robot.getId());
                RobotContext.put(robotContext);
            } else {
                robotContext = RobotContext.get(robot.getId());
            }

            // region 👇基本信息
            robotContext.setMapId(robot.getMapId());
            robotContext.setRobotName(robot.getRobotName());
            robotContext.setSn(robot.getSn());
            robotContext.setIp(robot.getIp());
            robotContext.setPort(Integer.parseInt(robot.getPort()));
            robotContext.setAppCode(robot.getAppCode());
            robotContext.setPriority(robot.getPriority());
            // endregion

            // 位置信息
            if (robot.getRobotDynamicAttr() != null) {
                // region 👇机器人所在点位
                String xmapPointId = robot.getRobotDynamicAttr().getXmapPointId();
                if (StringUtils.isEmpty(xmapPointId) || xmapPointId.equals("0")) {
                    // 不在点位上
                    robotContext.setPosition(new Position(
                            NumberUtil.defaultDouble(robot.getRobotDynamicAttr().getPosX()),
                            NumberUtil.defaultDouble(robot.getRobotDynamicAttr().getPosY())));
                } else {
                    // 在点位上
                    // 从redis获取
                    List<BizPoint> bizPoints = bizPointService.getByXmapId(xmapPointId, robotContext.getMapId());
                    BizPoint p = bizPoints.stream()
                            .filter(x -> PointClassNameEnum.CROSSROADS.getCode().equals(x.getClassName()))
                            .findFirst().orElse(null);
                    if (p == null) p = bizPoints.isEmpty() ? null : bizPoints.get(0);

                    robotContext.setPosition(new Position(
                            xmapPointId,
                            NumberUtil.defaultDouble(robot.getRobotDynamicAttr().getPosX()),
                            NumberUtil.defaultDouble(robot.getRobotDynamicAttr().getPosY()),
                            robot.getRobotDynamicAttr().getTheta(),
                            p != null ? p.getClassName() : "",
                            p != null ? p.getInstanceName() : ""
                    ));
                }
                robotContext.setTheta(NumberUtil.defaultDouble(robot.getRobotDynamicAttr().getTheta()));
                // endregion

                // region 👇机器人所在路径
                Long pathId = robot.getRobotDynamicAttr().getPathId();
                if (pathId != null) {
                    robotContext.setCurrentPath(MapContext.getPath(pathId, robotContext.getMapId()));
                }
                // endregion

                // region 👇机器人任务
                robotContext.setLatestTaskClass(robot.getRobotDynamicAttr().getLatestTaskClass());
                robotContext.setLatestTaskId(robot.getRobotDynamicAttr().getLatestTaskId());
                // endregion

                // region 👇机器人状态
                robotContext.setTotalRunning(robot.getRobotDynamicAttr().getTotalRunning());
                robotContext.setTotalOdom(robot.getRobotDynamicAttr().getTotalOdom());
                robotContext.setBattery(null);
                robotContext.setStatus(robot.getStatus());
                robotContext.setWorkStatus(robot.getWorkStatus());
                robotContext.setWorkMode(robot.getWorkMode());
                // endregion
            }

            log.debug("装载机器人[{}]上下文完成", robotContext.getRobotName());
        }

//        log.info("机器人上下文装载完成，共装载{}个机器人", RobotContext.size());
    }
}
