package com.honichang.dispatch.loader;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 上下文装载器
 * 在服务启动时从数据库装载MapContext和RobotContext到内存缓存
 *
 * <AUTHOR>
 */
@Component
@Order(1) // 确保在其他组件之前执行
@Slf4j
public class ContextLoader implements ApplicationRunner {

    private static volatile boolean isFirstRun = true;

    @Getter
    static volatile boolean isLoaded = false;

    public static boolean isReady() {
        return !isFirstRun && isLoaded;
    }

    public static void initDone() {
        isFirstRun = false;
    }


    @Resource
    private ContextLoaderHelper contextLoaderHelper;

    @Override
    public void run(ApplicationArguments args) {
        contextLoaderHelper.loadContexts();
    }

}
