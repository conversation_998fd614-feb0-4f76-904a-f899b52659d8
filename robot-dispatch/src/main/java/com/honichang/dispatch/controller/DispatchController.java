package com.honichang.dispatch.controller;

import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispatch.helper.CalcRobotPathHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 调度控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dispatch")
@Slf4j
public class DispatchController extends BaseController {

    @Resource
    private DispatchService dispatchService;

    // ========== 查找点位相关接口 ==========

    /**
     * 查找机器人当前位置的最近点位
     */
    @GetMapping("/getNearestPoint/{robotId}")
    public AjaxResult getNearestPointByRobot(@PathVariable long robotId) {
        try {
            IPosition position = CalcRobotPathHelper.getNearestPoint(robotId);
            return success(position.getXmapId());
        } catch (Exception e) {
            log.error("查找机器人[{}]最近点位异常", robotId, e);
            return error("查找最近点位失败: " + e.getMessage());
        }
    }

    /**
     * 查找机器人背后的最近点位
     */
    @GetMapping("/getNearestPointBackward/{robotId}")
    public AjaxResult getNearestPointBackward(@PathVariable long robotId) {
        try {
            IPosition nearestPoint = CalcRobotPathHelper.getNearestPointBackward(robotId);
            return success(nearestPoint);
        } catch (Exception e) {
            log.error("查找机器人[{}]背后最近点位异常", robotId, e);
            return error("查找背后最近点位失败: " + e.getMessage());
        }
    }

    // ========== 组合控制（遥控模式）相关接口 ==========

    /**
     * 切换工作模式
     */
    @PostMapping("/switchWorkMode")
    public AjaxResult switchWorkMode(@RequestParam long robotId,
                                     @RequestParam int workMode,
                                     @RequestParam(defaultValue = "true") boolean autoTimeout) {
        try {
            RobotWorkModeEnum modeEnum = RobotWorkModeEnum.fromCode(workMode);
            boolean result = dispatchService.switchWorkMode(robotId, modeEnum, !autoTimeout);
            return toAjax(result);
        } catch (Exception e) {
            log.error("切换机器人[{}]工作模式异常", robotId, e);
            return error("切换工作模式失败: " + e.getMessage());
        }
    }

    /**
     * 前进到最近点位
     */
    @PostMapping("/forwardStepPoint/{robotId}")
    public AjaxResult forwardStepPoint(@PathVariable long robotId) {
        try {
            String result = dispatchService.forwardStepPoint(robotId);
            return success(result);
        } catch (Exception e) {
            log.error("机器人[{}]前进到最近点位异常", robotId, e);
            return error("前进到最近点位失败: " + e.getMessage());
        }
    }

    /**
     * 后退到最近点位
     */
    @PostMapping("/backwardStepPoint/{robotId}")
    public AjaxResult backwardStepPoint(@PathVariable long robotId) {
        try {
            String result = dispatchService.backwardStepPoint(robotId);
            return success(result);
        } catch (Exception e) {
            log.error("机器人[{}]后退到最近点位异常", robotId, e);
            return error("后退到最近点位失败: " + e.getMessage());
        }
    }

    // ========== 调度（非手动模式）相关接口 ==========

    /**
     * 走一步
     */
    @PostMapping("/runStepPoint")
    public AjaxResult runStepPoint(@RequestParam long robotId,
                                   @RequestParam String pathId,
                                   @RequestParam String startPointId,
                                   @RequestParam String endPointId,
                                   @RequestParam int direction) {
        try {
            boolean result = dispatchService.runStepPoint(robotId, pathId, startPointId, endPointId, direction);
            return toAjax(result);
        } catch (Exception e) {
            log.error("机器人[{}]走一步异常", robotId, e);
            return error("走一步失败: " + e.getMessage());
        }
    }

    /**
     * 走百分比路径
     */
    @PostMapping("/runStepPercent")
    public AjaxResult runStepPercent(@RequestParam long robotId,
                                     @RequestParam String pathId,
                                     @RequestParam String startPointId,
                                     @RequestParam String endPointId,
                                     @RequestParam double percent,
                                     @RequestParam int direction) {
        try {
            boolean result = dispatchService.runStepPercent(robotId, pathId, startPointId, endPointId, percent, direction);
            return toAjax(result);
        } catch (Exception e) {
            log.error("机器人[{}]走百分比路径异常", robotId, e);
            return error("走百分比路径失败: " + e.getMessage());
        }
    }

    /**
     * 运行到指定点位
     */
    @PostMapping("/runToPoint")
    public AjaxResult runToPoint(@RequestParam long robotId, @RequestParam String pointId) {
        try {
            boolean result = dispatchService.runToPoint(robotId, pointId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("机器人[{}]运行到点位[{}]异常", robotId, pointId, e);
            return error("运行到指定点位失败: " + e.getMessage());
        }
    }

    // ========== 路径规划相关接口 ==========

    /**
     * 查找最近的可用充电桩
     */
    @GetMapping("/findNearestAvailableCharger/{robotId}")
    public AjaxResult findNearestAvailableCharger(@PathVariable long robotId) {
        try {
            Long chargerId = dispatchService.findNearestAvailableCharger(robotId);
            return success(chargerId);
        } catch (Exception e) {
            log.error("查找机器人[{}]最近可用充电桩异常", robotId, e);
            return error("查找最近可用充电桩失败: " + e.getMessage());
        }
    }

    /**
     * 查找排队最少的充电桩
     */
    @GetMapping("/findLeastChargerQueue/{robotId}")
    public AjaxResult findLeastChargerQueue(@PathVariable long robotId) {
        try {
            Long chargerId = dispatchService.findLeastChargerQueue(robotId);
            return success(chargerId);
        } catch (Exception e) {
            log.error("查找机器人[{}]排队最少充电桩异常", robotId, e);
            return error("查找排队最少充电桩失败: " + e.getMessage());
        }
    }

    /**
     * 查找最近的可用停车位
     */
    @GetMapping("/findNearestAvailableParking")
    public AjaxResult findNearestAvailableParking(@RequestParam long robotId, @RequestParam String pointId) {
        try {
            Long parkingId = dispatchService.findNearestAvailableParking(robotId, pointId);
            return success(parkingId);
        } catch (Exception e) {
            log.error("查找机器人[{}]最近可用停车位异常", robotId, e);
            return error("查找最近可用停车位失败: " + e.getMessage());
        }
    }

    // ========== 机器人上下文管理接口 ==========

    /**
     * 获取机器人上下文
     */
    @GetMapping("/getRobotContext/{robotId}")
    public AjaxResult getRobotContext(@PathVariable long robotId) {
        try {
            RobotContext robotContext = dispatchService.getRobotContext(robotId);
            return success(robotContext);
        } catch (Exception e) {
            log.error("获取机器人[{}]上下文异常", robotId, e);
            return error("获取机器人上下文失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有机器人上下文
     */
    @GetMapping("/getAllRobotContexts")
    public AjaxResult getAllRobotContexts() {
        try {
            Collection<RobotContext> robotContexts = dispatchService.getAllRobotContexts();
            return success(robotContexts);
        } catch (Exception e) {
            log.error("获取所有机器人上下文异常", e);
            return error("获取所有机器人上下文失败: " + e.getMessage());
        }
    }
}
