package com.honichang.dispatch.controller;

import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.dispatch.context.TaskEscortContext;
import com.honichang.dispatch.service.EscortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 陪同任务控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dispatch/escort")
@Slf4j
public class EscortController extends BaseController {
    
    @Resource
    private EscortService escortService;
    
    /**
     * 陪同任务立即执行
     */
    @PostMapping("/run/{taskEscortId}")
    public AjaxResult escortRun(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.escortRun(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]立即执行异常", taskEscortId, e);
            return error("陪同任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 访客到达起始点确认
     */
    @PostMapping("/confirmVisit/{taskEscortId}")
    public AjaxResult escortConfirmVisit(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.escortConfirmVisit(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]访客确认到达异常", taskEscortId, e);
            return error("访客确认到达失败: " + e.getMessage());
        }
    }
    
    /**
     * 访客在目的地完成工作后确认
     */
    @PostMapping("/confirmComplete/{taskEscortId}")
    public AjaxResult escortConfirmComplete(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.escortConfirmComplete(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]访客确认完成异常", taskEscortId, e);
            return error("访客确认完成失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭报警（暂停访客距离逻辑判断）
     */
    @PostMapping("/pauseAlarm/{taskEscortId}")
    public AjaxResult escortPauseAlarm(@PathVariable long taskEscortId, @RequestParam int pauseMin) {
        try {
            boolean result = escortService.escortPauseAlarm(taskEscortId, pauseMin);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]关闭报警异常", taskEscortId, e);
            return error("关闭报警失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消关闭报警（恢复访客距离逻辑判断）
     */
    @PostMapping("/restoreAlarm/{taskEscortId}")
    public AjaxResult escortRestoreAlarm(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.escortRestoreAlarm(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]恢复报警异常", taskEscortId, e);
            return error("恢复报警失败: " + e.getMessage());
        }
    }
    
    /**
     * 陪同任务异常终止
     */
    @PostMapping("/abort/{taskEscortId}")
    public AjaxResult escortAbort(@PathVariable long taskEscortId, @RequestParam String reason) {
        try {
            boolean result = escortService.escortAbort(taskEscortId, reason);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]异常终止异常", taskEscortId, e);
            return error("陪同任务异常终止失败: " + e.getMessage());
        }
    }
    
    /**
     * 访客到达出口确认，陪同任务完成
     */
    @PostMapping("/completeLeave/{taskEscortId}")
    public AjaxResult escortCompleteLeave(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.escortCompleteLeave(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("陪同任务[{}]完成离开异常", taskEscortId, e);
            return error("陪同任务完成失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取陪同任务上下文
     */
    @GetMapping("/getContext/{taskEscortId}")
    public AjaxResult getEscortContext(@PathVariable long taskEscortId) {
        try {
            TaskEscortContext escortContext = escortService.getEscortContext(taskEscortId);
            return success(escortContext);
        } catch (Exception e) {
            log.error("获取陪同任务[{}]上下文异常", taskEscortId, e);
            return error("获取陪同任务上下文失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建陪同任务上下文
     */
    @PostMapping("/createContext")
    public AjaxResult createEscortContext(@RequestBody TaskEscortContext taskEscortContext) {
        try {
            boolean result = escortService.createEscortContext(taskEscortContext);
            return toAjax(result);
        } catch (Exception e) {
            log.error("创建陪同任务上下文异常", e);
            return error("创建陪同任务上下文失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新陪同任务上下文
     */
    @PostMapping("/updateContext")
    public AjaxResult updateEscortContext(@RequestBody TaskEscortContext taskEscortContext) {
        try {
            escortService.updateEscortContext(taskEscortContext);
            return success();
        } catch (Exception e) {
            log.error("更新陪同任务上下文异常", e);
            return error("更新陪同任务上下文失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除陪同任务上下文
     */
    @DeleteMapping("/deleteContext/{taskEscortId}")
    public AjaxResult deleteEscortContext(@PathVariable long taskEscortId) {
        try {
            boolean result = escortService.deleteEscortContext(taskEscortId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除陪同任务[{}]上下文异常", taskEscortId, e);
            return error("删除陪同任务上下文失败: " + e.getMessage());
        }
    }
}
