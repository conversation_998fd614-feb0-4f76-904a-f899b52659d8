package com.honichang.dispatch.event.handler;

import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkStatusEnum;
import com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum;
import com.honichang.dispactch.model.enums.TaskEscortStatusEnum;
import com.honichang.dispactch.service.DispatchCommandService;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispatch.event.DispatchEventHandler;
import com.honichang.point.service.TaskEscortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.honichang.dispactch.event.DispatchEventType.*;
import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.GOTO_EXIT;
import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.GOTO_VISITOR;

/**
 * 陪同事件处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EscortEventHandler implements DispatchEventHandler {

    @Resource
    private TaskService taskService;
    @Resource
    private TaskEscortService taskEscortService;
    @Resource
    private DispatchService dispatchService;
    @Resource
    private DispatchCommandService dispatchCommandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleEvent(DispatchEvent event) {
        switch (event.getEventType()) {
            case TO_ESCORT_EVENT:
                // 由充电完成事件触发
                // 可能直接去接人，也可能去出口附件的停车位等待，根据时间判断
                handleToEscortEvent(event);
                return;
            case ARRIVE_WAIT_POINT_EVENT:
                TaskEscortStatusEnum status = event.getPayloadValue("status", TaskEscortStatusEnum.class);
                if (status == GOTO_VISITOR) {
                    // 到达等待点，由状态回调触发
                    // 更新状态，然后由CoreScheduler去处理访客超时
                    handleArriveWaitPointEvent(event);
                } else if (status == GOTO_EXIT) {
                    // 送访客事件
                    handleArriveExitEvent(event);
                }
                return;
            case ARRIVE_VISITOR_EVENT:
                // 接到访客事件
                // 由工控UWB卡签到回调触发
                handleArriveVisitorEvent(event);
                return;
            case ESCORT_ABORT_EVENT:
                // 等待访客超时，终止陪同任务
                // 由CoreScheduler计时超时时间触发
                handleEscortAbortEvent(event);
                return;
            case ARRIVE_TARGET_POINT_EVENT:
                // 到达目标点事件，由状态回调触发
                handleArriveTargetPointEvent(event);
                return;
            default:
                throw new UnsupportedOperationException("Unsupported event type: " + event.getEventType());
        }
    }

    @Override
    public boolean supports(DispatchEventType eventType) {
        return eventType == TO_ESCORT_EVENT ||
                eventType == ARRIVE_WAIT_POINT_EVENT ||
                eventType == ARRIVE_VISITOR_EVENT ||
                eventType == ESCORT_ABORT_EVENT;
    }

    @Override
    public int getPriority() {
        return 30; // 较高优先级
    }

    /**
     * 由充电完成事件触发
     * 可能直接去接人，也可能去出口附件的停车位等待，根据时间判断
     *
     * @param event 事件
     */
    @Override
    public void handleToEscortEvent(DispatchEvent event) {
        Long robotId = event.getRobotId();
        RobotContext rc = RobotContext.get(robotId);

        // 下面的异常基本不可能发生，因为事件触发前已经保证了机器人的状态
        TaskEscortContext te = TaskEscortContext.getByRobot(robotId);
        if (te == null) {
            log.error("Escort task not found for robot: {}", robotId);
            return;
        }

        String reason = taskService.canWorkForEscort(rc);
        if (StringUtils.isNotBlank(reason)) {
            log.error("Escort task can not work for robot {}: {}", rc.getRobotName(), reason);
            return;
        }

        // 判断时间，如果在15分钟内，直接去接人
        // 否则去接人点附件的停车位
        if (te.getTaskEscort().getExecTime().getTime() - System.currentTimeMillis() < 15 * 60 * 1000) {
            // 直接去接人
            try {
                taskEscortService.startToVisit(robotId, te.getTaskEscort());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                // ignore
            }
        } else {
            // 去接人点附件的停车位
            dispatchService.navigateToParking(robotId, te.getWaitPosition().getXmapId());
        }

        rc.setWorkStatus(RobotWorkStatusEnum.ESCORT_TASK);
        rc.setStatus(RobotStatusEnum.WORKING);
    }

    /**
     * 到达等待点事件
     */
    @Override
    public void handleArriveWaitPointEvent(DispatchEvent event) {
        // 播放语音：我在等你
        try {
            dispatchCommandService.playVoice(event.getRobotId(), 2);
        } catch (Exception e) {
            // ignore
        }
        // 抵达等待点，更新状态，然后由CoreScheduler去处理访客超时
        taskEscortService.arriveWaitPoint(event.getTaskEscortId());
    }

    /**
     * 送访客抵达出口事件
     * 由工控状态路径走完回调触发
     */
    @Override
    public void handleArriveExitEvent(DispatchEvent event) {
        // 播放语音：6任务结束
        try {
            dispatchCommandService.playVoice(event.getRobotId(), 6);
        } catch (Exception e) {
            // ignore
        }
        // 抵达出口，更新状态，然后由工控判断访客UWB距离离开，或工控回调按钮点击
        taskEscortService.arriveExitPoint(event.getTaskEscortId());
    }

    /**
     * 接到访客事件
     * 由工控UWB卡签到回调触发
     */
    @Override
    public void handleArriveVisitorEvent(DispatchEvent event) {
        // 暂停语音
        try {
            dispatchCommandService.pauseVoice(event.getRobotId());
        } catch (Exception e) {
            // ignore: 不可打断下面的状态转换
        }
        // 计算路线并导航到目的地
        if (taskEscortService.escortConfirmVisit(event.getRobotId(), event.getTaskEscortId())) {
            // 播放语音：7请跟随
            dispatchCommandService.playVoice(event.getRobotId(), 7);
        }
    }

    /**
     * 由CoreScheduler计时超时时间
     */
    @Override
    public void handleEscortAbortEvent(DispatchEvent event) {
        Long robotId = event.getRobotId();
        String reason = event.getPayloadValue("reason", String.class);
        TaskEscortPushTemplateEnum temp = event.getPayloadValue("pushTemplate", TaskEscortPushTemplateEnum.class);
        taskEscortService.abortBySchedule(robotId, reason, temp);
    }

    /**
     * 到达目标点事件，由状态回调触发
     *
     * @param event 事件
     */
    public void handleArriveTargetPointEvent(DispatchEvent event) {
        // 播放语音：3访客到达目的地
        try {
            dispatchCommandService.playVoice(event.getRobotId(), 3);
        } catch (Exception e) {
            // ignore: 不可打断下面的状态转换
        }
        // 抵达目标点，更新状态
        taskEscortService.arriveTargetPoint(event.getTaskEscortId());
    }

}