package com.honichang.dispatch.event;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 调度事件数据类
 * 
 * <AUTHOR>
 */
@Data
public class DispatchEvent {
    
    /** 事件ID **/
    private String eventId;
    
    /** 事件类型 **/
    private DispatchEventType eventType;
    
    /** 事件源（通常是robotId或taskId） **/
    private String source;
    
    /** 事件负载数据 **/
    private Map<String, Object> payload;
    
    /** 事件创建时间 **/
    private Date createTime;
    
    /** 事件处理时间 **/
    private Date processTime;
    
    /** 事件状态：PENDING待处理 | PROCESSING处理中 | COMPLETED已完成 | FAILED失败 **/
    private String status;
    
    /** 处理结果 **/
    private String result;
    
    /** 错误信息 **/
    private String errorMessage;
    
    /** 重试次数 **/
    private int retryCount;
    
    /** 最大重试次数 **/
    private int maxRetryCount;
    
    /** 是否异步处理 **/
    private boolean async;
    
    /** 事件优先级：1最高 - 10最低 **/
    private int priority;
    
    public DispatchEvent() {
        this.createTime = new Date();
        this.status = "PENDING";
        this.retryCount = 0;
        this.maxRetryCount = 3;
        this.async = true;
        this.priority = 5;
    }
    
    public DispatchEvent(DispatchEventType eventType, String source, Map<String, Object> payload) {
        this();
        this.eventType = eventType;
        this.source = source;
        this.payload = payload;
        this.eventId = generateEventId();
    }
    
    /**
     * 生成事件ID
     */
    private String generateEventId() {
        return eventType.getCode() + "_" + source + "_" + System.currentTimeMillis();
    }
    
    /**
     * 获取负载中的值
     */
    @SuppressWarnings("unchecked")
    public <T> T getPayloadValue(String key, Class<T> type) {
        if (payload == null) {
            return null;
        }
        Object value = payload.get(key);
        if (value == null) {
            return null;
        }
        if (type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 获取robotId
     */
    public Long getRobotId() {
        return getPayloadValue("robotId", Long.class);
    }
    
    /**
     * 获取taskId
     */
    public Long getTaskId() {
        return getPayloadValue("taskId", Long.class);
    }
    
    /**
     * 获取taskEscortId
     */
    public Long getTaskEscortId() {
        return getPayloadValue("taskEscortId", Long.class);
    }
    
    /**
     * 标记事件为处理中
     */
    public void markAsProcessing() {
        this.status = "PROCESSING";
        this.processTime = new Date();
    }
    
    /**
     * 标记事件为已完成
     */
    public void markAsCompleted(String result) {
        this.status = "COMPLETED";
        this.result = result;
    }
    
    /**
     * 标记事件为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = "FAILED";
        this.errorMessage = errorMessage;
        this.retryCount++;
    }
    
    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && "FAILED".equals(status);
    }
    
    /**
     * 重置为待处理状态（用于重试）
     */
    public void resetForRetry() {
        this.status = "PENDING";
        this.processTime = null;
        this.errorMessage = null;
    }
}
