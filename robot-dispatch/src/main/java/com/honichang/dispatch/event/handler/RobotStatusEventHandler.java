package com.honichang.dispatch.event.handler;

import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.enums.*;
import com.honichang.dispactch.util.LogUtil;
import com.honichang.dispatch.event.DispatchEventHandler;
import com.honichang.point.service.TaskEscortService;
import com.honichang.point.service.TaskInstanceService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum.ESCORT_TEMP_ABORT;

@Component
public class RobotStatusEventHandler implements DispatchEventHandler {

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    private TaskEscortService taskEscortService;


    @Override
    public void handleEvent(DispatchEvent event) {
        if (event.getEventType() == DispatchEventType.ROBOT_INIT_EVENT) {
            // 初始化事件
            // 机器人上下文已经从DB中加载完，且调用了robotConnection函数尝试连接
            handleRobotInitEvent(event);
            return;
        } else if (event.getEventType() == DispatchEventType.ROBOT_STATUS_UPDATE_EVENT) {
            // 任务状态变更事件
            handleRobotStatusUpdateEvent(event);
            return;
        } else if (event.getEventType() == DispatchEventType.ROBOT_ONLINE_STATUS_UPDATE_EVENT) {
            // 在线状态变更事件
            handleRobotOnlineStatusUpdateEvent(event);
            return;
        } else if (event.getEventType() == DispatchEventType.ROBOT_WORK_MODE_UPDATE_EVENT) {
            // 工作模式切换
            handleRobotWorkModeUpdateEvent(event);
            return;
        }
        throw new ServiceException("不支持的事件类型: " + event.getEventType());
    }

    @Override
    public boolean supports(DispatchEventType eventType) {
        return eventType == DispatchEventType.ROBOT_STATUS_UPDATE_EVENT ||
                eventType == DispatchEventType.ROBOT_INIT_EVENT ||
                eventType == DispatchEventType.ROBOT_ONLINE_STATUS_UPDATE_EVENT ||
                eventType == DispatchEventType.ROBOT_WORK_MODE_UPDATE_EVENT;
    }

    @Override
    public int getPriority() {
        return 0; // 最高优先级
    }

    @Override
    public void handleRobotStatusUpdateEvent(DispatchEvent event) {
        RobotContext rc = RobotContext.get(event.getRobotId());
        RobotStatusEnum newState = event.getPayloadValue("status", RobotStatusEnum.class);
        RobotStatusEnum oldState = rc.getStatus();
        String reason = event.getPayloadValue("reason", String.class);
        // 不需要更新则什么都不做
        if (rc.setStatus(newState) == StatusTransResultEnum.NO_NEED) return;

        switch (newState) {
            case CAN_NOT_NAVIGATION:
                // 无法导航，可能的故障有地图未加载、定位中、定位失败、置信度不足75%
                // 而不包含单纯的导航失败，导航失败是单次导航的问题，是路径下发的问题
                // *写日志
                LogUtil.addRobotLog(LogRobotTypeEnum.FAULT, reason, event.getRobotId(), "system");
                break;
            case PAUSED:
                // 交由CoreScheduler去处理
                LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, reason == null ? "暂停导航" : reason, event.getRobotId(), SecurityUtils.getUserIdOrSystem());
                break;
            case WORKING:
                // 交由CoreScheduler去处理
                break;
            case STANDBY:
                // 交由CoreScheduler去处理
                break;
            default:
                throw new ServiceException("不支持的机器人状态: " + newState);
        }
    }

    /**
     * 由ContextLoader触发
     * 系统启动时，从DB中读取机器人信息，初始化机器人上下文后触发
     * <p>
     * 初始必定是离线状态，需要等待工控netty回调
     * 回调如果在线会自动触发handleRobotOnlineStatusUpdateEvent
     * <p>
     * 随后工控也会自动回调机器人状态，更新机器人当前信息
     * 并触发handleRobotStatusUpdateEvent
     * <p>
     * 如果是定位中则交由CoreScheduler去处理，直等到定位完成
     * 如果是暂停状态则从数据库中判断当前任务是什么，已经任务到了哪一步，可能需要结合机器人当前点位和路径信息
     * 如果是充电或升级也是交由CoreScheduler去处理，直等到充电或升级完成
     * 如果是任务状态，逻辑等同于暂停
     * 如果是待机状态，则需要判断当前任务队列是否为空，为空则直接待机，不为空则需要结合任务信息走restoreTask流程
     * 其他状态都是交由CoreScheduler去处理
     */
    @Override
    public void handleRobotInitEvent(DispatchEvent event) {
    }

    /**
     * 机器人在线状态更新事件处理
     * 由工控netty端自动触发调用
     *
     * @param event 事件
     */
    @Override
    public void handleRobotOnlineStatusUpdateEvent(DispatchEvent event) {
        Boolean online = event.getPayloadValue("status", Boolean.class);
        // 更新RobotContext
        RobotContext.get(event.getRobotId())
                .setOnline(online);
        // 记录离线时间
        if (online) {
            // 重置离线时间
            RobotContext.get(event.getRobotId()).setOfflineTime(null);
        } else {
            // 记录离线时间
            RobotContext.get(event.getRobotId()).setOfflineTime(DateUtils.getNowDate());
        }
        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.CONNECT,
                event.getPayloadValue("reason", String.class),
                event.getRobotId(), "system");
    }

    /**
     * 机器人工作模式更新事件处理
     *
     * @param event 事件
     */
    @Override
    public void handleRobotWorkModeUpdateEvent(DispatchEvent event) {
        RobotWorkModeEnum newMode = event.getPayloadValue("workMode", RobotWorkModeEnum.class);
        RobotContext rc = RobotContext.get(event.getRobotId());

        rc.setWorkMode(newMode);

        switch (newMode) {
            case MANUAL_MODE:
                // 切到【手动模式】
                // *如果有陪同任务，则终止陪同
                taskEscortService.abortBySchedule(rc.getRobotId(), "切换到手动模式", ESCORT_TEMP_ABORT);
                // *如果有普通任务，则暂停当前任务
                taskInstanceService.abortTask(rc.getRobotId(), "切换到手动模式");

                // *机器人状态=暂停
                rc.setStatus(RobotStatusEnum.PAUSED);
                // *机器人工作状态=待机
                rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);

                // *清空当前一切任务队列、预分配路径、已完成路径、任务指令队列
                rc.getTaskDirectiveChain().clear();
                rc.clearPathChain();
                rc.getTaskQueue().clear();
                break;
            case REMOTE_CONTROL_MODE:
                // 切到【遥控模式】，如果不用自动超时，则设置为null
                boolean autoTimeout = event.getPayloadValue("autoTimeout", Boolean.class);
                rc.setLastRemoteControlTime(autoTimeout ? DateUtils.getNowDate() : null);
                // 超时时间
                // *机器人状态=暂停
                rc.setStatus(RobotStatusEnum.PAUSED);
                // *机器人工作状态=待机
                rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);

                // *如果有普通任务，则暂停当前任务
                taskInstanceService.pauseTask(rc.getRobotId(), "切换到遥控模式");

                // *只清理路径资源，不清理任务队列和任务指令队列
                rc.clearPathChain();
                break;
            case AUTO_MODE:
            default:
                // 切回自动模式，只需要把暂停状态变为待机状态
                // 注意：其他非正常模式不要动
                // 灌入任务与是否工作由CorScheduler决定
                if (rc.getStatus() == RobotStatusEnum.PAUSED) {
                    rc.setStatus(RobotStatusEnum.STANDBY);
                }
                break;
        }


        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL,
                newMode == RobotWorkModeEnum.REMOTE_CONTROL_MODE ? "切换到遥控模式" :
                        (newMode == RobotWorkModeEnum.MANUAL_MODE ? "切换到手动模式" : "切换到自动模式"),
                event.getRobotId(), SecurityUtils.getUserIdOrSystem());
        // 切换回自动模式，机器人下位机的导航任务将会丢失，必须重新下发任务
        // 但是我们没有这个业务场景
        // 只有机器人维修回来后，放入固定点位，执行以下逻辑：
        // 1.机器人开机
        // 2.等待下位机启动完成
        // 3.切换成手动模式
        // 4.执行机器人手动定位
        // 5.查询机器人运行状态：必须返回定位状态=定位完成，否则继续执行4
        // 6.确认机器人位置
        // 7.切换回自动模式
        //
        // 机器人重启也遵循这个逻辑
    }

}
