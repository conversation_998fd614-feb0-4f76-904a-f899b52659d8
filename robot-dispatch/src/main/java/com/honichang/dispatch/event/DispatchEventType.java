package com.honichang.dispatch.event;

/**
 * 调度事件类型枚举
 * 
 * <AUTHOR>
 */
public enum DispatchEventType {
    
    /**
     * 开始充电事件
     * payload：robotId
     * 场景：预约陪同触发或立即陪同、任务中电量低于阈值
     * 非异步事件，要求加事务
     * 行为：pauseCurrentTask() -> toCharge()
     */
    TO_CHARGING_EVENT("toChargingEvent", "开始充电事件"),
    
    /**
     * 完成充电事件
     * payload：robotId
     * 场景：充电桩检测到电量充满
     * 非异步事件，要求加事务
     * 行为：调用机器人api收回充电口（可能没有？） -> restoreTask()
     */
    FINISH_CHARGING_EVENT("finishChargingEvent", "完成充电事件"),
    
    /**
     * 开始陪同事件
     * payload: taskEscortId
     * 场景：预约陪同触发或立即陪同
     * 行为：pauseCurrentTask() -> escortRun()
     */
    TO_ESCORT_EVENT("toEscortEvent", "开始陪同事件"),
    
    /**
     * 访客超出陪同距离事件
     * payload: robotId
     * 场景：uwb检测到访客超出阈值
     * 异步事件，无事务
     */
    OUT_OF_RANGE_ESCORT_EVENT("outOfRangeEscortEvent", "访客超出陪同距离事件"),
    
    /**
     * 到达waitPointId（接客、送客）
     * payload: robotId
     * 场景：机器人到达waitPointId
     * 异步事件，要求加事务
     */
    ARRIVE_WAIT_POINT_EVENT("arriveWaitPointEvent", "到达等待点事件"),
    
    /**
     * 到达arrivePointId（送到目的地）
     * payload: robotId
     * 场景：机器人到达arrivePointId
     * 异步事件，要求加事务
     */
    ARRIVE_TARGET_POINT_EVENT("arriveTargetPointEvent", "到达目标点事件"),
    
    /**
     * 遭遇障碍
     * payload: robotId
     * 场景：机器人遇到障碍
     * 异步事件，要求加事务
     */
    BLOCK_EVENT("blockEvent", "遭遇障碍事件"),
    
    /**
     * 机器人状态更新事件
     * payload: robotId
     * 场景：机器人状态发生变化
     * 异步事件
     */
    ROBOT_STATUS_UPDATE_EVENT("robotStatusUpdateEvent", "机器人状态更新事件"),
    
    /**
     * 任务状态更新事件
     * payload: taskId, robotId
     * 场景：任务状态发生变化
     * 异步事件
     */
    TASK_STATUS_UPDATE_EVENT("taskStatusUpdateEvent", "任务状态更新事件"),
    
    /**
     * 路径冲突事件
     * payload: robotId, conflictRobotId
     * 场景：检测到路径冲突
     * 异步事件
     */
    PATH_CONFLICT_EVENT("pathConflictEvent", "路径冲突事件"),
    
    /**
     * 紧急停止事件
     * payload: robotId
     * 场景：紧急情况需要停止机器人
     * 同步事件，高优先级
     */
    EMERGENCY_STOP_EVENT("emergencyStopEvent", "紧急停止事件");
    
    private final String code;
    private final String description;
    
    DispatchEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取事件类型
     */
    public static DispatchEventType fromCode(String code) {
        for (DispatchEventType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
