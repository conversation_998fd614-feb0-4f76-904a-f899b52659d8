package com.honichang.dispatch.event;

/**
 * 调度事件处理器接口
 * 
 * <AUTHOR>
 */
public interface DispatchEventHandler {
    
    /**
     * 处理事件
     * 
     * @param event 调度事件
     * @return 处理结果
     */
    String handleEvent(DispatchEvent event);
    
    /**
     * 判断是否支持处理该类型的事件
     * 
     * @param eventType 事件类型
     * @return 支持返回true，否则返回false
     */
    boolean supports(DispatchEventType eventType);
    
    /**
     * 获取处理器优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 处理开始充电事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleToChargingEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理完成充电事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleFinishChargingEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理开始陪同事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleToEscortEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理访客超出陪同距离事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleOutOfRangeEscortEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理到达等待点事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleArriveWaitPointEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理到达目标点事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleArriveTargetPointEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理遭遇障碍事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleBlockEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理机器人状态更新事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleRobotStatusUpdateEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理任务状态更新事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleTaskStatusUpdateEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理路径冲突事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handlePathConflictEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
    
    /**
     * 处理紧急停止事件
     * 
     * @param event 事件
     * @return 处理结果
     */
    default String handleEmergencyStopEvent(DispatchEvent event) {
        throw new UnsupportedOperationException("Not implemented");
    }
}
