package com.honichang.dispatch.scheduler;

import com.honichang.dispatch.context.RobotContext;
import com.honichang.dispatch.service.DispatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

/**
 * 障碍处理调度器
 * 遭遇障碍倒计时处理
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class ObstacleScheduler {
    
    @Resource
    private DispatchService dispatchService;
    
    /**
     * 障碍处理调度
     * 每秒读取RobotContext障碍信息
     */
    @Scheduled(fixedDelay = 1000)
    public void obstacleSchedule() {
        try {
            Collection<RobotContext> robotContexts = RobotContext.values();
            
            for (RobotContext robotContext : robotContexts) {
                processObstacleLogic(robotContext);
            }
        } catch (Exception e) {
            log.error("障碍处理调度执行异常", e);
        }
    }
    
    /**
     * 处理单个机器人的障碍逻辑
     */
    private void processObstacleLogic(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();
        
        try {
            if (robotContext.isBlocked()) {
                // 机器人被阻挡
                handleBlockedRobot(robotContext);
            } else {
                // 机器人未被阻挡，检查是否刚刚解除阻挡
                if (robotContext.getBlockStartTime() != null) {
                    handleUnblockedRobot(robotContext);
                }
            }
        } catch (Exception e) {
            log.error("处理机器人[{}]障碍逻辑异常", robotId, e);
        }
    }
    
    /**
     * 处理被阻挡的机器人
     */
    private void handleBlockedRobot(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();
        Date blockStartTime = robotContext.getBlockStartTime();
        
        if (blockStartTime == null) {
            // 刚开始被阻挡
            robotContext.setBlockStartTime(new Date());
            log.info("机器人[{}]开始被阻挡", robotId);
            
            // 发送循环语音指令
            sendVoiceAlert(robotId, "遇到障碍");
            
            // 写日志
            writeRobotLog(robotId, "遇到障碍", "开始被阻挡");
            
        } else {
            // 检查是否超出阈值
            long blockAlarmTimeout = 2 * 60 * 1000; // 2分钟阈值，应该从配置读取
            long blockedDuration = System.currentTimeMillis() - blockStartTime.getTime();
            
            if (blockedDuration > blockAlarmTimeout) {
                // 超出阈值，执行障碍处理逻辑
                handleObstacleTimeout(robotContext);
            } else {
                // 阈值内，间隔5秒循环播报语音
                if (blockedDuration % 5000 < 1000) { // 每5秒播报一次
                    sendVoiceAlert(robotId, "遇到障碍");
                }
            }
        }
    }
    
    /**
     * 处理未被阻挡的机器人（刚解除阻挡）
     */
    private void handleUnblockedRobot(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();
        
        log.info("机器人[{}]障碍已移开，继续当前任务", robotId);
        
        // 清除阻挡状态
        robotContext.setBlockStartTime(null);
        
        // 取消循环语音指令
        cancelVoiceAlert(robotId);
        
        // 写日志
        writeRobotLog(robotId, "障碍移开", "继续当前任务");
        
        // 更新机器人上下文
        dispatchService.updateRobotContext(robotContext);
    }
    
    /**
     * 处理障碍超时
     */
    private void handleObstacleTimeout(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();
        
        log.warn("机器人[{}]障碍超时，执行后退避障逻辑", robotId);
        
        try {
            // 1. 汇报障碍
            reportObstacle(robotId);
            
            // 2. 调用getNearestPointBackward函数，后退就近停靠
            String backwardPoint = dispatchService.getNearestPointBackward(robotId);
            if (backwardPoint != null) {
                log.info("机器人[{}]后退到点位[{}]", robotId, backwardPoint);
                
                // 3. 强行后退
                String backwardStepPoint = dispatchService.backwardStepPoint(robotId);
                if (backwardStepPoint != null) {
                    // 4. 清空预分配路径
                    robotContext.getPreAllocatedPaths().clear();
                    
                    // 5. 重新规划路径
                    replanPath(robotContext, backwardStepPoint);
                }
            }
            
            // 6. 更新机器人状态
            robotContext.setBlocked(false);
            robotContext.setBlockStartTime(null);
            
            // 7. 取消循环语音指令
            cancelVoiceAlert(robotId);
            
            // 8. 写日志
            writeRobotLog(robotId, "障碍超时处理", "后退避障完成");
            
            // 9. 更新机器人上下文
            dispatchService.updateRobotContext(robotContext);
            
        } catch (Exception e) {
            log.error("机器人[{}]障碍超时处理异常", robotId, e);
        }
    }
    
    /**
     * 发送语音警告
     */
    private void sendVoiceAlert(long robotId, String message) {
        // TODO: 调用机器人API发送语音指令
        log.debug("机器人[{}]语音播报: {}", robotId, message);
    }
    
    /**
     * 取消语音警告
     */
    private void cancelVoiceAlert(long robotId) {
        // TODO: 调用机器人API取消语音指令
        log.debug("机器人[{}]取消语音播报", robotId);
    }
    
    /**
     * 汇报障碍
     */
    private void reportObstacle(long robotId) {
        // TODO: 向管理系统汇报障碍
        log.info("汇报机器人[{}]遇到障碍", robotId);
    }
    
    /**
     * 重新规划路径
     */
    private void replanPath(RobotContext robotContext, String startPoint) {
        // TODO: 重新规划从当前点位到目标点位的路径
        log.info("为机器人[{}]重新规划路径，起始点位[{}]", robotContext.getRobotId(), startPoint);
    }
    
    /**
     * 写机器人日志
     */
    private void writeRobotLog(long robotId, String event, String description) {
        // TODO: 写入机器人操作日志
        log.info("机器人[{}]日志: {} - {}", robotId, event, description);
    }
}
