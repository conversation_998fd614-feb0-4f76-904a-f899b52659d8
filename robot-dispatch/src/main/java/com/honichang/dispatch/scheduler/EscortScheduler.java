package com.honichang.dispatch.scheduler;

import com.honichang.dispatch.context.RobotContext;
import com.honichang.dispatch.context.TaskEscortContext;
import com.honichang.dispatch.service.ChargingService;
import com.honichang.dispatch.service.DispatchService;
import com.honichang.dispatch.service.EscortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;

/**
 * 陪同任务调度器
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class EscortScheduler {
    
    @Autowired
    private DispatchService dispatchService;
    
    @Autowired
    private EscortService escortService;
    
    @Autowired
    private ChargingService chargingService;
    
    /**
     * 预约陪同电量判断
     * 每60秒判断机器人是否有预约任务
     */
    @Scheduled(fixedDelay = 60000)
    public void checkEscortBattery() {
        try {
            Collection<RobotContext> robotContexts = RobotContext.values();
            
            for (RobotContext robotContext : robotContexts) {
                checkRobotEscortBattery(robotContext);
            }
        } catch (Exception e) {
            log.error("预约陪同电量判断异常", e);
        }
    }
    
    /**
     * 检查单个机器人的陪同任务电量
     */
    private void checkRobotEscortBattery(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();
        
        try {
            // TODO: 查询机器人是否有预约陪同任务
            // TaskEscortContext escortTask = getUpcomingEscortTask(robotId);
            // if (escortTask != null) {
            //     checkBatteryForEscort(robotContext, escortTask);
            // }
        } catch (Exception e) {
            log.error("检查机器人[{}]陪同任务电量异常", robotId, e);
        }
    }
    
    /**
     * 检查陪同任务电量
     */
    private void checkBatteryForEscort(RobotContext robotContext, TaskEscortContext escortTask) {
        long robotId = robotContext.getRobotId();
        double battery = robotContext.getBattery();
        Date execTime = escortTask.getExecTime();
        long timeToExec = execTime.getTime() - System.currentTimeMillis();
        
        // 预约时间进入3小时内，电量小于80%
        if (timeToExec <= 3 * 60 * 60 * 1000 && battery < 80.0) {
            log.info("机器人[{}]预约陪同任务3小时内执行，电量{}%，开始充电", robotId, battery);
            
            // 暂停当前任务队列
            // taskService.pauseCurrentTask(robotId, "预约陪同充电");
            
            // 计算最近的没有预约充电的充电桩，返回路径链
            Long chargingPileId = dispatchService.findNearestAvailableCharger(robotId);
            if (chargingPileId != null) {
                // 导航前往充电（高优先级）
                chargingService.toChargeAtPile(robotId, chargingPileId);
                
                // 写日志
                writeEscortLog(robotId, "预约陪同充电", "电量不足，前往充电桩充电");
            }
        }
        // 预约时间进入20分钟内，电量>75% and <80%
        else if (timeToExec <= 20 * 60 * 1000 && battery > 75.0 && battery < 80.0) {
            log.info("机器人[{}]预约陪同任务20分钟内执行，电量{}%，直接前往接人点", robotId, battery);
            
            // 暂停当前任务队列
            // taskService.pauseCurrentTask(robotId, "预约陪同准备");
            
            // 不去充电了，直接前往接人出发点，走陪同逻辑
            escortService.escortRun(escortTask.getId());
        }
    }
    
    /**
     * 陪同触发检查
     * 每60秒判断预约时间进入10分钟内（含立即执行）
     */
    @Scheduled(fixedDelay = 60000)
    public void checkEscortTrigger() {
        try {
            // TODO: 查询所有待执行的陪同任务
            // List<TaskEscortContext> pendingEscortTasks = getPendingEscortTasks();
            // 
            // for (TaskEscortContext escortTask : pendingEscortTasks) {
            //     checkEscortTaskTrigger(escortTask);
            // }
        } catch (Exception e) {
            log.error("陪同触发检查异常", e);
        }
    }
    
    /**
     * 检查陪同任务触发
     */
    private void checkEscortTaskTrigger(TaskEscortContext escortTask) {
        try {
            Date execTime = escortTask.getExecTime();
            long timeToExec = execTime.getTime() - System.currentTimeMillis();
            
            // 预约时间进入10分钟内（含立即执行）
            if (timeToExec <= 10 * 60 * 1000) {
                log.info("陪同任务[{}]触发执行", escortTask.getId());
                
                // 触发陪同事件
                escortService.escortRun(escortTask.getId());
            }
        } catch (Exception e) {
            log.error("检查陪同任务[{}]触发异常", escortTask.getId(), e);
        }
    }
    
    /**
     * 陪同任务状态监控
     * 每5秒检查陪同任务状态
     */
    @Scheduled(fixedDelay = 5000)
    public void monitorEscortTasks() {
        try {
            // TODO: 获取所有进行中的陪同任务
            // List<TaskEscortContext> activeEscortTasks = getActiveEscortTasks();
            // 
            // for (TaskEscortContext escortTask : activeEscortTasks) {
            //     monitorEscortTask(escortTask);
            // }
        } catch (Exception e) {
            log.error("陪同任务状态监控异常", e);
        }
    }
    
    /**
     * 监控单个陪同任务
     */
    private void monitorEscortTask(TaskEscortContext escortTask) {
        try {
            String status = escortTask.getStatus();
            long robotId = escortTask.getRobotId();
            
            // 距离报警检查
            if (isEscortingStatus(status)) {
                checkVisitorDistance(escortTask);
            }
            
            // 等待超时判断
            if ("102".equals(status)) { // 等待访客状态
                checkWaitTimeout(escortTask);
            }
            
            // 关闭报警超时检查
            if ("302".equals(status)) { // 关闭报警状态
                checkAlarmRestoreTimeout(escortTask);
            }
            
        } catch (Exception e) {
            log.error("监控陪同任务[{}]异常", escortTask.getId(), e);
        }
    }
    
    /**
     * 判断是否为陪同中状态
     */
    private boolean isEscortingStatus(String status) {
        return "201".equals(status) || "301".equals(status) || "401".equals(status);
    }
    
    /**
     * 检查访客距离
     */
    private void checkVisitorDistance(TaskEscortContext escortTask) {
        // TODO: 检测uwb距离
        // double uwbDistance = getUwbDistance(escortTask.getUwbCardId());
        // double outOfRangeDistance = 3.0; // 从配置读取
        // 
        // if (uwbDistance > outOfRangeDistance) {
        //     // 超出距离，发送事件
        //     publishOutOfRangeEvent(escortTask.getRobotId());
        // } else if (escortTask.getOutOfRangeTime() != null) {
        //     // 未超出距离，清空超出时间
        //     escortTask.setOutOfRangeTime(null);
        //     // 调用机器人api：继续
        //     resumeRobot(escortTask.getRobotId());
        // }
    }
    
    /**
     * 检查等待超时
     */
    private void checkWaitTimeout(TaskEscortContext escortTask) {
        if (escortTask.getWaitArriveTime() != null && escortTask.getTimeoutMin() != null) {
            long waitDuration = System.currentTimeMillis() - escortTask.getWaitArriveTime().getTime();
            long timeoutMs = escortTask.getTimeoutMin() * 60 * 1000L;
            
            if (waitDuration > timeoutMs) {
                log.warn("陪同任务[{}]等待超时，异常终止", escortTask.getId());
                escortService.escortAbort(escortTask.getId(), "等待访客超时");
            } else {
                // 每间隔5秒钟循环播报语音场景：等待访客
                if (waitDuration % 5000 < 1000) {
                    sendVoiceAlert(escortTask.getRobotId(), "等待访客");
                }
            }
        }
    }
    
    /**
     * 检查关闭报警恢复超时
     */
    private void checkAlarmRestoreTimeout(TaskEscortContext escortTask) {
        if (escortTask.getRecoveryTime() != null) {
            Date now = new Date();
            if (now.after(escortTask.getRecoveryTime())) {
                log.info("陪同任务[{}]关闭报警超时，自动恢复", escortTask.getId());
                escortService.escortRestoreAlarm(escortTask.getId());
            }
        }
    }
    
    /**
     * 发送语音警告
     */
    private void sendVoiceAlert(long robotId, String message) {
        // TODO: 调用机器人API发送语音指令
        log.debug("机器人[{}]语音播报: {}", robotId, message);
    }
    
    /**
     * 写陪同日志
     */
    private void writeEscortLog(long robotId, String event, String description) {
        // TODO: 写入陪同任务日志
        log.info("机器人[{}]陪同日志: {} - {}", robotId, event, description);
    }
}
