package com.honichang.dispatch.scheduler;

import com.google.common.collect.ImmutableMap;
import com.honichang.common.core.concurrent.FixedThreadPool;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.dispactch.model.enums.RobotWorkStatusEnum;
import com.honichang.dispactch.model.enums.TaskEscortStatusEnum;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispatch.loader.ContextLoader;
import com.honichang.point.domain.TaskEscort;
import com.honichang.point.service.RobotChargingPileService;
import com.honichang.point.service.RobotService;
import com.honichang.point.service.TaskEscortService;
import com.honichang.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

import static com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum.ESCORT_TEMP_WAIT_TIMEOUT;

/**
 * 核心调度器
 * 核心常驻守护线程，无限循环读取所有机器人的状态信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CoreScheduler {

    @Resource
    private RobotService robotService;

    @Resource
    private DispatchService dispatchService;

    @Resource
    private TaskService taskService;

    @Resource
    private TaskEscortService taskEscortService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private RobotChargingPileService robotChargingPileService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    private static final boolean STOP_ALL_TASK = true;

    // region 👇每天零点触发

    /**
     * TODO 每天零点触发，处理一些需要清理的任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void dailySchedule() {
        if (STOP_ALL_TASK) {
            return;
        }

        try {
            // *清理超过24小时没有完成的陪同任务

            // *清理因为机器人离线而挂起时间过长的普通任务

        } catch (Exception e) {
            log.error("核心调度执行异常", e);
        }
    }

    // endregion


    // region 👇任务分配（10秒一次）


    /**
     * 每10秒统一检查并分配循环任务，推入机器人任务队列中
     * 1. 从task_def/task_instance中读取
     * 2. task_def周期任务已到期的推入顶层
     * 3. task_instance未完成的任务推入
     * 4. task_def按优先级推入
     */
    @Scheduled(fixedDelay = 10000)
    public void distributeTaskSchedule() {
        if (STOP_ALL_TASK || !ContextLoader.isLoaded()) {
            return;
        }

        taskService.distributeTasksForRobots();

        // 首次完成任务的断点续连
        if (!ContextLoader.isReady()) {
            ContextLoader.initDone();
        }
    }


    // endregion


    // region 👇机器人模式监控（1分钟一次）

    /**
     * 监控机器人模式及
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void listeningWorkModeSchedule() {
        if (STOP_ALL_TASK) {
            return;
        }

        // 获取所有机器人上下文
        for (RobotContext rc : RobotContext.values()) {
            try {
                // *检查遥控模式超时
                __checkRemoteControlTimeout(rc);
            } catch (Exception e) {
                log.error("核心调度执行异常", e);
            }
        }

        for (TaskEscortContext tec : TaskEscortContext.values()) {
            try {
                // *检查陪同任务接客超时
                __checkEscortTimeout(tec);
            } catch (Exception e) {
                log.error("核心调度执行异常", e);
            }
        }
    }

    private void __checkEscortTimeout(TaskEscortContext tec) {
        TaskEscort te = tec.getTaskEscort();
        if (TaskEscortStatusEnum.WAIT_VISITOR_ARRIVE.getCode().equals(te.getStatus())) { // 等待访客状态
            if (tec.getWaitArriveTime() != null && tec.getTaskEscort().getTimeoutMin() != null) {
                long waitDuration = System.currentTimeMillis() - tec.getWaitArriveTime().getTime();
                long timeoutMs = tec.getTaskEscort().getTimeoutMin() * 60 * 1000L;

                if (waitDuration > timeoutMs) {
                    log.warn("陪同任务[{}]等待超时，异常终止", tec.getTaskEscort().getId());
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.ESCORT_ABORT_EVENT,
                            "CoreScheduler.checkEscortTimeout",
                            ImmutableMap.of("robotId", te.getRobotId(), "taskEscortId", te.getId(),
                                    "reason", "等待访客超时",
                                    "pushTemplate", ESCORT_TEMP_WAIT_TIMEOUT),
                            true)); // 102-109
                }
            }
        }
    }

    /**
     * 检查遥控模式超时
     */
    private void __checkRemoteControlTimeout(RobotContext rc) {
        Date lastTime = rc.getLastRemoteControlTime();
        if (rc.getWorkMode() == RobotWorkModeEnum.REMOTE_CONTROL_MODE && lastTime != null) {

            int minutes;
            try {
                minutes = Integer.parseInt(sysConfigService.selectConfigByKey("remoteControlTimeout"));
                // 不允许小于3分钟或大于30分钟
                minutes = Math.min(Math.max(minutes, 3), 30);
            } catch (Exception e) {
                minutes = 10; // 默认
            }

            long timeout = (long) minutes * 60 * 1000; // 超时时间，单位毫秒
            long elapsed = System.currentTimeMillis() - lastTime.getTime();

            if (elapsed > timeout) {
                log.info("机器人[{}]遥控模式超时，切换回自动模式", rc.getRobotId());
                dispatchService.switchWorkMode(rc.getRobotId(), RobotWorkModeEnum.AUTO_MODE, true);
            }
        }
    }

    // endregion


    // region 👇任务调度（每8秒一次）

    /**
     * 核心调度逻辑
     * 每8s执行一次
     */
    @Scheduled(fixedDelay = 8000)
    public void coreSchedule() {
        if (!ContextLoader.isReady() || STOP_ALL_TASK) {
            return;
        }

        try {
            // 获取所有机器人上下文
            Collection<RobotContext> robotContexts = RobotContext.values();

            for (RobotContext rc : robotContexts) {
                // 检查机器人状态
                if (taskService.canWorkForEscort(rc).equals(StringUtils.EMPTY)) {
                    // *处理陪同任务调度
                    __processEscortTaskSchedule(rc);

                    // 检查机器人状态
                    // 为避免线程问题，这里必须放入上一个if内
                    if (taskService.canWorkForTask(rc).equals(StringUtils.EMPTY)) {

                        // *处理普通任务调度
                        // 含周期任务
                        __processNormalTaskSchedule(rc);

                    }
                }
            }
        } catch (Exception e) {
            log.error("核心调度执行异常", e);
        }
    }

    /**
     * 处理陪同任务调度
     */
    private void __processEscortTaskSchedule(RobotContext rc) {
        TaskEscortContext tec = TaskEscortContext.getByRobot(rc.getRobotId());
        if (tec == null) {
            // 机器人没有陪同任务
            RobotWorkStatusEnum ws = rc.getWorkStatus();
            if (ws == RobotWorkStatusEnum.ESCORT_TASK || ws == RobotWorkStatusEnum.ESCORT_CHARGE || ws == RobotWorkStatusEnum.ESCORT_RETURN) {
                rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);
            }
            return;
        }

        TaskEscort te = tec.getTaskEscort();
        TaskEscortStatusEnum status = TaskEscortStatusEnum.fromCode(te.getStatus());
        switch (status) {
            case NOT_STARTED: // 未开始
                // 判断时间，到了就出发，电量等不用考虑
                // 由distributeTasksForRobots已经安排好了提前充电的逻辑，并推入TaskEscortContext
                // 正在充电会主动停止充电
                // 提前5分钟跑
                if (te.getExecTime().getTime() - System.currentTimeMillis() <= 5 * 60 * 1000) {
                    taskEscortService.startToVisit(rc.getRobotId(), te);
                } // 001->101
                break;
            case GOTO_VISITOR: // 接访客
                // 判断是否走到了接客点由回调机器人预分配路径为空触发
                // 101->102
                break;
            case WAIT_VISITOR_ARRIVE: // 等待访客
                // 由回调触发UWB签到  102->201
                // 或由CoreScheduler超时触发 102->109
                break;
            case ESCORTING: // 陪同中
                // 签到后进入陪同中状态
                // 判断是否抵达目的地由回调机器人预分配路径为空触发 201->301
                // 或由CoreScheduler超时触发 201->209
                break;
            case ARRIVED_DESTINATION: // 到达目的地
                // 由工控按钮回调触发下一个动作 301->401
                // 或由CoreScheduler超时触发 301->309
                break;
            case PAUSED_ALARM: // 暂停报警
                // 手动暂停报警（TaskEscortController） 301->302 | 302->301
                break;
            case GOTO_EXIT: // 送访客
                // 判断是否抵达出口由回调机器人预分配路径为空触发 401->402
                break;
            case WAIT_VISITOR_LEAVE: // 等待访客离开
                // 由工控按钮回调触发下一个动作 402->501
                // 由回调触发UWB距离超时 402->501
                break;
            case COMPLETED: // 已完成
                // 由工控回调触发安全地前往最近一个十字路口
                break;
            case TERMINATED_009:
            case TERMINATED_109:
            case TERMINATED_209:
            case TERMINATED_309:
            case TERMINATED_409:
            default: // 终止
                // 由工控回调触发安全地前往最近一个十字路口
                break;
        }
    }

    /**
     * 处理普通任务恢复调度
     */
    private void __processNormalTaskSchedule(RobotContext rc) {
        if (!rc.isStandBy()) {
            return;
        }
        // 场景一：待机模式，机器人队列顶端是【全新】的任务
        // 如果有，则开始调度执行任务
        // 1.改变机器人状态：工作中
        // 2.insert到db中
        // 3.规划完全路径，并调度机器人出发
        //
        // 场景二：待机模式，机器人队列顶端不是全新的任务
        // 判断任务本身状态，如果不是暂停，说明是【服务重启】
        // 1.改变机器人状态：工作中
        // 判断任务状态，如果是【暂停】，说明是充电、陪同、遥控等【打断】了
        // 1.改变机器人状态：工作中
        // 2.update任务状态并save
        // 3.规划断点继续的路径，并调度机器人出发
        if (taskService.runTaskInQueue(rc)) {
            // 状态、工作状态
            rc.setWorkStatus(rc.getTaskDirectiveChain().getTaskInstance().getPlanType() == 1
                    ? RobotWorkStatusEnum.PERIOD_TASK : RobotWorkStatusEnum.CYCLE_TASK);
            rc.setStatus(RobotStatusEnum.WORKING);
        }
    }

    // endregion


    // region 👇1秒处理集中


    /**
     * 每1s执行一次
     */
    @Scheduled(fixedDelay = 1000)
    public void oneSecondSchedule() {
        if (STOP_ALL_TASK) {
            return;
        }

        try {
            // *实时写入DB信息
            __flushRobotContextToDB();

            // *刷新充电桩状态
            // 如果变成非工作状态会将机器人调走，去其他充电桩
            __refreshChargingPileStatus();

            // *处理百分比行进逻辑
            FixedThreadPool.execute(this::__processRunStepPercent);

            // *检查停车位上的机器人走了没有
            FixedThreadPool.execute(this::__checkRobotLeaveParking);

            // *刷新走过的路径
            // Done: 在DispatchCallbackService中机器人状态回调中处理！

        } catch (Exception e) {
            log.error("核心调度执行异常", e);
        }
    }

    // region 刷新入DB

    /**
     * 实时刷入DB
     * 每1秒触发，自动将RobotContext中的状态信息刷入DB
     */
    private void __flushRobotContextToDB() {
        try {
            robotService.flushRobotContextToDB();
        } catch (Exception e) {
            log.error("刷入机器人上下文到数据库异常", e);
        }
    }

    // endregion

    // region 实时自动释放停车位

    /**
     * 实时自动释放停车位
     */
    private void __checkRobotLeaveParking() {
        MapContext.values().forEach(mc -> {
            mc.getParkPoints().forEach(park -> {
                if (park.getRobotId() != null && RobotContext.exists(park.getRobotId())) {
                    RobotContext rc = RobotContext.get(park.getRobotId());
                    if (!park.equalXY(rc.getPosition())) {
                        // 已经离开
                        park.setRobotId(null);
                    }
                }
            });
        });
    }

    // endregion

    // region 处理百分比行进逻辑【TODO】

    /**
     * 处理百分比行进逻辑
     */
    private void __processRunStepPercent() {
        Collection<RobotContext> rcs = RobotContext.values();
        // 获取所有机器人上下文
        for (RobotContext rc : rcs) {
            if (rc.getRunStepPercent() > 0.0 && rc.getRunStepPercent() < 1.0) {
                // TODO: 判断当前机器人是否已经完成计划的百分比
                // 如果完成则：
                // 1. 发送机器人api暂停指令
                // 2. set runStepPercent=0.0
            }
        }
    }

    // endregion

    // region 刷新充电桩状态

    /**
     * 刷新充电桩状态
     */
    private void __refreshChargingPileStatus() {
        robotChargingPileService.refreshChargingPileStatus();
    }

    // endregion

    // endregion


}
