package com.honichang.dispatch.scheduler;

import com.honichang.dispatch.context.RobotContext;
import com.honichang.dispatch.service.DispatchService;
import com.honichang.dispatch.service.EscortService;
import com.honichang.dispatch.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * 核心调度器
 * 核心常驻守护线程，无限循环读取所有机器人的状态信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CoreScheduler {

    @Autowired
    private DispatchService dispatchService;

    @Autowired
    private EscortService escortService;

    @Autowired
    private TaskService taskService;

    /**
     * 核心调度逻辑
     * 每250ms执行一次（4帧）
     */
    @Scheduled(fixedDelay = 250)
    public void coreSchedule() {
        try {
            // 获取所有机器人上下文
            Collection<RobotContext> robotContexts = RobotContext.values();

            for (RobotContext robotContext : robotContexts) {
                processRobotSchedule(robotContext);
            }
        } catch (Exception e) {
            log.error("核心调度执行异常", e);
        }
    }

    /**
     * 处理单个机器人的调度逻辑
     */
    private void processRobotSchedule(RobotContext robotContext) {
        long robotId = robotContext.getRobotId();

        try {
            // 1. 更新机器人状态信息
            updateRobotStatus(robotContext);

            // 2. 检查遥控模式超时
            checkRemoteControlTimeout(robotContext);

            // 3. 处理陪同任务调度
            processEscortTaskSchedule(robotContext);

            // 4. 处理百分比行进逻辑
            processRunStepPercent(robotContext);

            // 5. 处理普通任务调度
            processNormalTaskSchedule(robotContext);

        } catch (Exception e) {
            log.error("处理机器人[{}]调度异常", robotId, e);
        }
    }

    /**
     * 更新机器人状态信息
     */
    private void updateRobotStatus(RobotContext robotContext) {
        // TODO: 调用机器人API获取最新状态
        // 更新位置、电量、状态等信息
        // dispatchService.updateRobotContext(robotContext);
    }

    /**
     * 检查遥控模式超时
     */
    private void checkRemoteControlTimeout(RobotContext robotContext) {
        if (robotContext.getWorkMode() == 102 && robotContext.getLastRemoteControlTime() != null) {
            long timeout = 10 * 60 * 1000; // 10分钟超时，应该从配置读取
            long elapsed = System.currentTimeMillis() - robotContext.getLastRemoteControlTime().getTime();

            if (elapsed > timeout) {
                log.info("机器人[{}]遥控模式超时，切换回自动模式", robotContext.getRobotId());
                dispatchService.switchWorkMode(robotContext.getRobotId(), 101, 0);
            }
        }
    }

    /**
     * 处理陪同任务调度
     */
    private void processEscortTaskSchedule(RobotContext robotContext) {
        if (robotContext.getWorkStatus() == 10100) { // 陪同任务
            // TODO: 实现陪同任务调度逻辑
            // 1. 路径调度
            // 2. 距离报警
            // 3. 等待超时判断
            // 4. 关闭报警超时
            // 5. 抵达判断
        }
    }

    /**
     * 处理百分比行进逻辑
     */
    private void processRunStepPercent(RobotContext robotContext) {
        if (robotContext.getRunStepPercent() > 0.0 && robotContext.getRunStepPercent() < 1.0) {
            // TODO: 判断当前机器人是否已经完成计划的百分比
            // 如果完成则：
            // 1. 发送机器人api暂停指令
            // 2. set runStepPercent=0.0
        }
    }

    /**
     * 处理普通任务调度
     */
    private void processNormalTaskSchedule(RobotContext robotContext) {
        if (robotContext.getWorkMode() == 101) { // 自动模式
            if (robotContext.getWorkStatus() == 60100 || robotContext.getWorkStatus() == 60200) {
                // 周期任务或普通任务
                if ("1".equals(robotContext.getStatus())) { // 工作状态
                    // TODO: 调用runStep函数
                } else if ("4".equals(robotContext.getStatus())) { // 暂停状态
                    // TODO: 调用pause函数
                }
            }
        }
    }

    /**
     * 实时刷入DB
     * 每30秒触发，自动将RobotContext中的状态信息刷入DB
     */
    @Scheduled(fixedDelay = 30000)
    public void flushRobotContextToDB() {
        try {
            Collection<RobotContext> robotContexts = RobotContext.values();
            for (RobotContext robotContext : robotContexts) {
                // TODO: 将机器人上下文刷入数据库
                // robotService.updateRobotStatus(robotContext);
            }
            log.debug("机器人上下文已刷入数据库，共{}个机器人", robotContexts.size());
        } catch (Exception e) {
            log.error("刷入机器人上下文到数据库异常", e);
        }
    }
}
