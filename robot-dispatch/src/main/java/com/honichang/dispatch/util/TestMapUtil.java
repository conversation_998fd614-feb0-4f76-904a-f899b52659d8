package com.honichang.dispatch.util;

import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class TestMapUtil {

    public static void drawImage(MapContext mapContext, int width, int height, int scale, String outputPath) {
        // 根据mapContext.points / paths，绘制一张jpg图片
        // 十字路口用白色原点、识别点位用蓝色原点，路径用黄色线段
        // 保存到项目根目录的large_scale_test.jpg
//        int scale = 30;
//        int width = 8000;
//        int height = 8000;
        int pointWidth = 24;
        int padding = 200;
        try {
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            g2d.setColor(Color.BLUE);
            for (Position point : mapContext.getPoints()) {
                if (PointClassNameEnum.CROSSROADS.getCode().equals(point.getClassName())) {
                    g2d.setColor(Color.RED);
                } else {
                    g2d.setColor(Color.BLUE);
                }
                // 偏移N像素作为边距，并将坐标变为像素，再放大一些
                // 将0,0作为图片的左下角
                int x = (int) point.getX() * scale + padding;
                int y = height - (int) point.getY() * scale - padding;

                g2d.fillOval(x, y, pointWidth, pointWidth);
                // 点位名称
                g2d.setColor(Color.BLACK);
                g2d.drawString(point.getXmapId(), x, y);
            }
            g2d.setColor(Color.GRAY);
            for (Path path : mapContext.getPaths()) {
                int x1 = (int) path.getStartPos().getX() * scale + padding;
                int y1 = height - (int) path.getStartPos().getY() * scale - padding;
                int x2 = (int) path.getEndPos().getX() * scale + padding;
                int y2 = height - (int) path.getEndPos().getY() * scale - padding;

                g2d.drawLine(x1 + pointWidth/2,
                        y1 + pointWidth/2,
                        x2 + pointWidth/2,
                        y2 + pointWidth/2);
            }
            ImageIO.write(image, "jpg", new File(outputPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
