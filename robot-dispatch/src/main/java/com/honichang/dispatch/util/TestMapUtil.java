package com.honichang.dispatch.util;

import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class TestMapUtil {

    public static void drawImage(MapContext mapContext, int width, int height, int scale, String outputPath) {
        // 根据mapContext.points / paths，绘制一张jpg图片
        // 十字路口用白色原点、识别点位用蓝色原点，路径用黄色线段
        // 保存到项目根目录的large_scale_test.jpg
//        int scale = 30;
//        int width = 8000;
//        int height = 8000;
        int pointWidth = 24;
        int padding = 200;
        try {
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            g2d.setColor(Color.BLUE);
            for (Position point : mapContext.getPoints()) {
                if (PointClassNameEnum.CROSSROADS.getCode().equals(point.getClassName())) {
                    g2d.setColor(Color.RED);
                } else {
                    g2d.setColor(Color.BLUE);
                }
                // 偏移N像素作为边距，并将坐标变为像素，再放大一些
                // 将0,0作为图片的左下角
                int x = (int) point.getX() * scale + padding;
                int y = height - (int) point.getY() * scale - padding;

                g2d.fillOval(x, y, pointWidth, pointWidth);
                // 点位名称
                g2d.setColor(Color.BLACK);
                g2d.drawString(point.getXmapId(), x, y);
            }
            g2d.setColor(Color.GRAY);
            for (Path path : mapContext.getPaths()) {
                int x1 = (int) path.getStartPos().getX() * scale + padding;
                int y1 = height - (int) path.getStartPos().getY() * scale - padding;
                int x2 = (int) path.getEndPos().getX() * scale + padding;
                int y2 = height - (int) path.getEndPos().getY() * scale - padding;

                g2d.drawLine(x1 + pointWidth/2,
                        y1 + pointWidth/2,
                        x2 + pointWidth/2,
                        y2 + pointWidth/2);
            }
            ImageIO.write(image, "jpg", new File(outputPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 绘制地图、任务点位和最优路径的图片
     *
     * @param mapContext 地图上下文
     * @param taskNodes 任务节点列表
     * @param optimalPaths 最优路径
     * @param width 图片宽度
     * @param height 图片高度
     * @param scale 缩放比例
     * @param outputPath 输出路径
     */
    public static void drawImageWithTasksAndPaths(MapContext mapContext,
                                                  java.util.List<com.honichang.dispactch.model.TaskNode> taskNodes,
                                                  com.honichang.dispactch.model.PathChain<Path> optimalPaths,
                                                  int width, int height, int scale, String outputPath) {
        int pointWidth = 24;
        int padding = 200;

        try {
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 白色背景
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);

            // 1. 绘制所有地图路径（灰色）
            g2d.setColor(Color.LIGHT_GRAY);
            g2d.setStroke(new BasicStroke(2));
            for (Path path : mapContext.getPaths()) {
                int x1 = (int) path.getStartPos().getX() * scale + padding;
                int y1 = height - (int) path.getStartPos().getY() * scale - padding;
                int x2 = (int) path.getEndPos().getX() * scale + padding;
                int y2 = height - (int) path.getEndPos().getY() * scale - padding;

                g2d.drawLine(x1 + pointWidth/2, y1 + pointWidth/2, x2 + pointWidth/2, y2 + pointWidth/2);
            }

            // 2. 绘制所有地图点位
            for (Position point : mapContext.getPoints()) {
                if (com.honichang.dispactch.model.enums.PointClassNameEnum.CROSSROADS.getCode().equals(point.getClassName())) {
                    g2d.setColor(Color.RED); // 十字路口用红色
                } else {
                    g2d.setColor(Color.BLUE); // 识别点位用蓝色
                }

                int x = (int) point.getX() * scale + padding;
                int y = height - (int) point.getY() * scale - padding;
                g2d.fillOval(x, y, pointWidth, pointWidth);

                // 点位名称
                g2d.setColor(Color.BLACK);
                g2d.setFont(new Font("Arial", Font.PLAIN, 10));
                g2d.drawString(point.getXmapId(), x, y - 5);
            }

            // 3. 绘制任务点位（绿色标记）
            g2d.setColor(Color.GREEN);
            g2d.setStroke(new BasicStroke(4));
            for (com.honichang.dispactch.model.TaskNode taskNode : taskNodes) {
                String xmapId = taskNode.getTaskInstanceNode().getBizPoint().getXmapId();
                Position taskPoint = findPointByXmapId(mapContext.getPoints(), xmapId);
                if (taskPoint != null) {
                    int x = (int) taskPoint.getX() * scale + padding;
                    int y = height - (int) taskPoint.getY() * scale - padding;

                    // 绘制绿色圆圈标记
                    g2d.drawOval(x - 5, y - 5, pointWidth + 10, pointWidth + 10);
                }
            }

            // 4. 绘制最优路径（紫色线）
            g2d.setColor(new Color(128, 0, 128)); // 紫色
            g2d.setStroke(new BasicStroke(6));
            for (Path path : optimalPaths.getAll()) {
                int x1 = (int) path.getStartPos().getX() * scale + padding;
                int y1 = height - (int) path.getStartPos().getY() * scale - padding;
                int x2 = (int) path.getEndPos().getX() * scale + padding;
                int y2 = height - (int) path.getEndPos().getY() * scale - padding;

                g2d.drawLine(x1 + pointWidth/2, y1 + pointWidth/2, x2 + pointWidth/2, y2 + pointWidth/2);
            }

            // 5. 添加图例
            drawLegend(g2d, width, height);

            g2d.dispose();
            ImageIO.write(image, "jpg", new File(outputPath));
            System.out.println("路径规划结果图片已保存到: " + outputPath);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 绘制图例
     */
    private static void drawLegend(Graphics2D g2d, int width, int height) {
        int legendX = width - 300;
        int legendY = 50;
        int lineHeight = 30;

        g2d.setColor(Color.WHITE);
        g2d.fillRect(legendX - 10, legendY - 10, 280, 200);
        g2d.setColor(Color.BLACK);
        g2d.drawRect(legendX - 10, legendY - 10, 280, 200);

        g2d.setFont(new Font("Arial", Font.BOLD, 14));
        g2d.drawString("图例 (Legend)", legendX, legendY);

        g2d.setFont(new Font("Arial", Font.PLAIN, 12));

        // 十字路口
        g2d.setColor(Color.RED);
        g2d.fillOval(legendX, legendY + lineHeight, 12, 12);
        g2d.setColor(Color.BLACK);
        g2d.drawString("十字路口", legendX + 20, legendY + lineHeight + 10);

        // 识别点位
        g2d.setColor(Color.BLUE);
        g2d.fillOval(legendX, legendY + lineHeight * 2, 12, 12);
        g2d.setColor(Color.BLACK);
        g2d.drawString("识别点位", legendX + 20, legendY + lineHeight * 2 + 10);

        // 任务点位
        g2d.setColor(Color.GREEN);
        g2d.setStroke(new BasicStroke(2));
        g2d.drawOval(legendX - 2, legendY + lineHeight * 3 - 2, 16, 16);
        g2d.setColor(Color.BLACK);
        g2d.drawString("任务点位", legendX + 20, legendY + lineHeight * 3 + 10);

        // 地图路径
        g2d.setColor(Color.LIGHT_GRAY);
        g2d.setStroke(new BasicStroke(2));
        g2d.drawLine(legendX, legendY + lineHeight * 4 + 5, legendX + 15, legendY + lineHeight * 4 + 5);
        g2d.setColor(Color.BLACK);
        g2d.drawString("地图路径", legendX + 20, legendY + lineHeight * 4 + 10);

        // 最优路径
        g2d.setColor(new Color(128, 0, 128));
        g2d.setStroke(new BasicStroke(4));
        g2d.drawLine(legendX, legendY + lineHeight * 5 + 5, legendX + 15, legendY + lineHeight * 5 + 5);
        g2d.setColor(Color.BLACK);
        g2d.drawString("最优路径", legendX + 20, legendY + lineHeight * 5 + 10);
    }

    /**
     * 根据XmapId查找点位
     */
    private static Position findPointByXmapId(java.util.List<Position> points, String xmapId) {
        if (points == null || xmapId == null) {
            return null;
        }

        return points.stream()
                .filter(point -> xmapId.equals(point.getXmapId()))
                .findFirst()
                .orElse(null);
    }

}
