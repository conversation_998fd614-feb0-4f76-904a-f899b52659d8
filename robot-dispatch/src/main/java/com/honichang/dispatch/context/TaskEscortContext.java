package com.honichang.dispatch.context;

import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.PathChain;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 陪同任务上下文
 * 
 * <AUTHOR>
 */
@Data
public class TaskEscortContext {
    
    /**
     * 缓存静态数组
     */
    private static final Map<Long, TaskEscortContext> cache = new ConcurrentHashMap<>();
    
    /** 陪同任务id **/
    private long id;
    /** 陪同任务名称 **/
    private String taskName;
    /** 执行类型: 1-立即执行 | 2-预约执行 **/
    private int execType;
    /** 陪同任务执行时间 **/
    private Date execTime;
    /** 陪同任务超时时间: 允许等待的时间（分钟） **/
    private Integer timeoutMin;
    /** 陪同任务状态 **/
    private String status;
    /** 陪同任务开始时间 **/
    private Date startTime;
    /** 陪同任务结束时间 **/
    private Date endTime;
    /** 陪同任务等待点位 **/
    private String waitPointId;
    /** 机器人到达等待点时间，用于计算等待超时 **/
    private Date waitArriveTime;
    /** 陪同任务到达点位 **/
    private String arrivePointId;
    /** 陪同任务机器人 **/
    private long robotId;
    /** 陪同任务负责人 **/
    private Long superintendent;
    /** 陪同任务暂停时间 **/
    private Integer pauseMin;
    /** 陪同任务恢复时间 **/
    private Date recoveryTime;
    /** 陪同任务UWB卡id **/
    private Long uwbCardId;
    /** 访客超出范围时间 **/
    private Date outOfRangeTime;
    /** 预分配路径资源 **/
    private PathChain<Path> preAllocatedPaths;
    /** 已发送路径资源 **/
    private PathChain<Path> hasBeenSendPaths;
    /** 已完成路径资源 **/
    private PathChain<Path> completedPaths;
    
    /**
     * 获取陪同任务上下文
     */
    public static TaskEscortContext get(long taskId) {
        return cache.get(taskId);
    }
    
    /**
     * 缓存陪同任务上下文
     */
    public static void put(TaskEscortContext taskEscortContext) {
        cache.put(taskEscortContext.getId(), taskEscortContext);
    }
    
    /**
     * 移除陪同任务上下文
     */
    public static void remove(long taskId) {
        cache.remove(taskId);
    }
    
    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }
}
