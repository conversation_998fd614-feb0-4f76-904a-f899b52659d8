package com.honichang.dispatch.context;

import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.TaskChain;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 机器人调度上下文
 * <AUTHOR>
 */
@Data
public class RobotContext {

    /**
     * 缓存静态数组
     */
    private static final Map<Long, RobotContext> cache = new ConcurrentHashMap<>();

    //↓--------不常变属性
    /** 机器人id **/
    private volatile long robotId;
    /** 地图id **/
    private volatile long mapId;
    /** 机器人名称 **/
    private volatile String robotName;
    /** 机器人序列号 **/
    private volatile String sn;
    /** 机器人ip **/
    private volatile String ip;
    /** 机器人端口 **/
    private volatile int port;
    /** 机器人appCode **/
    private volatile String appCode;
    /** 机器人优先级 **/
    private volatile int priority;

    //↓--------常变属性
    /** 机器人当前坐标（米） **/
    private volatile Position position;
    /** 机器人当前朝向（弧度rad） **/
    private volatile double theta;

    /** 机器人当前所在路径 **/
    private volatile String xmapPathId;
    /** 机器人当前所在点位 **/
    private volatile String xmapPointId;
    /** 机器人当前所在路径的起始点位 **/
    private volatile String xmapPathStartId;
    /** 机器人当前所在路径的截止点位 **/
    private volatile String xmapPathEndId;
    /** 当前是正走还是倒走：1正走 | 2倒走 **/
    private volatile int direction;
    /** 机器人当前任务类型 **/
    private volatile int latestTaskClass;
    /** 机器人当前任务id **/
    private volatile long latestTaskId;

    /** 机器人累计运行时间（分钟） **/
    private volatile long totalRunning;
    /** 机器人累计里程数（米） **/
    private volatile double totalOdom;
    /** 机器人当前电量 **/
    private volatile double battery;

    /**
     * 机器人状态:<br/>
     * 0待机 | 1工作 | 4暂停 | 6定位中 |
     * 701载入地图失败 | 702定位失败 | 703导航失败
     * 8离线 | 9故障
     **/
    private volatile String status;

    /** 机器人当前工作状态（主优先级） **/
    private volatile int workStatus;

    /** 工作模式：101自动模式 | 102遥控模式(后端遥控) | 201手动模式(厂家遥控器) **/
    private volatile int workMode;
    /** 遥控模式最后操作时间，用于超时切回自动模式 **/
    private volatile Date lastRemoteControlTime;

    /** 当前是否被阻挡 **/
    private volatile boolean isBlocked;
    /** 阻挡开始时间，用于计算超时阈值 **/
    private volatile Date blockStartTime;

    /** 设定runStep完成百分比（0.0-1.0）；0或1走完 **/
    private volatile double runStepPercent;

    /**
     * 机器人任务队列
     * 包含正在进行的和等待执行的任务
     **/
    private final transient AtomicReference<TaskChain<?>> taskQueue = new AtomicReference<>();
    /** 预分配路径资源 **/
    private final transient AtomicReference<PathChain<Path>> preAllocatedPaths;
    /** 已完成路径资源 **/
    private final transient AtomicReference<PathChain<Path>> completedPaths;

}
