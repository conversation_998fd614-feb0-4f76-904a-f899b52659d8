package com.honichang.dispatch.context;

import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地图上下文
 * 需要缓存
 * 
 * <AUTHOR>
 */
@Data
public class MapContext {
    
    /**
     * 缓存静态数组
     */
    private static final Map<Long, MapContext> cache = new ConcurrentHashMap<>();
    
    /** 地图id **/
    private long mapId;
    /** 地图名称 **/
    private String name;
    /** 地图类型 **/
    private String type;
    /** 地图最小坐标 **/
    private Position minPos;
    /** 地图最大坐标 **/
    private Position maxPos;
    /** 地图宽度 **/
    private double width;
    /** 地图高度 **/
    private double height;
    /** 地图路径, 取消both，如果是both则拆成两条路径 **/
    private List<Path> paths;
    /** 地图点位 **/
    private List<Position> points;
    /** 地图障碍 **/
    private List<Obstacle> obstacles;
    
    /**
     * 获取地图上下文
     */
    public static MapContext get(long mapId) {
        return cache.get(mapId);
    }
    
    /**
     * 缓存地图上下文
     */
    public static void put(MapContext mapContext) {
        cache.put(mapContext.getMapId(), mapContext);
    }
    
    /**
     * 移除地图上下文
     */
    public static void remove(long mapId) {
        cache.remove(mapId);
    }
    
    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }
}
