package com.honichang.dispatch.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.util.concurrent.AtomicDouble;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispatch.common.ChainSerializer;
import com.honichang.dispatch.model.base.Chain;
import com.honichang.dispatch.model.base.ChainBase;

/**
 * 路径链
 * @param <E>
 * <AUTHOR>
 */
@JsonSerialize(using = ChainSerializer.class)
public final class PathChain<E extends IPath> extends ChainBase<E> {

    /**
     * 路径链的总长度
     */
    private final transient AtomicDouble pathLength = new AtomicDouble(0.0);

    /**
     * 路径链的总长度，单位同坐标系
     */
    public double getPathLength() {
        return pathLength.get();
    }

    @Override
    public void append(ChainBase<E> other) {
        if (!(other instanceof PathChain)) {
            return;
        }
        PathChain<E> pc = (PathChain<E>) other;
        super.append(pc);
        pathLength.addAndGet(pc.getPathLength());
    }

    @Override
    public void prepend(ChainBase<E> other) {
        if (!(other instanceof PathChain)) {
            return;
        }
        PathChain<E> pc = (PathChain<E>) other;
        super.prepend(pc);
        pathLength.addAndGet(pc.getPathLength());
    }

    @Override
    public void add(E r) {
        if (r == null) return;
        super.add(r);
        pathLength.addAndGet(r.getLength());
    }

    @Override
    public E popTail() {
        E e = super.popTail();
        if (e != null) {
            pathLength.addAndGet(-(e.getLength()));
        }
        return e;
    }

    @Override
    public E popHead() {
        E e = super.popHead();
        if (e != null) {
            pathLength.addAndGet(-(e.getLength()));
        }
        return e;
    }

    @Override
    public void cloneTo(Chain<E> target) {
        if (!(target instanceof PathChain)) {
            return;
        }
        PathChain<E> pc = (PathChain<E>) target;
        super.cloneTo(pc);
        pc.pathLength.set(pathLength.get());
    }

    @Override
    public void clear() {
        super.clear();
        pathLength.set(0.0);
    }

    /**
     * 避免内存泄漏与溢出，这里必须采用手动序列化形式实现链表的序列化
     */
    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();
        // Write out size
        s.writeDouble(pathLength.get());
    }

    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();
        pathLength.set(s.readDouble());
    }
}
