package com.honichang.dispatch.model;

import com.honichang.dispatch.model.base.ChainBase;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractChain<E> implements ChainBase<E> {

    /**
     * 链中包含了多少元素
     */
    private transient int size = 0;

    /**
     * 头
     */
    private transient Entry head = null;

    /**
     * 尾
     */
    private transient Entry last = null;

    /**
     * 路径链的总长度，单位同坐标系
     */
    public double getRouteLength() {
        return routeLength;
    }

    /**
     * 路径链中包含了多少路径
     */
    public int size() {
        return size;
    }

    /**
     * 获取路径链的头，null 表示空链
     */
    public E getHead() {
        return head.data;
    }

    /**
     * 获取路径链的尾，null 表示空链
     */
    public E getLast() {
        return last.data;
    }

    /**
     * 获取路径链中指定索引的路径
     * @param i 索引
     * @return 路径
     * @throws IndexOutOfBoundsException 如果索引越界
     */
    public E get(int i) {
        if (i < 0 || i >= size) {
            throw new IndexOutOfBoundsException("Index: " + i + ", Size: " + size);
        }

        Entry e = head;
        for (int j = 0; j < i; j++) {
            e = e.next;
        }

        return e.data;
    }

    /**
     * 获取路径链中所有的路径
     * @return 路径列表
     */
    public List<E> getAll() {
        List<E> list = new ArrayList<>(size);
        for (Entry e = head; e != null; e = e.next) {
            list.add(e.data);
        }
        return list;
    }

    /**
     * 将其他路径链追加到本链尾部
     */
    public void append(RouteChain<E> other) {
        if (other == null || other.size == 0) {
            return;
        }
        if (head == null) {
            head = other.head;
        } else {
            last.next = other.head;
            other.head.prev = last;
        }
        last = other.last;
        size += other.size;
        routeLength += other.routeLength;
    }

    /**
     * 将其他路径链插入到本链头之前
     */
    public void prepend(RouteChain<E> other) {
        if (other == null || other.size == 0) {
            return;
        }
        if (head != null) {
            other.last.next = head;
            head.prev = other.last;
        } else {
            last = other.last;
        }
        head = other.head;
        size += other.size;
        routeLength += other.routeLength;
    }

    /**
     * 避免链被污染，克隆一份新的路径链
     */
    public RouteChain<E> clone() {
        RouteChain<E> clone = new RouteChain<>();
        for (Entry e = head; e != null; e = e.next) {
            clone.add((E) e.data);
        }
        return clone;
    }

    /**
     * 查找是否已经存在
     */
    public boolean exists(E r) {
        for (Entry e = head; e != null; e = e.next) {
            if (e.data.getId().equals(r.getId())) {
                return true;
            }
        }
        return false;
    }

    // No longer Serializable!
    private class Entry {
        E data;
        Entry next;
        Entry prev;
    }

    /**
     * Appends the specified Route to the list
     */
    public void add(E r) {
        Entry e = new Entry();
        e.data = r;
        if (head == null) {
            head = e;
        } else {
            last.next = e;
            e.prev = last;
        }
        last = e;
        size++;
        // 路径长度，加入routeLength总长度中
        routeLength += r.getLength();
    }

    /**
     * 避免内存泄漏与溢出，这里必须采用手动序列化形式实现链表的序列化
     */
    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();

        // Write out size
        s.writeInt(size);

        // Write out routeLength
        s.writeDouble(routeLength);

        // Write out all elements in the proper order.
        for (Entry e = head; e != null; e = e.next)
            s.writeObject(e.data);
    }

    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();

        // Read in size
        int size = s.readInt();

        // Read in routeLength
        routeLength = s.readDouble();

        // Read in all elements in the proper order.
        for (int i = 0; i < size; i++) {
            add((E) s.readObject());
        }
    }
}
