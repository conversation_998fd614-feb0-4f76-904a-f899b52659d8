package com.honichang.dispatch.model;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;

public class <PERSON><PERSON>hain extends TaskChain<String> {

    private int length;

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();

        // Write out size
        s.writeInt(length);

        System.out.println("writeObject child: " + length);
    }

    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();

        // Read in size
        length = s.readInt();

        System.out.println("readObject child: " + length);
    }

    public static void main(String[] args) {
        ChildChain c = new ChildChain();
        c.setLength(10);
        c.add("1");
        c.add("2");
        c.add("3");

        // 测试 Java 原生序列化
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(bos)) {
            oos.writeObject(c);
            System.out.println("Java serialization completed");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
