package com.honichang.dispatch.model.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.honichang.dispatch.common.ChainSerializer;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 链结构
 * @param <E>
 * <AUTHOR>
 */
@JsonSerialize(using = ChainSerializer.class)
public class ChainBase<E> implements Chain<E> {

    /**
     * 链中包含了多少元素
     */
    private final transient AtomicInteger size = new AtomicInteger(0);

    /**
     * 头
     */
    private final transient AtomicReference<Entry> head = new AtomicReference<>(null);

    /**
     * 尾
     */
    private final transient AtomicReference<Entry> last = new AtomicReference<>(null);

    /**
     * 链中包含了多少元素
     */
    @Override
    public int size() {
        return size.get();
    }

    /**
     * 获取链的头，null表示空链
     */
    @Override
    public E getHead() {
        Entry entry = head.get();
        return entry != null ? entry.data : null;
    }

    /**
     * 获取链的尾，null表示空链
     */
    @Override
    public E getTail() {
        Entry entry = last.get();
        return entry != null ? entry.data : null;
    }

    /**
     * pop tail
     */
    @Override
    public E popTail() {
        Entry e = last.get();
        if (e == null)
            return null;

        Entry newLast = e.prev;
        if (newLast != null) {
            newLast.next = null;
            last.set(newLast);
        } else {
            head.set(null);
            last.set(null);
        }
        size.decrementAndGet();

        E r = e.data;
        e.destroy();
        return r;
    }

    /**
     * pop head
     */
    @Override
    public E popHead() {
        Entry e = head.get();
        if (e == null)
            return null;

        Entry newHead = e.next;
        if (newHead != null) {
            newHead.prev = null;
            head.set(newHead);
        } else {
            head.set(null);
            last.set(null);
        }
        size.decrementAndGet();

        E r = e.data;
        e.destroy();
        return r;
    }

    /**
     * 获取链中指定索引的成员
     *
     * @param i 索引
     * @return 成员
     * @throws IndexOutOfBoundsException 如果索引越界
     */
    @Override
    public E get(int i) {
        if (i < 0 || i >= size.get()) {
            throw new IndexOutOfBoundsException("Index: " + i + ", Size: " + size.get());
        }

        Entry e = head.get();
        for (int j = 0; j < i; j++) {
            e = e.next;
        }

        return e.data;
    }

    /**
     * 获取链中所有的成员
     *
     * @return 路径列表
     */
    @Override
    public List<E> getAll() {
        List<E> list = new ArrayList<>(size.get());
        for (Entry e = head.get(); e != null; e = e.next) {
            list.add(e.data);
        }
        return list;
    }

    /**
     * 将其他链追加到本链尾部
     * TODO: 追加操作需要other是克隆体，否则可能会出致命问题
     */
    public void append(ChainBase<E> other) {
        if (other == null || other.size.get() == 0) {
            return;
        }

        if (head.get() == null) {
            head.set(other.head.get());
        } else {
            last.get().next = other.head.get();
            other.head.get().prev = last.get();
        }

        last.set(other.last.get());
        size.addAndGet(other.size.get());
    }

    /**
     * 将其他链插入到本链头之前
     * TODO: 追加操作需要other是克隆体，否则可能会出致命问题
     */
    public void prepend(ChainBase<E> other) {
        if (other == null || other.size.get() == 0) {
            return;
        }

        if (head.get() == null) {
            last.set(other.last.get());
        } else {
            other.last.get().next = head.get();
            head.get().prev = other.last.get();
        }
        head.set(other.head.get());
        size.addAndGet(other.size.get());
    }

    /**
     * 避免链被污染，克隆一份新的链
     */
    @Override
    public void cloneTo(Chain<E> target) {
        if (target == null) return;
        target.clear();

        for (Entry e = head.get(); e != null; e = e.next) {
            target.add(e.data);
        }
    }

    // No longer Serializable!
    private class Entry {
        E data;
        Entry next;
        Entry prev;

        /**
         * 断掉所有引用，交给jvm gc
         */
        void destroy() {
            next = null;
            prev = null;
            data = null;
        }
    }

    /**
     * Appends the specified Route to the list
     */
    @Override
    public void add(E r) {
        if (r == null) return;

        Entry e = new Entry();
        e.data = r;

        if (head.get() == null) {
            head.set(e);
        } else {
            last.get().next = e;
            e.prev = last.get();
        }

        last.set(e);
        size.incrementAndGet();
    }

    @Override
    public boolean exists(E r) {
        if (r == null) return false;
        for (Entry e = head.get(); e != null; e = e.next) {
            if (e.data.equals(r)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void clear() {
        Entry e;
        while ((e = head.get()) != null) {
            head.set(e.next);
            e.destroy();
        }
        size.set(0);
    }

    /**
     * 避免内存泄漏与溢出，这里必须采用手动序列化形式实现链表的序列化
     */
    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();

        // Write out size
        s.writeInt(size.get());

        // Write out all elements in the proper order.
        for (Entry e = head.get(); e != null; e = e.next)
            s.writeObject(e.data);
    }

    @SuppressWarnings("unchecked")
    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();

        // Read in size
        size.set(s.readInt());

        // Read in all elements in the proper order.
        for (int i = 0; i < size.get(); i++) {
            add((E) s.readObject());
        }
    }
}
