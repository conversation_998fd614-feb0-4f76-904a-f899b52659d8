package com.honichang.dispatch.model;

import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Position;
import com.honichang.point.domain.MapPath;
import lombok.Data;

@Data
public class Path implements IPath {

    /** 地图ID **/
    private long mapId;
    /** XMAP路径ID **/
    private String xmapId;
    /** XMAP路径名称 **/
    private String instanceName;
    /** 路径类型: bezier_curve贝塞尔曲线|straight_line直线|convex凸弧线|concave_arc凹弧线 **/
    private String routeType;
    /** 弧形路径的弧度值(仅弧线类型需要),单位:rad **/
    private double radian;
    /** XMAP起始 **/
    private IPosition startPos;
    private IPosition endPos;
    private IPosition ctrlPos1;
    private IPosition ctrlPos2;
    /** 角度补偿 **/
    private double angleCompensation;
    /** 方向: forward正走 | backward倒走 **/
    private String direction;
    /** start至end的长度，单位：米 **/
    private double length;

    /**
     * 机器人是否倒着走，非路径的方向
     */
    private boolean isBackward;

    public Path convert(MapPath path) {
        Path p = new Path();
        p.setMapId(path.getMapId());
        p.setXmapId(path.getXmapId());
        p.setInstanceName(path.getInstanceName());
        p.setRouteType(path.getRouteType());
        p.setRadian(path.getRadian());
        p.setStartPos(new Position(path.getStartX(), path.getStartY()));
        p.setEndPos(new Position(path.getEndX(), path.getEndY()));
        p.setCtrlPos1(new Position(path.getControlX1(), path.getControlY1()));
        p.setCtrlPos2(new Position(path.getControlX2(), path.getControlY2()));
        p.setAngleCompensation(path.getAngleCompensation());
        p.setDirection(path.getDirection());
        p.setLength(path.getRealLength());
        return p;
    }
}
