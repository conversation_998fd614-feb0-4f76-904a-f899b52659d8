package com.honichang.dispatch.helper;

import com.honichang.common.constant.Constants;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.enums.MoveDirectionEnum;
import com.honichang.dispactch.util.GeoCalcUtil;
import com.honichang.exception.RobotNotOnAnyPathException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 路径计算助手
 *
 * <AUTHOR>
 */
@Slf4j
public final class CalcRobotPathHelper {


    // region 👇选择前后左右指令需要给的路径

    @NonNull
    public static IPath selectPath(long robotId, @NonNull MoveDirectionEnum direction) {
        RobotContext rc = RobotContext.get(robotId);
        MapContext mc = MapContext.get(rc.getMapId());

        if (rc.getPosition() == null || StringUtils.isEmpty(rc.getPosition().getXmapId())) {
            throw new ServiceException("机器人不在点位上，无法控制方向！");
        }

        // 计算目标角度
        double targetAngle = GeoCalcUtil.calcTargetAngle(rc.getTheta(), direction);

        IPath bestPath = null;
        double minAngleDiff = Double.MAX_VALUE;
        double maxLength = 0;

        // 筛选所有起点在当前点的路径
        List<IPath> nextPaths = mc.getNextPaths(rc.getPosition());

        // 如果没有有效路径
        if (nextPaths.isEmpty()) {
            log.error("当前机器人在点位{}({},{})上，没有可用路径！",
                    rc.getPosition().getXmapId(), rc.getPosition().getX(), rc.getPosition().getY());
            throw new ServiceException(StringUtils.format("向{}方向没有可用路径！", direction.getDescription()));
        }

        // 寻找最佳匹配路径
        for (IPath path : nextPaths) {
            double pathAngle = path.calculateAngle();
            double angleDiff = GeoCalcUtil.normalizeAngleDiff(pathAngle, targetAngle);
            double pathLength = path.getLength();

            // 选择角度最接近且路径最长的
            if (angleDiff < minAngleDiff ||
                    (Math.abs(angleDiff - minAngleDiff) < Constants.EPSILON && pathLength > maxLength)) {
                minAngleDiff = angleDiff;
                maxLength = pathLength;
                bestPath = path;
            }
        }

        // 检查找到的路径是否合理(角度差小于π/4，即45度)
        if (bestPath == null || minAngleDiff > Math.PI / 4) {
            log.error("当前机器人在点位{}({},{})上，朝向角{}，向{}没有路！",
                    rc.getPosition().getXmapId(), rc.getPosition().getX(), rc.getPosition().getY(),
                    rc.getTheta(), direction.getDescription());
            throw new ServiceException(
                    String.format("向%s方向没有可用路径，当前角度为%.3f！",
                            direction.getDescription(), rc.getTheta()));
        }

        return bestPath;
    }

    // endregion

    // region 👇查找最近的十字路口

    /**
     * 查找已走过路线中，离点位最近的十字路口，为了躲避其他机器人用
     *
     * @param robotId 机器人id
     * @return 成功返回十字路口xmap点位，失败返回null
     */
    @Nullable
    public static IPosition getNearestCrossPointInPassed(long robotId) {
        RobotContext rc = RobotContext.get(robotId);

        PathChain<IPath> completedPaths = rc.getCompletedPaths();
        if (completedPaths.isEmpty())
            completedPaths = rc.getHisCompletedPaths();

        final AtomicReference<IPosition> find = new AtomicReference<>(null);

        if (Chain.isNotEmpty(completedPaths)) {
            AtomicBoolean isBreak = new AtomicBoolean(false);
            completedPaths.forReverseEach(p -> {
                if (isBreak.get()) {
                    return;
                }

                Path reversePath = MapContext.getReversePath(p, rc.getMapId());
                // 单向路径，不能返回
                if (reversePath == null) {
                    isBreak.set(true);
                    return;
                }

                // 如果是十字路口则返回
                if (reversePath.getEndPos().isCrossroads()) {
                    find.set(reversePath.getEndPos());
                    isBreak.set(true);
                }
            });
        }

        return find.get();
    }

    /**
     * 查找最近的十字路口，按距离排序
     *
     * @param robotId 机器人id
     * @return 成功返回十字路口xmap点位，失败返回null
     */
    @NonNull
    public static List<IPosition> getNearestCrossPoint(long robotId) {
        List<IPosition> crossPoints = __calcNearestCrossPoint(robotId, true);

        // 从机器人当前点位距离这些点位的顺序排序
        IPosition currentPos = getNearestPoint(robotId);

        MapContext mc = MapContext.getByRobotId(robotId);

        crossPoints.sort((o1, o2) -> {
            double dist1 = mc.getDist(currentPos.getXmapId(), o1.getXmapId());
            double dist2 = mc.getDist(currentPos.getXmapId(), o2.getXmapId());
            return Double.compare(dist1, dist2);
        });
        return crossPoints;
    }

    /**
     * 计算最近的十字路口<br/>
     * 这里先是简单的避让附近的机器人
     *
     * @param robotId   机器人id
     * @param needAvoid 是否需要躲避其他机器人
     * @return 成功返回十字路口xmap点位，失败返回空列表
     */
    @NonNull
    private static List<IPosition> __calcNearestCrossPoint(long robotId, boolean needAvoid) {
        RobotContext rc = RobotContext.get(robotId);

        if (rc.getPosition() != null && rc.getPosition().isCrossroads()) {
            return Collections.singletonList(rc.getPosition());
        }

        String xmapPathId = rc.getXmapPathId();
        List<Path> paths = MapContext.getPathsByXmapId(xmapPathId, rc.getMapId());
        if (paths.isEmpty()) {
            throw new RobotNotOnAnyPathException();
        }

        final List<String> excludePaths = new ArrayList<>();
        if (needAvoid) {
            // 避开其他机器人
            RobotContext.values().forEach(robot -> {
                if (robot.getRobotId() != robotId && robot.getXmapPathId() != null) {
                    excludePaths.add(robot.getXmapPathId());
                }
            });
        }

        MapContext mc = MapContext.get(rc.getMapId());

        List<IPosition> result = new ArrayList<>();
        if (rc.getPosition() == null || StringUtils.isEmpty(rc.getPosition().getXmapId())) {
            // 机器人不在点位上，则需要判断起始两端
            result.addAll(__calcNearestCrossPoint(paths.get(0).getStartPos(), mc, excludePaths));
            result.addAll(__calcNearestCrossPoint(paths.get(0).getEndPos(), mc, excludePaths));
        } else {
            result.addAll(__calcNearestCrossPoint(rc.getPosition(), mc, excludePaths));
        }

        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 计算最近的十字路口
     *
     * @param position     位置
     * @param mc           地图上下文
     * @param excludePaths 需要排除的路径
     * @return 成功返回十字路口xmap点位列表，失败返回空列表
     */
    @NonNull
    private static List<IPosition> __calcNearestCrossPoint(@NonNull IPosition position, MapContext mc, @NonNull List<String> excludePaths) {
        List<IPosition> results = new ArrayList<>();

        if (position.isCrossroads()) {
            return Collections.singletonList(position);
        }

        List<IPath> paths = mc.getNextPaths(position, excludePaths);

        for (IPath p : paths) {
            // 如果终点是十字路口，则加入结果列表
            if (p.getEndPos().isCrossroads()) {
                results.add(p.getEndPos());
            } else {
                excludePaths.add(p.getXmapId());
                results.addAll(__calcNearestCrossPoint(p.getEndPos(), mc, excludePaths));
            }
        }

        return results;
    }

    // endregion

    // region 👇查找点位相关方法

    // region 最近的业务点位

    /**
     * 查找机器人所在位置最近的BizPoint
     *
     * @return 成功返回最近的biz点位，失败返回null
     */
    @Nullable
    public static IPosition getNearestBizPoint(long robotId) {
        RobotContext rc = RobotContext.get(robotId);
        if (rc.getPosition() != null && rc.getPosition().getBizPointId() != null) {
            return rc.getPosition();
        } else {
            if (rc.getPosition() == null) {
                throw new ServiceException("未获取机器人位置信息！");
            }
            return __getNearestBizPoint(rc.getPosition().getX(), rc.getPosition().getY(), rc.getXmapPathId(), rc.getMapId());
        }
    }

    @Nullable
    private static IPosition __getNearestBizPoint(double x, double y, String xmapPathId, long mapId) {
        List<Path> paths = MapContext.getPathsByXmapId(xmapPathId, mapId);
        if (paths.isEmpty()) throw new ServiceException("路径不存在: " + xmapPathId, HttpStatus.ERROR);
        // 两条路径是一条
        Path path = paths.get(0);

        if (path.getStartPos().getBizPointId() != null && path.getEndPos().getBizPointId() != null) {
            return __getNearestPoint(x, y, path);
        } else if (path.getStartPos().getBizPointId() != null) {
            return path.getStartPos();
        } else if (path.getEndPos().getBizPointId() != null) {
            return path.getEndPos();
        }

        MapContext mc = MapContext.get(path.getMapId());
        final List<String> excludeXPathId = new ArrayList<>();
        // start & end is null
        List<IPosition> pl1 = __getNearestBizPoint(path.getStartPos(), mc, excludeXPathId);
        List<IPosition> pl2 = __getNearestBizPoint(path.getEndPos(), mc, excludeXPathId);

        pl1.sort((a, b) -> {
            double distA = mc.getDist(path.getStartPos().getXmapId(), a.getXmapId());
            double distB = mc.getDist(path.getStartPos().getXmapId(), b.getXmapId());
            return Double.compare(distA, distB);
        });
        pl2.sort((a, b) -> {
            double distA = mc.getDist(path.getEndPos().getXmapId(), a.getXmapId());
            double distB = mc.getDist(path.getEndPos().getXmapId(), b.getXmapId());
            return Double.compare(distA, distB);
        });

        IPosition p1 = pl1.isEmpty() ? null : pl1.get(0);
        IPosition p2 = pl2.isEmpty() ? null : pl2.get(0);
        IPosition fp;
        double dist;

        if (p1 == null && p2 == null)
            return null;
        else if (p2 == null) {
            dist = mc.getDist(path.getStartPos().getXmapId(), p1.getXmapId());
            fp = p1;
        } else if (p1 == null) {
            dist = mc.getDist(path.getEndPos().getXmapId(), p2.getXmapId());
            fp = p2;
        } else {
            double dist1 = mc.getDist(path.getStartPos().getXmapId(), p1.getXmapId());
            double dist2 = mc.getDist(path.getEndPos().getXmapId(), p2.getXmapId());
            if (dist1 + Math.hypot(x - path.getStartPos().getX(), y - path.getStartPos().getY()) <
                    dist2 + Math.hypot(x - path.getEndPos().getX(), y - path.getEndPos().getY())) {
                dist = dist1;
                fp = p1;
            } else {
                dist = dist2;
                fp = p2;
            }
        }

        if (dist == MapContext.UNREACHABLE_MM) {
            return null;
        }
        return fp;
    }

    @NonNull
    private static List<IPosition> __getNearestBizPoint(@NonNull IPosition pos, @NonNull MapContext mc, @NonNull List<String> excludeXPathId) {

        List<IPath> nextPaths = mc.getNextPaths(pos, excludeXPathId);
        if (nextPaths.isEmpty()) return Collections.emptyList();

        List<IPosition> result = new ArrayList<>();
        for (IPath path : nextPaths) {
            if (path.getEndPos().getBizPointId() != null) {
                result.add(path.getEndPos());
            } else {
                excludeXPathId.add(path.getXmapId());
                result.addAll(__getNearestBizPoint(path.getEndPos(), mc, excludeXPathId));
            }
        }
        return result;
    }

    // endregion

    // region 最近的地图点位

    /**
     * 查找机器人当前位置的最近点位
     * 应用场景：AI报警，报告最近柱位
     *
     * @param robotId 机器人id
     * @return 成功返回最近xmap点位，失败返回null
     */
    @NonNull
    public static IPosition getNearestPoint(long robotId) {
        RobotContext rc = RobotContext.get(robotId);
        if (rc.getPosition() != null && StringUtils.isNotEmpty(rc.getPosition().getXmapId())) {
            return rc.getPosition();
        } else {
            if (rc.getPosition() == null) {
                throw new ServiceException("未获取机器人位置信息！");
            }
            return __getNearestPoint(rc.getPosition().getX(), rc.getPosition().getY(), rc.getXmapPathId(), rc.getMapId());
        }
    }

    /**
     * 查找当前位置的最近点位
     * 应用场景：AI报警，报告最近柱位
     *
     * @param x          当前坐标（米）
     * @param y          当前坐标（米）
     * @param xmapPathId 当前坐标所在xmap路径id
     * @param mapId      地图id
     * @return 成功返回最近xmap点位，失败返回null
     */
    @NonNull
    public static IPosition __getNearestPoint(double x, double y, @NonNull String xmapPathId, long mapId) {
        List<Path> paths = MapContext.getPathsByXmapId(xmapPathId, mapId);
        if (paths.isEmpty()) throw new ServiceException("路径不存在: " + xmapPathId, HttpStatus.ERROR);

        Path p = paths.get(0);
        // 比较path两端距离
        return __getNearestPoint(x, y, p);
    }

    /**
     * 查找当前位置的最近点位
     * 应用场景：AI报警，报告最近柱位
     *
     * @param x 当前坐标（米）
     * @param y 当前坐标（米）
     * @param p 机器人当前路径
     * @return 成功返回最近xmap点位，失败返回null
     */
    @NonNull
    public static IPosition __getNearestPoint(double x, double y, @NonNull Path p) {
        // 比较path两端距离
        double distStart = Math.hypot(x - p.getStartPos().getX(), y - p.getStartPos().getY());
        double distEnd = Math.hypot(x - p.getEndPos().getX(), y - p.getEndPos().getY());
        return distStart < distEnd ? p.getStartPos() : p.getEndPos();
    }

    /**
     * 查找机器人背后的最近点位
     * 应用场景：遭遇障碍后反方向就近停靠
     *
     * @param robotId 机器人id
     * @return 成功返回最近xmap点位，失败返回null
     */
    @NonNull
    public static IPosition getNearestPointBackward(long robotId) {
        throw new UnsupportedOperationException();
//        RobotContext rc = RobotContext.get(robotId);
//        if (rc.getXmapPathId() == null) {
//            throw new RobotNotOnAnyPathException();
//        }
//        if (rc.getPosition() == null) {
//            throw new ServiceException("未获取机器人位置信息！");
//        }
//
//        // 如果机器人不在具体的点上，则直接返回本路径的起点
//        if (StringUtils.isEmpty(rc.getPosition().getXmapId())) {
//            return rc.getXmapPathId().getStartPos();
//        }
//
//        // 如果在具体的点上，则查看是否已经走过的路线，有则直接反来时的路径的上一个start
//        if (Chain.isNotEmpty(rc.getCompletedPaths())) {
//            return rc.getCompletedPaths().getTail().getStartPos();
//        }
//
//        MapContext mc = MapContext.getByRobotId(robotId);
//        List<IPath> prePaths = mc.getPreviousPath(rc.getXmapPathId());
//        // 如果没有则判断是不是十字路口，如果不是则就是路径上一个路径
//        if (rc.getPosition().isCross())) {
//            if (!prePaths.isEmpty()) {
//                return prePaths.get(0).getStartPos();
//            }
//        }
//
//        // 是十字路口，暂时返回当前路径的起点
//        // TODO: 未来需要判断机器人theta朝向计算
//        return rc.getXmapPathId().getStartPos();
    }

    // endregion

    // endregion

}
