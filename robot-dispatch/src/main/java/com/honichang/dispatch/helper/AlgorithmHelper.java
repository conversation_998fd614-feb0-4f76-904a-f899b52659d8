package com.honichang.dispatch.helper;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.dispactch.model.enums.PointClassNameEnum;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 核心算法类
 *
 * <AUTHOR>
 */
public class AlgorithmHelper {

    // region AGV倒走正走算法

    /**
     * 根据十字路口到目标点的期望朝向，计算在当前路径下应该是正走还是倒走？
     * <p/>
     * 核心逻辑：
     * 1. 在十字路口，机器人需要选择正确的方向进入目标路径
     * 2. 因为小巷内不允许调头，所以必须在十字路口就选对方向
     * 3. 只考虑从十字路口到目标点的期望朝向，机器人会自动调整车头朝向
     *
     * @param crossPoint         十字路口点位（必须是十字路口，否则无法挑头）
     * @param targetPoint        目标识别点
     * @param pathsCrossToTarget 从十字路口到目标点位的路径列表
     * @return 1正走 2倒走
     */
    public static int calcDirection(IPosition crossPoint, IPosition targetPoint, List<IPath> pathsCrossToTarget) {
        if (pathsCrossToTarget == null || pathsCrossToTarget.isEmpty()) {
            throw new ServiceException("无法从十字路口找到通往目标点的路径！");
        }

        if (!PointClassNameEnum.CROSSROADS.getCode().equals(crossPoint.getClassName())) {
            throw new ServiceException("计算的起点不是十字路口，无法计算进入巷道内的朝向！");
        }

        // 目标点的期望朝向角
        double expectedAngle = targetPoint.getAngle();

        // 找到从十字路口出发的第一条路径
        IPath firstPath = pathsCrossToTarget.get(0);
        // 获取路径的正向和反向朝向角
        double pathForwardAngle = getPathForwardAngle(firstPath, crossPoint);
        double pathBackwardAngle = normalizeAngle(pathForwardAngle + Math.PI); // 反向角度

        // 计算期望朝向角与路径正向、反向的角度差
        double expectedForwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathForwardAngle));
        double expectedBackwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathBackwardAngle));

        // 选择与期望朝向角度差更小的方向
        return expectedForwardDiff <= expectedBackwardDiff ? 1 : 2;
    }

    /**
     * 获取路径从指定点出发的正向角度
     */
    private static double getPathForwardAngle(IPath path, IPosition fromPoint) {
        IPosition startPoint = path.getStartPos();
        IPosition endPoint = path.getEndPos();

        // 如果fromPoint是起点，则正向是从起点到终点
        if (fromPoint.equalCo(startPoint)) {
            return calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
                    endPoint.getX(), endPoint.getY());
        }
        // 如果fromPoint是终点，则正向是从终点到起点
        else if (fromPoint.equalCo(endPoint)) {
            return calcAngleBetweenPoints(endPoint.getX(), endPoint.getY(),
                    startPoint.getX(), startPoint.getY());
        }

        // 默认从起点到终点
        return calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
                endPoint.getX(), endPoint.getY());
    }

    /**
     * 计算两点之间的角度（弧度）
     */
    public static double calcAngleBetweenPoints(double x1, double y1, double x2, double y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    }

    /**
     * 标准化角度到 [-π, π] 范围
     */
    private static double normalizeAngle(double angle) {
        while (angle > Math.PI) {
            angle -= 2 * Math.PI;
        }
        while (angle < -Math.PI) {
            angle += 2 * Math.PI;
        }
        return angle;
    }

    /**
     * 计算两个角度之间的差值，结果在 [-π, π] 范围内
     */
    private static double normalizeAngleDiff(double angleDiff) {
        return normalizeAngle(angleDiff);
    }

    // endregion

    // region 机器人位置计算

    /**
     * 从RobotContext中确定当前机器人所处的点位<br/>
     * 如果不在点位上则看当前所在路径，取机器人头部朝向路径上的点
     *
     * @param robotId 机器人id
     * @return 当前机器人所在点位
     **/
    public static IPosition getCurrentOrBehindPosition(long robotId) {
        // 获取机器人上下文
        com.honichang.dispatch.context.RobotContext robotContext = com.honichang.dispatch.context.RobotContext.get(robotId);
        if (robotContext == null) {
            throw new ServiceException("机器人上下文不存在！");
        }

        // 1. 如果机器人当前在点位上，直接返回该点位
        if (robotContext.getXmapPointId() != null && !robotContext.getXmapPointId().isEmpty()) {
            // 从地图上下文中获取点位信息
            com.honichang.dispatch.context.MapContext mapContext = com.honichang.dispatch.context.MapContext.get(robotContext.getMapId());
            if (mapContext != null && mapContext.getPoints() != null) {
                for (com.honichang.dispactch.model.Position point : mapContext.getPoints()) {
                    if (robotContext.getXmapPointId().equals(point.getXmapId())) {
                        return point;
                    }
                }
            }
        }

        // 2. 如果不在点位上，则根据当前所在路径和朝向确定头部朝向的点位
        if (robotContext.getXmapPathId() != null && !robotContext.getXmapPathId().isEmpty()) {
            return getPositionOnPath(robotContext);
        }

        // 3. 如果都没有，返回机器人当前坐标作为位置
        return robotContext.getPosition();
    }

    /**
     * 根据机器人在路径上的位置和朝向，确定头部朝向的点位
     */
    private static IPosition getPositionOnPath(com.honichang.dispatch.context.RobotContext robotContext) {
        // 获取地图上下文
        com.honichang.dispatch.context.MapContext mapContext = com.honichang.dispatch.context.MapContext.get(robotContext.getMapId());
        if (mapContext == null || mapContext.getPaths() == null) {
            return robotContext.getPosition();
        }

        // 找到当前所在的路径
        com.honichang.dispactch.model.Path currentPath = null;
        for (com.honichang.dispactch.model.Path path : mapContext.getPaths()) {
            if (robotContext.getXmapPathId().equals(path.getXmapId())) {
                currentPath = path;
                break;
            }
        }

        if (currentPath == null) {
            return robotContext.getPosition();
        }

        // 根据机器人的朝向和路径方向确定头部朝向的点位
        return determineHeadPosition(robotContext, currentPath);
    }

    /**
     * 确定机器人头部朝向的点位
     */
    private static IPosition determineHeadPosition(com.honichang.dispatch.context.RobotContext robotContext, com.honichang.dispactch.model.Path currentPath) {
        double robotTheta = robotContext.getTheta();
        int direction = robotContext.getDirection(); // 1正走 2倒走

        IPosition startPos = currentPath.getStartPos();
        IPosition endPos = currentPath.getEndPos();

        // 计算路径的正向角度（从起点到终点）
        double pathAngle = calcAngleBetweenPoints(
            startPos.getX(), startPos.getY(),
            endPos.getX(), endPos.getY()
        );

        // 计算机器人朝向与路径正向的角度差
        double angleDiff = normalizeAngleDiff(robotTheta - pathAngle);

        // 根据角度差和行进方向确定头部朝向的点位
        if (direction == 1) { // 正走
            // 正走时，如果机器人朝向与路径正向一致（角度差小于π/2），头部朝向终点
            if (Math.abs(angleDiff) <= Math.PI / 2) {
                return endPos;
            } else {
                return startPos;
            }
        } else { // 倒走
            // 倒走时，如果机器人朝向与路径反向一致（角度差大于π/2），头部朝向起点
            if (Math.abs(angleDiff) > Math.PI / 2) {
                return startPos;
            } else {
                return endPos;
            }
        }
    }

    // endregion





    // region 路径计算

    /**
     * 给定地图上的所有路径、障碍点（包含了所属路径id）<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续
     *
     * @param start     起始点
     * @param end       目标点
     * @param paths     所有路径
     * @param obstacles 所有障碍点
     * @return 按路径长度排序后的路径链结果数组
     */
    public static List<PathChain<IPath>> calcPathChain(IPosition start, IPosition end, List<? extends IPath> paths, List<Obstacle> obstacles) {
        List<PathChain<IPath>> results = new ArrayList<>();
        // 如果路径为空
        if (paths == null || paths.isEmpty()) {
            throw new ServiceException("路径列表为空，无法计算路径！");
        }

        // 不允许起点和终点重合
        if (start.equalCo(end)) {
            return results;
        }

        // 获取包含起始点的，且可以往下走的路径
        List<IPath> containStartPosPath = paths.stream()
                .filter(p -> p.isStartPoint(start))
                .collect(Collectors.toList());
        // 没有包含起始点的路径，说明起始点错误，返回空结果
        if (containStartPosPath.isEmpty()) {
            return results;
        }

        for (IPath path : containStartPosPath) {
            // 基于start点为head，预备一个路径链
            PathChain<IPath> root = new PathChain<>();
            root.add(path); // TODO: 这里是否需要克隆?
            __tryAddIfArrive(results, paths, root, start, start, end, obstacles);
        }

        // 根据路径链的长度排序，越短优先
        results.sort(Comparator.comparingDouble(PathChain::getPathLength));

        return results;
    }

    /**
     * 推算end目标是否可达，如果可达则添加到结果数组中，不可达则什么都不做
     *
     * @param pathChainList 结果数组
     * @param paths         所有路径
     * @param parent        父路径链（null表示是根路径链），用于处理分叉路口的递归计算
     * @param beginning     最初的起始点
     * @param start         parent的起始点（因为机器人路线可以是both，所以不能单纯的判断end，start也要判断！）
     * @param finishing     目标点
     * @param obstacles     障碍点
     */
    private static void __tryAddIfArrive(List<PathChain<IPath>> pathChainList, List<? extends IPath> paths, PathChain<IPath> parent, IPosition beginning, IPosition start, IPosition finishing, List<Obstacle> obstacles) {
        // 如果本路径包含了障碍点，则说明路不通，跳过
        if (obstacles.stream().anyMatch(o -> o.getXmapPathId() != null && o.getXmapPathId().equals(parent.getTail().getXmapId()))) {
            return;
        }

        // 获取parent的尾部路径的起始点和结束点
        IPosition startPos = parent.getTail().getStartPos();
        IPosition endPos = parent.getTail().getEndPos();
        // 因为路径方向未知，所以要计算出真正的本次实际的另一端
        IPosition realEndPos = startPos.equalCo(start) ? endPos : startPos;
        IPosition realStartPos = startPos.equalCo(start) ? startPos : endPos;

        // 成功抵达：则加入结果数组并返回！！
        if (realEndPos.equalCo(finishing)) {
            // 这里必须建立全新的副本Entry链，否则会被后面的路径链污染
            pathChainList.add(parent.clone());
            return;
        }

        // 闭环回到原点，也没抵达，说明这个环路上没有目标点，跳过，不跳过就会进入无限递归
        if (realEndPos.equalCo(beginning)) {
            return;
        }

        // 未抵达，则获取除尾链路径外与realEndPos相连的路径
        List<IPath> containEndPosPaths = paths.stream()
                .filter(r -> !r.getXmapId().equals(parent.getTail().getXmapId()) &&
                        ((r.getStartPos().equalCo(realEndPos) || r.getEndPos().equalCo(realEndPos)) && r.getDirection().equals("both")) ||
                        (r.getStartPos().equalCo(realEndPos) && r.getDirection().equals("forward")) ||
                        (r.getEndPos().equalCo(realEndPos) && r.getDirection().equals("backward"))
                )
                .collect(Collectors.toList());
        // 如果没有包含realEndPos的其他路径，说明是死胡同，跳过
        if (containEndPosPaths.isEmpty()) {
            return;
        }

        for (IPath path : containEndPosPaths) {
            // 查找这个路径是否已经存在在parent链中，如果已经存在说明绕回来了，自动跳过即可
            if (parent.exists(path)) {
                continue;
            }
            // 基于parentTailEndPos点为head，预备一个路径链
            PathChain<IPath> child = new PathChain<>();
            child.add(path);
            child.prepend(parent);
            // 递归调用，如果可达则添加到结果数组中
            __tryAddIfArrive(pathChainList, paths, child, beginning, realEndPos, finishing, obstacles);
        }
    }

    // endregion
}
