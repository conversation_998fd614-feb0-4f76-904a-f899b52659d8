package com.honichang.dispatch.helper;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.dispactch.util.GeoCalcUtil;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * 核心算法类
 *
 * <AUTHOR>
 */
public class AlgorithmHelper {

    // region 👇AGV倒走正走算法

    /**
     * 根据十字路口到目标点的期望朝向，计算在当前路径下应该是正走还是倒走？
     * <p/>
     * 核心逻辑：
     * 1. 在十字路口，机器人需要选择正确的方向进入目标路径
     * 2. 因为小巷内不允许调头，所以必须在十字路口就选对方向
     * 3. 只考虑从十字路口到目标点的期望朝向，机器人会自动调整车头朝向
     *
     * @param crossPoint         十字路口点位（必须是十字路口，否则无法挑头）
     * @param targetPoint        目标识别点
     * @param pathsCrossToTarget 从十字路口到目标点位的路径列表
     * @return 1正走 2倒走
     */
    @NonNull
    public static DirectionEnum calcDirection(
            @NonNull IPosition crossPoint,
            @NonNull IPosition targetPoint,
            List<IPath> pathsCrossToTarget) {

        if (pathsCrossToTarget == null || pathsCrossToTarget.isEmpty()) {
            throw new ServiceException("无法从十字路口找到通往目标点的路径！", HttpStatus.ERROR);
        }

        if (!crossPoint.isCrossroads()) {
            throw new ServiceException("计算的起点不是十字路口，无法计算进入巷道内的朝向！", HttpStatus.ERROR);
        }

        // 目标点的期望朝向角
        double expectedAngle = targetPoint.getAngle();

        // 找到从十字路口出发的第一条路径
        IPath firstPath = pathsCrossToTarget.get(0);
        // 获取路径的正向和反向朝向角
        double pathForwardAngle = __getPathForwardAngle(firstPath, crossPoint);
        double pathBackwardAngle = GeoCalcUtil.normalizeAngle2(pathForwardAngle + Math.PI); // 反向角度

        // 计算期望朝向角与路径正向、反向的角度差
        double expectedForwardDiff = Math.abs(GeoCalcUtil.normalizeAngle2(expectedAngle - pathForwardAngle));
        double expectedBackwardDiff = Math.abs(GeoCalcUtil.normalizeAngle2(expectedAngle - pathBackwardAngle));

        // 选择与期望朝向角度差更小的方向
        return expectedForwardDiff <= expectedBackwardDiff ? DirectionEnum.FORWARD : DirectionEnum.BACKWARD;
    }

    /**
     * 获取路径从指定点出发的正向角度
     */
    private static double __getPathForwardAngle(@NonNull IPath path, @NonNull IPosition fromPoint) {
        IPosition startPoint = path.getStartPos();
        IPosition endPoint = path.getEndPos();

        // 如果fromPoint是起点，则正向是从起点到终点
        if (fromPoint.equalCo(startPoint)) {
            return GeoCalcUtil.calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
                    endPoint.getX(), endPoint.getY());
        }
        // 如果fromPoint是终点，则正向是从终点到起点
        else if (fromPoint.equalCo(endPoint)) {
            return GeoCalcUtil.calcAngleBetweenPoints(endPoint.getX(), endPoint.getY(),
                    startPoint.getX(), startPoint.getY());
        }

        // 默认从起点到终点
        return GeoCalcUtil.calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
                endPoint.getX(), endPoint.getY());
    }

    // endregion


    // region 机器人位置计算

//    /**
//     * 从RobotContext中确定当前机器人所处的点位<br/>
//     * 如果不在点位上则看当前所在路径，取机器人头部朝向路径上的点
//     *
//     * @param robotId 机器人id
//     * @return 当前机器人所在点位
//     **/
//    @Nullable
//    public static IPosition getCurrentOrBehindPosition(long robotId) throws RobotNotOnAnyPathException {
//        // 获取机器人上下文
//        RobotContext robotContext = RobotContext.get(robotId);
//
//        // 1. 如果机器人当前在点位上，直接返回该点位
//        if (robotContext.getPosition() != null && StringUtils.isNotEmpty(robotContext.getPosition().getXmapId())) {
//            return robotContext.getPosition();
//        }
//
//        // 2. 如果不在点位上，则根据当前所在路径和朝向确定头部朝向的点位
//        if (robotContext.getPosition() != null) {
//            return getPositionOnPath(robotContext);
//        }
//
//        // 3. 如果都没有，返回机器人当前坐标作为位置
//        return null;
//    }
//
//    /**
//     * 根据机器人在路径上的位置和朝向，确定头部朝向的点位
//     */
//    private static IPosition getPositionOnPath(RobotContext robotContext) throws RobotNotOnAnyPathException {
//        // 找到当前所在的路径
//        Path currentPath = robotContext.getCurrentPath();
//
//        if (currentPath == null) {
//            throw new RobotNotOnAnyPathException();
//        }
//
//        // 因为机器人永远是对于路径来说是正走，所以直接判断路径即可
//        return currentPath.getEndPos();
//
//        // 根据机器人的朝向和路径方向确定头部朝向的点位
////        return determineHeadPosition(robotContext, currentPath);
//    }

//    /**
//     * 确定机器人头部朝向的点位
//     */
//    private static IPosition determineHeadPosition(RobotContext robotContext, Path currentPath) {
//        double robotTheta = robotContext.getTheta();
//        DirectionEnum direction = robotContext.getDirection();
//
//        IPosition startPos = currentPath.getStartPos();
//        IPosition endPos = currentPath.getEndPos();
//
//        // 计算路径的正向角度（从起点到终点）
//        double pathAngle = calcAngleBetweenPoints(
//            startPos.getX(), startPos.getY(),
//            endPos.getX(), endPos.getY()
//        );
//
//        // 计算机器人朝向与路径正向的角度差
//        double angleDiff = normalizeAngleDiff(robotTheta - pathAngle);
//
//        // 根据角度差和行进方向确定头部朝向的点位
//        if (direction == DirectionEnum.FORWARD) { // 正走
//            // 正走时，如果机器人朝向与路径正向一致（角度差小于π/2），头部朝向终点
//            if (Math.abs(angleDiff) <= Math.PI / 2) {
//                return endPos;
//            } else {
//                return startPos;
//            }
//        } else { // 倒走
//            // 倒走时，如果机器人朝向与路径反向一致（角度差大于π/2），头部朝向起点
//            if (Math.abs(angleDiff) > Math.PI / 2) {
//                return startPos;
//            } else {
//                return endPos;
//            }
//        }
//    }

    // endregion


    // endregion

}
