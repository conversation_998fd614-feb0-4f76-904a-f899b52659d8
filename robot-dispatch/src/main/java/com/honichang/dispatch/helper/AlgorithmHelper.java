package com.honichang.dispatch.helper;

import com.google.common.util.concurrent.AtomicDouble;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispatch.common.core.FixedThreadPool;
import com.honichang.exception.NoReachablePathException;
import com.honichang.exception.PositioningFailedException;
import com.honichang.exception.RobotNotOnAnyPathException;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 核心算法类
 *
 * <AUTHOR>
 */
public class AlgorithmHelper {

    // region 👇AGV倒走正走算法

//    /**
//     * 根据十字路口到目标点的期望朝向，计算在当前路径下应该是正走还是倒走？
//     * <p/>
//     * 核心逻辑：
//     * 1. 在十字路口，机器人需要选择正确的方向进入目标路径
//     * 2. 因为小巷内不允许挑头，所以必须在十字路口就选对方向
//     * 3. 只考虑从十字路口到目标点的期望朝向，机器人会自动调整车头朝向
//     *
//     * @param currentDirection   当前来路的朝向
//     * @param currentPathChain   从十字路口到目标点位的路径链
//     * @return DirectionEnum
//     */
//    public static DirectionEnum calcDirection(
//            @NonNull DirectionEnum currentDirection,
//            PathChain<IPath> currentPathChain) {
//
//        DirectionEnum result = null;
//
//        if (currentPathChain == null || currentPathChain.size() <= 0) {
//            throw new ServiceException("无法从十字路口找到通往目标点的路径！");
//        }
//
//        for (int i = currentPathChain.size() - 1; i >= 0; i--) {
//            IPath path = currentPathChain.get(i);
//            if (PointClassNameEnum.isCrossroads(path.getStartPos().getClassName())) {
//                result = DirectionEnum.FORWARD;
//                break;
//            } else if (path.isEndPoint(crossPoint)) {
//                result = DirectionEnum.BACKWARD;
//                break;
//            }
//        }
//
//        if (currentDirection != result && !PointClassNameEnum.CROSSROADS.getCode().equals(crossPoint.getClassName())) {
//            throw new ServiceException("不是十字路口或路径规划错误！");
//        }
//        return result;
//    }

//    /**
//     * 根据十字路口到目标点的期望朝向，计算在当前路径下应该是正走还是倒走？
//     * <p/>
//     * 核心逻辑：
//     * 1. 在十字路口，机器人需要选择正确的方向进入目标路径
//     * 2. 因为小巷内不允许调头，所以必须在十字路口就选对方向
//     * 3. 只考虑从十字路口到目标点的期望朝向，机器人会自动调整车头朝向
//     *
//     * @param crossPoint         十字路口点位（必须是十字路口，否则无法挑头）
//     * @param targetPoint        目标识别点
//     * @param pathsCrossToTarget 从十字路口到目标点位的路径列表
//     * @return 1正走 2倒走
//     */
//    public static int calcDirection(IPosition crossPoint, IPosition targetPoint, List<IPath> pathsCrossToTarget) {
//        if (pathsCrossToTarget == null || pathsCrossToTarget.isEmpty()) {
//            throw new ServiceException("无法从十字路口找到通往目标点的路径！");
//        }
//
//        if (!PointClassNameEnum.CROSSROADS.getCode().equals(crossPoint.getClassName())) {
//            throw new ServiceException("计算的起点不是十字路口，无法计算进入巷道内的朝向！");
//        }
//
//        // 目标点的期望朝向角
//        double expectedAngle = targetPoint.getAngle();
//
//        // 找到从十字路口出发的第一条路径
//        IPath firstPath = pathsCrossToTarget.get(0);
//        // 获取路径的正向和反向朝向角
//        double pathForwardAngle = getPathForwardAngle(firstPath, crossPoint);
//        double pathBackwardAngle = normalizeAngle(pathForwardAngle + Math.PI); // 反向角度
//
//        // 计算期望朝向角与路径正向、反向的角度差
//        double expectedForwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathForwardAngle));
//        double expectedBackwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathBackwardAngle));
//
//        // 选择与期望朝向角度差更小的方向
//        return expectedForwardDiff <= expectedBackwardDiff ? 1 : 2;
//    }
//
//    /**
//     * 获取路径从指定点出发的正向角度
//     */
//    private static double getPathForwardAngle(IPath path, IPosition fromPoint) {
//        IPosition startPoint = path.getStartPos();
//        IPosition endPoint = path.getEndPos();
//
//        // 如果fromPoint是起点，则正向是从起点到终点
//        if (fromPoint.equalCo(startPoint)) {
//            return calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
//                    endPoint.getX(), endPoint.getY());
//        }
//        // 如果fromPoint是终点，则正向是从终点到起点
//        else if (fromPoint.equalCo(endPoint)) {
//            return calcAngleBetweenPoints(endPoint.getX(), endPoint.getY(),
//                    startPoint.getX(), startPoint.getY());
//        }
//
//        // 默认从起点到终点
//        return calcAngleBetweenPoints(startPoint.getX(), startPoint.getY(),
//                endPoint.getX(), endPoint.getY());
//    }
//
//    /**
//     * 计算两点之间的角度（弧度）
//     */
//    public static double calcAngleBetweenPoints(double x1, double y1, double x2, double y2) {
//        return Math.atan2(y2 - y1, x2 - x1);
//    }
//
//    /**
//     * 标准化角度到 [-π, π] 范围
//     */
//    private static double normalizeAngle(double angle) {
//        while (angle > Math.PI) {
//            angle -= 2 * Math.PI;
//        }
//        while (angle < -Math.PI) {
//            angle += 2 * Math.PI;
//        }
//        return angle;
//    }
//
//    /**
//     * 计算两个角度之间的差值，结果在 [-π, π] 范围内
//     */
//    private static double normalizeAngleDiff(double angleDiff) {
//        return normalizeAngle(angleDiff);
//    }

    // endregion


    // region 机器人位置计算

    /**
     * 从RobotContext中确定当前机器人所处的点位<br/>
     * 如果不在点位上则看当前所在路径，取机器人头部朝向路径上的点
     *
     * @param robotId 机器人id
     * @return 当前机器人所在点位
     **/
    @Nullable
    public static IPosition getCurrentOrBehindPosition(long robotId) throws RobotNotOnAnyPathException {
        // 获取机器人上下文
        RobotContext robotContext = RobotContext.get(robotId);

        // 1. 如果机器人当前在点位上，直接返回该点位
        if (robotContext.getPosition() != null && StringUtils.isNotEmpty(robotContext.getPosition().getXmapId())) {
            return robotContext.getPosition();
        }

        // 2. 如果不在点位上，则根据当前所在路径和朝向确定头部朝向的点位
        if (robotContext.getPosition() != null) {
            return getPositionOnPath(robotContext);
        }

        // 3. 如果都没有，返回机器人当前坐标作为位置
        return null;
    }

    /**
     * 根据机器人在路径上的位置和朝向，确定头部朝向的点位
     */
    private static IPosition getPositionOnPath(RobotContext robotContext) throws RobotNotOnAnyPathException {
        // 找到当前所在的路径
        Path currentPath = robotContext.getCurrentPath();

        if (currentPath == null) {
            throw new RobotNotOnAnyPathException();
        }

        // 因为机器人永远是对于路径来说是正走，所以直接判断路径即可
        return currentPath.getEndPos();

        // 根据机器人的朝向和路径方向确定头部朝向的点位
//        return determineHeadPosition(robotContext, currentPath);
    }

//    /**
//     * 确定机器人头部朝向的点位
//     */
//    private static IPosition determineHeadPosition(RobotContext robotContext, Path currentPath) {
//        double robotTheta = robotContext.getTheta();
//        DirectionEnum direction = robotContext.getDirection();
//
//        IPosition startPos = currentPath.getStartPos();
//        IPosition endPos = currentPath.getEndPos();
//
//        // 计算路径的正向角度（从起点到终点）
//        double pathAngle = calcAngleBetweenPoints(
//            startPos.getX(), startPos.getY(),
//            endPos.getX(), endPos.getY()
//        );
//
//        // 计算机器人朝向与路径正向的角度差
//        double angleDiff = normalizeAngleDiff(robotTheta - pathAngle);
//
//        // 根据角度差和行进方向确定头部朝向的点位
//        if (direction == DirectionEnum.FORWARD) { // 正走
//            // 正走时，如果机器人朝向与路径正向一致（角度差小于π/2），头部朝向终点
//            if (Math.abs(angleDiff) <= Math.PI / 2) {
//                return endPos;
//            } else {
//                return startPos;
//            }
//        } else { // 倒走
//            // 倒走时，如果机器人朝向与路径反向一致（角度差大于π/2），头部朝向起点
//            if (Math.abs(angleDiff) > Math.PI / 2) {
//                return startPos;
//            } else {
//                return endPos;
//            }
//        }
//    }

    // endregion


    // region 👇任务排点及路径规划

    /**
     * 给任务排列节点，并规划路径
     *
     * @param robotId          机器人id
     * @param task             任务实例
     * @param taskNodes        任务实例节点
     * @param taskChain        引用参数返回规划后的任务队列
     * @param unreachableNodes 引用参数返回不可达节点
     * @param paths            引用参数返回规划后的路径
     **/
    public static void planTaskPath(
            long robotId,
            @NonNull TaskInstance task,
            @NonNull List<TaskInstanceNode> taskNodes,
            @NonNull TaskChain<TaskNode> taskChain,
            @NonNull List<Long> unreachableNodes,
            @NonNull PathChain<Path> paths) throws RobotNotOnAnyPathException, NoReachablePathException, PositioningFailedException {

        // 1. 获取机器人当前坐标、当前所在路径
        RobotContext robotContext = RobotContext.get(robotId);
        IPosition currentPos = getCurrentOrBehindPosition(robotId);
        Path currentPath = robotContext.getCurrentPath();

        if (currentPos == null) {
            // 定位失败！
            throw new PositioningFailedException();
        }

        // 2. 根据当前坐标、当前所在路径、起始点位、目标点位，规划最优路径，按照路径的长度给可能的路径链数组排序
        OptimalPathChain optimalPathChain = new OptimalPathChain();
        for (TaskInstanceNode taskNode : taskNodes) {
            FixedThreadPool.execute(() -> {
                        PathChain<IPath> pc = calcPathChainOptimal(
                                robotId,
                                currentPos,
                                taskNode.getBizPoint().getMapPoint(),
                                MapContext.get(task.getMapId()).getPaths(),
                                MapContext.get(task.getMapId()).getObstacles());
                        if (pc == null) {
                            // 不可达点位
                            unreachableNodes.add(taskNode.getId());
                        } else {
                            // 可达先放入
                            optimalPathChain.put(pc);
                        }
                    }
            );

        }

        // 判断要到达的第一优先点

        // 3. 在返回的可选的路径链列表中，循环判断当前是否与其他机器人的预分配路径冲突，如果冲突则找下一条路径链
        // 4. 返回规划的路径
    }

    /**
     * 计算从A点到任务点的最优路径，期间不能碰到A点和其他待分配任务点
     * 如果碰到其他待分配任务点说明这个不是下个要执行的任务点或这条路径不是到这个任务点的路径
     * 返回到这个任务点最近的路径链，期间要与当前最短路径链比较，如果超过则放弃这个路径链
     * 所有分析过的路径都要缓存，其他路径碰到都要跳过
     * 返回null则为不可达任务点；返回空链说明任务点就在脚下不用动
     *
     * @param start     起始点
     * @param target    目标点
     * @param taskNodes 任务节点（待分配的）
     * @param optimalPathChain 最优路径链
     * @param passedPaths 已经分析过的路径
     * @param allPaths     所有路径
     * @param obstacles 所有障碍点
     * @return 最优路径链：null不可达，空链则目标点就在脚下
     */
    @Nullable
    private static PathChain<IPath> calcPathChainOptimal(
            @NonNull IPosition start,
            @NonNull IPosition target,
            @NonNull List<TaskInstanceNode> taskNodes,
            @NonNull OptimalPathChain optimalPathChain,
            @NonNull Set<? extends IPath> passedPaths,
            @NonNull List<? extends IPath> allPaths,
            @Nullable List<Obstacle> obstacles) {

        // 如果起始点就是目标点，返回空链
        if (start.equalCo(target)) {
            return new PathChain<>();
        }

        // 获取包含起始点的，且可以往下走的路径
        List<IPath> containStartPosPath = allPaths.stream()
                .filter(p -> p.isStartPoint(start))
                .collect(Collectors.toList());
        // 没有包含起始点的路径，说明起始点错误，返回空结果
//        if (containStartPosPath.isEmpty()) {
//            return new ArrayList<>(results);
//        }

        // 2. 如果已经分析过这个路径，则跳过
        if (passedPaths.contains(start.getXmapId())) {
            return null;
        }

        // 3. 如果当前路径已经比最优路径长，则跳过
//        if (optimalPathChain.length() > 0 && optimalPathChain.length() < start.distanceTo(target)) {
//            return null;
//        }

        // 4. 分析当前路径
//        PathChain<IPath> result = calcPathChainOptimal(robotId, start, target, taskNodes, optimalPathChain, passedPaths, allPaths, obstacles);
//        if (result != null) {
//            optimalPathChain.put(result);
//        }
//        return result;
        return null;
    }

    // endregion


    // region 👇路径计算

    /**
     * 给定地图上的所有路径、障碍点（包含了所属路径id）<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续
     *
     * @param start     起始点
     * @param end       目标点
     * @param paths     所有路径
     * @param obstacles 所有障碍点
     * @return 按路径长度排序后的路径链结果数组
     */
    @Nullable
    public static PathChain<IPath> calcPathChainOptimal(
            long robotId,
            @NonNull IPosition start,
            @NonNull IPosition end,
            @NonNull List<? extends IPath> paths,
            @Nullable List<Obstacle> obstacles) {
        List<PathChain<IPath>> list = calcPathChain(robotId, start, end, paths, obstacles);
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 给定地图上的所有路径、障碍点（包含了所属路径id）<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续
     *
     * @param start     起始点
     * @param end       目标点
     * @param paths     所有路径
     * @param obstacles 所有障碍点
     * @return 按路径长度排序后的路径链结果数组
     */
    @NonNull
    public static List<PathChain<IPath>> calcPathChain(
            long robotId,
            @NonNull IPosition start,
            @NonNull IPosition end,
            @NonNull List<? extends IPath> paths,
            @Nullable List<Obstacle> obstacles) {

        Queue<PathChain<IPath>> results = new ConcurrentLinkedQueue<>();
        // 如果路径为空
        if (paths.isEmpty()) {
            throw new ServiceException("路径列表为空，无法计算路径！");
        }

        // 不允许起点和终点重合，返回空列表，即不需要动
        if (start.equalCo(end)) {
            return new ArrayList<>(results);
        }

        // 获取包含起始点的，且可以往下走的路径
        List<IPath> containStartPosPath = paths.stream()
                .filter(p -> p.isStartPoint(start))
                .collect(Collectors.toList());
        // 没有包含起始点的路径，说明起始点错误，返回空结果
        if (containStartPosPath.isEmpty()) {
            return new ArrayList<>(results);
        }

        // 从起始点开始，递归计算所有可能的路径链
        CountDownLatch latch = new CountDownLatch(containStartPosPath.size());
        for (IPath path : containStartPosPath) {
            // 基于start点为head，预备一个路径链
            FixedThreadPool.execute(() -> {
                        PathChain<IPath> root = new PathChain<>();
                        root.add(path); // TODO: 这里是否需要克隆?
                        __tryAddIfArrive(results, paths, root, start, end, obstacles);
                        latch.countDown();
                    }
            );
        }

        // 根据路径链的长度排序，越短优先
        ArrayList<PathChain<IPath>> pc = new ArrayList<>(results);
        // 使用getLengthWithFactor(long currentRobotId)排序
        pc.sort(Comparator.comparingDouble(p -> p.getLengthWithFactor(robotId)));

        return pc;
    }

    /**
     * 推算end目标是否可达，如果可达则添加到结果数组中，不可达则什么都不做
     *
     * @param pathChainList 结果数组
     * @param paths         所有路径
     * @param parent        父路径链（null表示是根路径链），用于处理分叉路口的递归计算
     * @param beginning     最初的起始点
     * @param finishing     目标点
     * @param obstacles     障碍点
     */
    private static void __tryAddIfArrive(
            @NonNull Queue<PathChain<IPath>> pathChainList,
            @NonNull List<? extends IPath> paths,
            @NonNull PathChain<IPath> parent,
            @NonNull IPosition beginning,
            @NonNull IPosition finishing,
            @Nullable List<Obstacle> obstacles) {

        // 如果本路径包含了障碍点，则说明路不通，跳过
        if (obstacles != null && obstacles.stream().anyMatch(o -> o.getXmapPathId() != null && o.getXmapPathId().equals(parent.getTail().getXmapId()))) {
            return;
        }

        // 获取parent的尾部路径的结束点
        IPosition endPos = parent.getTail().getEndPos();

        // 成功抵达：则加入结果数组并返回！！
        if (endPos.equalCo(finishing)) {
            // 这里必须建立全新的副本Entry链，否则会被后面的路径链污染
            pathChainList.add(parent.clone());
            return;
        }

        // 闭环回到原点，也没抵达，说明这个环路上没有目标点，不跳过就会进入无限递归
        if (endPos.equalCo(beginning)) {
            return;
        }

        // 超过其他路径长度，不考虑


        // 未抵达，则获取除尾链路径外与endPos相连的路径
        List<IPath> containEndPosPaths = paths.stream()
                .filter(p ->
                        !p.getXmapId().equals(parent.getTail().getXmapId()) &&
                                p.isStartPoint(endPos))
                .collect(Collectors.toList());
        // 如果没有包含realEndPos的其他路径，说明是死胡同，跳过
        if (containEndPosPaths.isEmpty()) {
            return;
        }

        for (IPath path : containEndPosPaths) {
            // 查找这个路径是否已经存在在parent链中，如果已经存在说明绕回来了，自动跳过即可
            if (parent.exists(path)) {
                continue;
            }
            // 基于parentTailEndPos点为head，预备一个路径链
            PathChain<IPath> child = new PathChain<>();
            child.add(path);
            child.prepend(parent);
            // 递归调用，如果可达则添加到结果数组中
            __tryAddIfArrive(pathChainList, paths, child, beginning, finishing, obstacles);
        }
    }

    // endregion


    private static class OptimalPathChain {

        private final AtomicReference<PathChain<IPath>> pathChain = new AtomicReference<>(null);

        private final AtomicDouble minLength = new AtomicDouble(Double.MAX_VALUE);

        public void put(PathChain<IPath> pathChain) {
            if (pathChain.getLength() < minLength.get()) {
                minLength.set(pathChain.getLength());
                this.pathChain.set(pathChain);
            }
        }

        public PathChain<IPath> get() {
            return pathChain.get();
        }

        public double length() {
            return minLength.get();
        }
    }
}
