package com.honichang.dispatch.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.honichang.dispatch.model.base.Chain;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * JSON特殊序列化器
 * <AUTHOR>
 */
public class ChainSerializer extends JsonSerializer<Chain<?>> implements ContextualSerializer {

    @Override
    public void serialize(Chain<?> chain, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        List<?> elements = chain.getAll();
        if (elements == null) {
            return;
        }
        gen.writeStartArray();
        for (Object element : elements) {
            gen.writeObject(element);
        }
        gen.writeEndArray();
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty == null) {
            return serializerProvider.findValueSerializer(Chain.class, null);
        }

        JavaType type = beanProperty.getType();
        Class<?> rawClass = type.getRawClass();

        if (Chain.class.isAssignableFrom(rawClass) || !Collection.class.isAssignableFrom(rawClass)) {
            return this;
        }

        if (type.isCollectionLikeType()) {
            JavaType contentType = type.getContentType();
            if (Chain.class.isAssignableFrom(contentType.getRawClass())) {
                return this;
            }
        }

        return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
    }
}
