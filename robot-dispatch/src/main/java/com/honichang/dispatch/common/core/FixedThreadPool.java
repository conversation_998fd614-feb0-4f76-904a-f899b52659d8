package com.honichang.dispatch.common.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 固定线程池组件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FixedThreadPool {

    private volatile ExecutorService fixedThreadPool;

    private static final int MAX_THREAD_POOL_NUM = 10;

    @PostConstruct
    public void init() {
        fixedThreadPool = java.util.concurrent.Executors.newFixedThreadPool(MAX_THREAD_POOL_NUM);
        log.info("固定线程池初始化完成: {}", MAX_THREAD_POOL_NUM);
    }

    @PreDestroy
    public void destroy() {
        if (fixedThreadPool != null) {
            fixedThreadPool.shutdown();
            try {
                if (!fixedThreadPool.awaitTermination(15, java.util.concurrent.TimeUnit.SECONDS)) {
                    log.info("固定线程池销毁超时(15s未正常关闭)，强制关闭");
                    fixedThreadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("固定线程池销毁完成");
        }
    }

    public ThreadPoolExecutor getFixedThreadPool() {
        if (fixedThreadPool == null) {
            throw new RuntimeException("固定线程池未初始化");
        }
        return (ThreadPoolExecutor) fixedThreadPool;
    }

    public <E> List<Future<E>> invokeAll(List<Callable<E>> tasks) throws InterruptedException {
        return fixedThreadPool.invokeAll(tasks);
    }

    public <T> Future<T> submit(Callable<T> task) {
        return fixedThreadPool.submit(task);
    }

    public <T> Future<T> submit(Runnable task, T result) {
        return fixedThreadPool.submit(task, result);
    }

    public void execute(Runnable task) {
        fixedThreadPool.execute(task);
    }

    public Future<?> submit(Runnable task) {
        return fixedThreadPool.submit(task);
    }

    public int getActiveCount() {
        return getFixedThreadPool().getActiveCount();
    }
}
