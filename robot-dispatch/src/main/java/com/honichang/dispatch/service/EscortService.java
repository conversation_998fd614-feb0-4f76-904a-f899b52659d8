package com.honichang.dispatch.service;

import com.honichang.dispatch.context.TaskEscortContext;

/**
 * 陪同任务服务接口
 * 
 * <AUTHOR>
 */
public interface EscortService {
    
    /**
     * 陪同任务立即执行: 0->101
     * 说明：陪同任务立即执行
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean escortRun(long taskEscortId);
    
    /**
     * 访客到达起始点确认: 102->201
     * 说明：访客到达起始点确认
     * 场景：访客触摸屏点击确认到达
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean escortConfirmVisit(long taskEscortId);
    
    /**
     * 访客在目的地完成工作后确认: 301->401
     * 说明：访客在目的地完成工作后确认
     * 场景：访客触摸屏点击确认本次目的完成，随后调度机器人送访客
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean escortConfirmComplete(long taskEscortId);
    
    /**
     * 关闭报警（暂停访客距离逻辑判断）: 301->302
     * 说明：关闭报警（暂停访客距离逻辑判断）
     * 场景：管理员暂时陪同任务关闭报警
     * 
     * @param taskEscortId 陪同任务ID
     * @param pauseMin 暂停分钟数
     * @return 成功返回true，失败返回false
     */
    boolean escortPauseAlarm(long taskEscortId, int pauseMin);
    
    /**
     * 取消关闭报警（恢复访客距离逻辑判断）: 302->301
     * 说明：取消关闭报警（恢复访客距离逻辑判断）
     * 场景：管理员恢复陪同任务关闭报警
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean escortRestoreAlarm(long taskEscortId);
    
    /**
     * 陪同任务异常终止: XXX -> XX9
     * 说明：陪同任务异常终止
     * 
     * @param taskEscortId 陪同任务ID
     * @param reason 终止原因
     * @return 成功返回true，失败返回false
     */
    boolean escortAbort(long taskEscortId, String reason);
    
    /**
     * 访客到达出口确认，陪同任务完成: 402->501
     * 说明：访客到达出口确认，陪同任务完成
     * 场景：①访客触摸屏点击确认结束；②status=402 && uwb卡超出阈值5分钟后
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean escortCompleteLeave(long taskEscortId);
    
    /**
     * 获取陪同任务上下文
     * 
     * @param taskEscortId 陪同任务ID
     * @return 陪同任务上下文
     */
    TaskEscortContext getEscortContext(long taskEscortId);
    
    /**
     * 更新陪同任务上下文
     * 
     * @param taskEscortContext 陪同任务上下文
     */
    void updateEscortContext(TaskEscortContext taskEscortContext);
    
    /**
     * 创建陪同任务上下文
     * 
     * @param taskEscortContext 陪同任务上下文
     * @return 成功返回true，失败返回false
     */
    boolean createEscortContext(TaskEscortContext taskEscortContext);
    
    /**
     * 删除陪同任务上下文
     * 
     * @param taskEscortId 陪同任务ID
     * @return 成功返回true，失败返回false
     */
    boolean deleteEscortContext(long taskEscortId);
}
