package com.honichang.dispatch.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.config.RobotDispatchConfig;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.enums.LogRobotTypeEnum;
import com.honichang.dispactch.service.DispatchCallbackService;
import com.honichang.dispactch.service.DispatchCommandService;
import com.honichang.dispactch.util.LogUtil;
import com.honichang.netty.domain.IndustrialControlResult;
import com.honichang.netty.service.IndustrialControlService;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.ConfRobotVoice;
import com.honichang.point.service.ConfRobotVoiceService;
import com.honichang.point.service.RobotChargingPileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

import static com.honichang.dispactch.model.enums.DirectionEnum.BACKWARD;
import static com.honichang.dispactch.model.enums.DirectionEnum.FORWARD;
import static com.honichang.dispactch.model.enums.PointClassNameEnum.INFRARED_DETECTION;

/**
 * 工控指令调度服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DispatchCommandServiceImpl implements DispatchCommandService {

    @Resource
    private IndustrialControlService industrialControlService;

    @Resource
    private RobotDispatchConfig robotDispatchConfig;

    @Resource
    private DispatchCallbackService callbackService;

    @Resource
    private RobotChargingPileService robotChargingPileService;

    @Resource
    private ConfRobotVoiceService confRobotVoiceService;


    // region 👇获取机器人状态[不需要主动调]

    /**
     * 获取机器人状态
     * 失败前至少有三次重试机会
     * 【注意】不需要主动调这个方法，callbackService.getRobotStatus会自动被调用
     *
     * @param robotId 机器人id
     */
    @Deprecated
    public void getRobotStatus(@NonNull Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.getRobotStatus(robotId, ""));

        callbackService.getRobotStatus(ic);
    }

    // endregion


    /**
     * 复位机器人到指定点位 TODO
     * @param robotId 机器人id
     * @param xmapId 复位的点位xmapID
     */
    @Override
    public void resetRobot(long robotId, String xmapId) {
        Position position = MapContext.getXmapPoint(xmapId, RobotContext.get(robotId).getMapId());
        if (position == null) {
            throw new ServiceException("点位不存在：" + xmapId, HttpStatus.ERROR);
        }

        Map<String, Double> params = new LinkedHashMap<>();
        params.put("x", position.getX());
        params.put("y", position.getY());
        params.put("theta", position.getAngle());

        // 机器人复位
        // TODO: 接口未定义
//        IndustrialControlResult ic = __retryDoing(() ->
//                industrialControlService.resetRobot(robotId, JSON.toJSONString(params)));
//        if (ic.getCode() != 0) {
//            // 复位失败
//            logRobotService.add(LogRobotTypeEnum.CONTROL, "复位机器人失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("复位机器人失败：" + ic.getMessage(), HttpStatus.ERROR);
//        }

        // 👇这部分逻辑不在服务端控制，由工控机统一处理成一个指令动作
//        // 机器人手动定位
//        IndustrialControlResult ic = __retryDoing(() ->
//                industrialControlService.setRobotPosition(robotId, JSON.toJSONString(params)));
//        if (ic.getCode() != 0) {
//            // 复位失败
//            logRobotService.add(LogRobotTypeEnum.CONTROL, "复位机器人失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("复位机器人失败：" + ic.getMessage(), HttpStatus.ERROR);
//        }
//
//        // 确认机器人位置
//        ic = __retryDoing(() ->
//                industrialControlService.confirmRobotPosition(robotId, ""));
//        if (ic.getCode() != 0) {
//            // 复位失败
//            logRobotService.add(LogRobotTypeEnum.CONTROL, "复位机器人失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("复位机器人失败：" + ic.getMessage(), HttpStatus.ERROR);
//        }
    }


    // region 👇UWB


    /**
     * UWB刷卡签到
     * @param robotId 机器人
     * @param success 签到成功否
     */
    @Override
    public void uwbCheckIn(Long robotId, boolean success) {
//        IndustrialControlResult ic = __retryDoing(() ->
//                industrialControlService.uwbCheckIn(robotId, success ? "1" : "0"));
//
//        if (ic.getCode() != 0) {
//            // 失败
//            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "UWB刷卡签到失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("UWB刷卡签到失败：" + ic.getMessage(), HttpStatus.ERROR);
//        }
    }


    // endregion



    // region 👇导航指令


    /**
     * 暂停导航
     *
     * @param robotId 机器人id
     */
    @Override
    public void pauseNavigation(@NonNull Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.pausetNavigation(robotId));

        if (ic.getCode() != 0) {
            // 暂停失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "暂停导航失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("暂停导航失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }


    /**
     * 继续导航
     *
     * @param robotId 机器人id
     */
    @Override
    public void resumeNavigation(@NonNull Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.resumeNavigation(robotId));

        if (ic.getCode() != 0) {
            // 继续失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "继续导航失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("继续导航失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }


    /**
     * 取消导航
     *
     * @param robotId 机器人id
     */
    @Override
    @NonNull
    public String abortNavigation(@NonNull Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.abortNavigation(robotId));

        if (ic.getCode() != 0) {
            // 取消失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "取消导航失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
//            throw new ServiceException("取消导航失败：" + ic.getMessage(), HttpStatus.ERROR);
        }

        RobotContext rc = RobotContext.get(robotId);
        rc.clearPathChain();
        return ic.getCode() == 0 ? StringUtils.EMPTY : ic.getMessage();
    }


    /**
     * 下发任务指令
     *
     * @param directiveChain 任务指令链
     */
    @Override
    public void navigateTaskDistribution(long robotId, @NonNull TaskDirectiveChain directiveChain) {
        if (directiveChain.isEmpty()) return;

        // {"id":1234,"cnt":1500,"data":[[1,9,8,101,10,10,1,1,800,1],[9,9,0,102,4,4,1,1,340,1]],"pts":[[[1,2,1],[2,3,1],[3,4,2],[4,5,2],[5,6,1],[6,7,1],[7,8,1],[8,9,1]],[]]}
        String params = __formatTaskDirectiveChain(directiveChain);
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.taskDistribution(robotId, params));

        if (ic.getCode() != 0) {
            // 失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "下发任务指令失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("下发任务指令失败：" + ic.getMessage(), HttpStatus.ERROR);
        }

        RobotContext rc = RobotContext.get(robotId);
        rc.clearPathChain();
        directiveChain.forEach(dc -> {
            if (Chain.isNotEmpty(dc.getPathChain()))
                rc.getPreAllocatedPaths().append(dc.getPathChain());
        });
    }

    /**
     * @return
     * {
     *   "id":1234,
     *   "cnt":1500,
     *   "data":[
     *     [1,9,8,101,10,10,1,1,800,1],
     *     [9,9,0,102,4,4,1,1,340,1]
     *   ],
     *   "pts":[
     *     [
     *       [1,2,1],
     *       [2,3,1],
     *       [3,4,2]
     *     ],
     *     []
     *   ]
     * }
     */
    private String __formatTaskDirectiveChain(TaskDirectiveChain directiveChain) {

        int size = directiveChain.size();
        int[][] data = new int[size][10];
        List<List<int[]>> pts = new LinkedList<>();

        AtomicInteger ati = new AtomicInteger(0);
        directiveChain.forEach(td -> {
            int i = ati.getAndIncrement();

            TaskNode taskNode = td.getTaskNode();
            PathChain<IPath> pathChain = td.getPathChain();

            BizPoint bp = taskNode.getTaskInstanceNode().getBizPoint();
            int sp = Integer.parseInt(pathChain.getHead().getStartPos().getXmapId());
            int gp = Integer.parseInt(pathChain.getTail().getEndPos().getXmapId());
            int pn = pathChain.size();
            int biz = bp.getId().intValue();
            int pat = (int) (bp.getPtzAngleTb() * 1000); // TODO: 单位转换比例需要确定
            int pal = (int) (bp.getPtzAngleLr() * 1000);
            int cf = (int) (bp.getCameraFocus() * 1000);
            int cz = (int) (bp.getCameraZoom() * 1000);
            int th = (int) (bp.getTelescopicRodH() * 1000);
            int dc = INFRARED_DETECTION.getCode().equals(bp.getClassName()) ? 2 : 1;

            data[i] = new int[]{sp, gp, pn, biz, pat, pal, cf, cz, th, dc};

            pts.add(new LinkedList<>());
            pathChain.forEach(p -> {
                int s = Integer.parseInt(p.getStartPos().getXmapId());
                int e = Integer.parseInt(p.getEndPos().getXmapId());
                // 0不指定,1正走向终点,2倒走向终点
                int d = FORWARD.getCode().equals(p.getDirection()) ? 1 :
                        (BACKWARD.getCode().equals(p.getDirection()) ? 2 : 0);
                pts.get(i).add(new int[]{s, e, d});
            });
        });


        Map<String, Object> root = new LinkedHashMap<>();
        root.put("id", directiveChain.getTaskInstance().getId().intValue());
        root.put("cnt", directiveChain.size());
        root.put("data", data);
        root.put("pts", pts);

        return JSON.toJSONString(root);
    }


    /**
     * 导航路径
     *
     * @param robotId   机器人id
     * @param bizId     业务id
     * @param pathChain 路径链
     */
    @Override
    public void navigate(long robotId, @NonNull Long bizId, @NonNull PathChain<IPath> pathChain) {
        if (pathChain.isEmpty()) return;

        // {"bid":1,"sp":1,"gp":9,"pn":9,"pts":[[1,2,1],[2,3,1],[3,4,2],[4,5,2],[5,6,1],[6,7,1],[7,8,1],[8,9,1]]}
        String params = __formatPathChain(bizId, pathChain);
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.startNavigationDirection(robotId, params));

        if (ic.getCode() != 0) {
            // 导航失败
            // TODO: 重点是如何处理导航失败的逻辑，可能是与机器人的导航逻辑相悖
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "导航失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("导航失败：" + ic.getMessage(), HttpStatus.ERROR);
        }

        RobotContext rc = RobotContext.get(robotId);
        rc.clearPathChain();
        rc.getPreAllocatedPaths().append(pathChain);
    }


    /**
     * {"bid":1,"sp":1,"gp":9,"pn":9,"pts":[[1,2,1],[2,3,1],[3,4,2],[4,5,2],[5,6,1],[6,7,1],[7,8,1],[8,9,1]]}
     *
     * @param bizId 业务id
     * @param pathChain 路径链
     * @return 格式化后的路径链
     */
    @NonNull
    private String __formatPathChain(@NonNull Long bizId, @NonNull PathChain<IPath> pathChain) {
        Map<String, Object> m = new LinkedHashMap<>();
        m.put("bid", bizId.intValue());
        m.put("sp", Integer.parseInt(pathChain.getHead().getStartPos().getXmapId()));
        m.put("gp", Integer.parseInt(pathChain.getTail().getEndPos().getXmapId()));
        m.put("pn", pathChain.size());

        List<int[]> pts = new LinkedList<>();
        pathChain.forEach(p -> {
            int s = Integer.parseInt(p.getStartPos().getXmapId());
            int e = Integer.parseInt(p.getEndPos().getXmapId());
            // 0不指定,1正走向终点,2倒走向终点
            int d = FORWARD.getCode().equals(p.getDirection()) ? 1 :
                    (BACKWARD.getCode().equals(p.getDirection()) ? 2 : 0);
            pts.add(new int[]{s, e, d});
        });

        m.put("pts", pts);
        return JSON.toJSONString(m);
    }

    // endregion



    // region 👇语音指令


    @Override
    public void playVoice(Long robotId, int voiceId) {

        ConfRobotVoice voice = confRobotVoiceService.getBySceneId(voiceId);
        if (voice == null) {
            throw new ServiceException("语音场景ID不存在：" + voiceId, HttpStatus.ERROR);
        }

        Map<String, Integer> m = ImmutableMap.of(
                "id", voiceId,
                "play_interval", voice.getPlayInterval(),
                "play_times", voice.getPlayTimes());
        String json = JSON.toJSONString(m);

        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.musicPlay(robotId, json));

        if (ic.getCode() != 0) {
            // 失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "播放语音失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("播放语音失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }

    @Override
    public void pauseVoice(Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.musicPause(robotId, 0L));

        if (ic.getCode() != 0) {
            // 失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "暂停语音失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("暂停语音失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }


    // endregion



    // region 👇充电指令

    /**
     * 打开或关闭充电口
     * @param robotId 机器人id
     * @param i 1打开 | 2关闭
     * @throws ServiceException 打开或关闭充电口失败
     */
    @Override
    public void openOrCloseChargingPort(long robotId, int i) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.openAndCloseChargingPort(robotId, String.valueOf(i)));

        if (ic.getCode() != 0) {
            // 失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "打开或关闭充电口失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("打开或关闭充电口失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }

    /**
     * 开始充电
     * @param pileId 充电桩id
     * @throws ServiceException 开始充电失败
     */
    @Override
    public void startPileCharging(Long pileId) {
        IndustrialControlResult ic = __retryDoing(() -> {
            int i = robotChargingPileService.writeChargingPileWork(pileId, 1);
            return IndustrialControlResult.builder()
                    .code(i == 1 ? 0 : 1)
                    .message(i == 1 ? "充电桩开始充电成功" : "充电桩开始充电失败")
                    .build();
        });

        if (ic.getCode() != 0) {
            log.error(ic.getMessage());
            throw new ServiceException(ic.getMessage(), HttpStatus.ERROR);
        }
    }

    /**
     * 停止充电
     * @param pileId 充电桩id
     * @throws ServiceException 停止充电失败
     */
    @Override
    public void stopPileCharging(Long pileId) {
        IndustrialControlResult ic = __retryDoing(() -> {
            int i = robotChargingPileService.writeChargingPileWork(pileId, 0);
            return IndustrialControlResult.builder()
                    .code(i == 1 ? 0 : 1)
                    .message(i == 1 ? "充电桩停止充电成功" : "充电桩停止充电失败")
                    .build();
        });

        if (ic.getCode() != 0) {
            log.error(ic.getMessage());
            throw new ServiceException(ic.getMessage(), HttpStatus.ERROR);
        }
    }

    // endregion



    // region 👇升级指令

    @Override
    public void firmwareUpdateConfirm(Long robotId) {
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.firmwareUpdateConfirm(robotId, ""));

        if (ic.getCode() != 0) {
            // 失败
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "升级机器人失败：" + ic.getMessage(), ic.getRobotId(), SecurityUtils.getUserIdOrSystem());
            throw new ServiceException("升级机器人失败：" + ic.getMessage(), HttpStatus.ERROR);
        }
    }


    // endregion



    // region 👇云台和伸缩杆工控指令

    /**
     * 复位伸缩杆<br/>
     * 需要工控：伸缩杆复位
     *
     * @param robotId 机器人id
     */
    public void resetTelescopic(@NonNull Long robotId) {
        // TODO: 参数未明确
        IndustrialControlResult ic = __retryDoing(() ->
                industrialControlService.telescopicPole(robotId, ""));

        if (ic.getCode() != 0) {
            log.error("伸缩杆复位失败：robotId={}，{}", robotId, ic.getMessage());
            String userId = SecurityUtils.getUserIdOrSystem();
            LogUtil.addRobotLog(LogRobotTypeEnum.CONTROL, "伸缩杆复位失败：" + ic.getMessage(), robotId, userId);
            throw new ServiceException("伸缩杆复位失败！");
        }
    }

    // endregion



    // region 👇私有辅助方法

    private final Lock lock = new ReentrantLock();
    private final Condition condition = lock.newCondition();

    /**
     * 重试执行工控指令，如果失败会根据配置参数自动重试
     * @param supplier 工控指令执行函数
     * @return 工控指令执行结果
     */
    @NonNull
    private IndustrialControlResult __retryDoing(@NonNull Supplier<IndustrialControlResult> supplier) {
        return __retryDoing(supplier, 0);
    }

    /**
     * 重试执行工控指令
     * @param supplier 工控指令执行函数
     * @param retryCount 当前已经重试次数
     * @return 工控指令执行结果
     */
    @NonNull
    private IndustrialControlResult __retryDoing(@NonNull Supplier<IndustrialControlResult> supplier, int retryCount) {
        IndustrialControlResult ic = supplier.get();
        if (ic.getCode() != 0 && retryCount < robotDispatchConfig.getMaxRetries() - 1) {
            if (robotDispatchConfig.getRetryWaitMillis() > 0) {
                lock.lock();
                try {
                    if (condition.await(robotDispatchConfig.getRetryWaitMillis(), TimeUnit.MILLISECONDS)) {
                        log.debug("重试等待被唤醒，可能系统状态已变化，当前重试次数: {}", retryCount);
                    }
                } catch (InterruptedException e) {
                    log.error("重试等待被中断，当前重试次数: {}", retryCount, e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                } finally {
                    lock.unlock();
                }
            }

            return __retryDoing(supplier, retryCount + 1);
        }
        return ic;
    }

    // endregion

}
