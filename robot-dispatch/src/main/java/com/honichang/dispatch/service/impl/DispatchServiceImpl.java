package com.honichang.dispatch.service.impl;

import com.google.common.collect.ImmutableMap;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.bo.ChargingPileFreeOrLessBo;
import com.honichang.dispactch.model.bo.ChargingPilePathPlan;
import com.honichang.dispactch.model.enums.DirectionEnum;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.dispactch.service.DispatchCommandService;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispatch.helper.CalcRobotPathHelper;
import com.honichang.dispatch.planner.PathPlanner;
import com.honichang.exception.NoEmptyParkException;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.RobotChargingReservationService;
import com.honichang.point.service.TaskEscortService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调度核心服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DispatchServiceImpl implements DispatchService {

    @Resource
    private DispatchCommandService dispatchCommandService;

    @Resource
    private RobotChargingReservationService robotChargingReservationService;

    @Resource
    private TaskEscortService taskEscortService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private DispatchService self;

    @Resource
    private BizPointService bizPointService;

    // region 👇切换工作模式

    private void __switchWorkModeSpecification(RobotContext rc, RobotWorkModeEnum workMode) {

        RobotStatusEnum s = rc.getStatus();
        if (!rc.isOnline() || rc.isCharging() || rc.isUpgrading()
                || s == RobotStatusEnum.MANUAL_FAULT || s == RobotStatusEnum.HARDWARE_ALARM
                || s == RobotStatusEnum.CAN_NOT_NAVIGATION) {

            throw new ServiceException(StringUtils.format("机器人当前状态为【{}】，不能执行任务！",
                    rc.getStatus().getDescription()), HttpStatus.ERROR);
        }

        // 如果当前机器人处于陪同任务下，则不允许切换到遥控模式
        if (workMode == RobotWorkModeEnum.REMOTE_CONTROL_MODE
                && (rc.isEscorting() || taskEscortService.checkRobotHasEscortInHours(rc.getRobotId(), 8))) {
            throw new ServiceException("机器人处于陪同任务中，或8小时内安排有陪同任务，不允许切换到遥控模式！", HttpStatus.ERROR);
        }
    }

    @Override
    public boolean switchWorkMode(long robotId, RobotWorkModeEnum workMode, boolean autoTimeout) {
        // 实现工作模式切换逻辑
        log.info("切换机器人[{}]工作模式到[{}]，是否启用超时[{}]", robotId, workMode, autoTimeout);

        RobotContext rc = getRobotContext(robotId);
        // 如果已经处于这个模式下则直接返回（遥控模式要重置LastRemoteControlTime）
        if (rc.getWorkMode() == workMode) {
            if (workMode == RobotWorkModeEnum.REMOTE_CONTROL_MODE && rc.getLastRemoteControlTime() != null) {
                rc.setLastRemoteControlTime(DateUtils.getNowDate());
            }
            return true;
        }

        // 事件处理器：TaskStatusEventHandler
        // *控制机器人【暂停导航】
        // *将机器人当前taskInstance的状态改为【暂停】，并保存DB
        // *写机器人日志【任务：暂停当前任务】
        // *工控回调 DispatchCallbackService#pauseNavigation
        //   *成功：写机器人日志【控制：暂停导航】
        //   *失败：写机器人日志【控制：暂停导航失败】
        // *变更机器人上下文
        //   *切换到【遥控模式】
        //   *【暂停状态】
        //   *【清空预分配路径】
        if (workMode == RobotWorkModeEnum.REMOTE_CONTROL_MODE) { // 遥控模式
            // 只有遥控模式需要验证当前机器人状态
            __switchWorkModeSpecification(rc, workMode);

            // 如果当前有普通任务在执行，则需要【取消当前任务】
            if (Chain.isNotEmpty(rc.getPreAllocatedPaths())) {
                // 先取消导航
                String msg = abortNavigation(robotId);
                if (!msg.isEmpty()) {
                    throw new ServiceException("取消导航失败，无法切换遥控模式：" + msg);
                }
            }
        }

        // 发送事件处理属性与其他逻辑
        eventPublisher.publishEvent(new DispatchEvent(
                DispatchEventType.ROBOT_WORK_MODE_UPDATE_EVENT,
                "DispatchService.switchWorkMode",
                ImmutableMap.of("robotId", robotId, "workMode", workMode, "autoTimeout", autoTimeout),
                false));
        return true;
    }

    // endregion

    @Override
    public boolean resetRobot(long robotId, String xmapId) {
        dispatchCommandService.resetRobot(robotId, xmapId);
        return true;
    }

    @Override
    public String forwardStepPoint(long robotId) {
        // TODO: 实现前进到最近点位逻辑
        log.info("机器人[{}]前进到最近点位", robotId);
        return null;
    }

    @Override
    public String backwardStepPoint(long robotId) {
        // TODO: 实现后退到最近点位逻辑
        log.info("机器人[{}]后退到最近点位", robotId);
        return null;
    }

    @Override
    public boolean runStepPoint(long robotId, String pathId, String startPointId, String endPointId, int direction) {
        // TODO: 实现走一步逻辑
        log.info("机器人[{}]走一步：路径[{}] 从[{}]到[{}] 方向[{}]", robotId, pathId, startPointId, endPointId, direction);
        return false;
    }

    @Override
    public boolean runStepPercent(long robotId, String pathId, String startPointId, String endPointId, double percent, int direction) {
        // TODO: 实现走百分比路径逻辑
        log.info("机器人[{}]走百分比路径：路径[{}] 从[{}]到[{}] 百分比[{}] 方向[{}]",
                robotId, pathId, startPointId, endPointId, percent, direction);
        return false;
    }

    @Override
    public boolean runToPoint(long robotId, String pointId) {
        // TODO: 实现运行到指定点位逻辑
        log.info("机器人[{}]运行到点位[{}]", robotId, pointId);
        return false;
    }

    @Override
    public Optional<PathChain<IPath>> planPath(
            long robotId,
            @NonNull Position startPoint,
            @NonNull Position endPoint,
            boolean alwaysForward,
            String... tempObstacleXmapPathId) throws NoReachablePathException {

        return PathPlanner.calcPathChainOptimal(robotId, startPoint, endPoint, alwaysForward, tempObstacleXmapPathId);
    }

    @Override
    public Long findNearestAvailableCharger(long robotId) {
        // TODO: 实现查找最近可用充电桩逻辑
        log.info("查找机器人[{}]最近的可用充电桩", robotId);
        return null;
    }

    @Override
    public Long findLeastChargerQueue(long robotId) {
        // TODO: 实现查找排队最少充电桩逻辑
        log.info("查找机器人[{}]排队最少的充电桩", robotId);
        return null;
    }

    @Override
    public Long findNearestAvailableParking(long robotId, String pointId) {
        // TODO: 实现查找最近可用停车位逻辑
        log.info("查找机器人[{}]在点位[{}]附近最近的可用停车位", robotId, pointId);
        return null;
    }


    // region 👇查找一些类型的点位

    @Nullable
    @Override
    public BizPoint findNearestBizPoint(Long robotId) {
        // 查找机器人所在位置最近的BizPoint
        IPosition nearestPoint = CalcRobotPathHelper.getNearestBizPoint(robotId);
        if (nearestPoint == null || nearestPoint.getBizPointId() == null) return null;
        return bizPointService.getById(nearestPoint.getBizPointId());
    }

    // endregion


    @Override
    public boolean predictFutureOccupancy(long robotId, int steps) {
        // TODO: 实现预测未来路径冲突逻辑
        log.info("预测机器人[{}]未来[{}]步的路径冲突", robotId, steps);
        return false;
    }

    @Override
    public boolean resolveConflict(long robotId) {
        // TODO: 实现解决路径冲突逻辑
        log.info("解决机器人[{}]的路径冲突", robotId);
        return false;
    }

    @Override
    public boolean planTaskPath(long robotId, long taskDefId, TaskChain taskChain, List<Long> unreachableNodes, PathChain<Path> paths) {
        // TODO: 实现任务路径规划逻辑
        log.info("为机器人[{}]规划任务[{}]的路径", robotId, taskDefId);
        return false;
    }

    @Override
    @NonNull
    public RobotContext getRobotContext(long robotId) {
        return RobotContext.get(robotId);
    }

    @Override
    public void updateRobotContext(RobotContext robotContext) {
        if (robotContext != null) {
            RobotContext.put(robotContext);
            log.debug("更新机器人[{}]上下文", robotContext.getRobotId());
        }
    }

    @Override
    public Collection<RobotContext> getAllRobotContexts() {
        return RobotContext.values();
    }

    // region 👇驱动导航

    /**
     * 导航任务指令
     *
     * @param directiveChain 任务指令链
     */
    @Override
    public void navigateTaskDistribution(long robotId, TaskDirectiveChain directiveChain) {
        dispatchCommandService.navigateTaskDistribution(robotId, directiveChain);
    }

    /**
     * 导航路径
     *
     * @param robotId   机器人id
     * @param bizId     业务id
     * @param pathChain 路径链
     */
    @Override
    public void navigate(long robotId, @NonNull Long bizId, @NonNull PathChain<IPath> pathChain) {
        dispatchCommandService.navigate(robotId, bizId, pathChain);
    }

    /**
     * 取消当前导航
     */
    @Override
    public String abortNavigation(long robotId) {
        try {
            return dispatchCommandService.abortNavigation(robotId);
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    /**
     * 驱动机器人前往排队最少且最近的充电桩
     *
     * @throws ServiceException 充电桩不存在、充电桩无空闲停车位、找不到规划路径
     */
    @NonNull
    @Override
    public ChargingPilePathPlan navigateToCharge(long robotId) {
        RobotContext rc = RobotContext.get(robotId);
        ChargingPileFreeOrLessBo bo = robotChargingReservationService.getLeastChargerQueue();
        ChargingPilePathPlan pathPlan;
        try {
            pathPlan = PathPlanner.planPathToPileOrNearParking(rc.getRobotId(), bo);
        } catch (NoReachablePathException e) {
            throw new ServiceException("无法执行陪同任务：找不到充电桩路径！", HttpStatus.ERROR);
        } catch (NoEmptyParkException e) {
            throw new ServiceException("无法执行陪同任务：没有空闲的充电桩，也没有空闲停车位供等待！", HttpStatus.ERROR);
        }

        // 调度去目的地
        if (Chain.isNotEmpty(pathPlan.getPathChain())) {
            self.navigate(robotId, pathPlan.getChargingPile().getId(), pathPlan.getPathChain());
        }

        // 预约上
        robotChargingReservationService.add(pathPlan.getChargingPile().getId(), robotId);

        return pathPlan;
    }

    /**
     * 驱动机器人前往目标点附件的停车位
     *
     * @return true成功，false失败
     * @throws ServiceException 无空闲停车位、找不到规划路径; 目标点位在地图中不存在
     */
    @Override
    public boolean navigateToParking(long robotId, @NonNull String xmapPointId) {
        RobotContext rc = RobotContext.get(robotId);
        Position target = MapContext.getXmapPoint(xmapPointId, rc.getMapId());
        if (target == null) {
            throw new ServiceException("目标点位在地图中不存在：" + xmapPointId, HttpStatus.ERROR);
        }

        try {
            Optional<PathChain<IPath>> pathChain = PathPlanner.planPathToParking(rc.getRobotId(), rc.getPosition(), target);
            if (pathChain.isPresent() && Chain.isNotEmpty(pathChain.get())) {
                self.navigate(robotId, 0L, pathChain.get());
                return true;
            }
        } catch (NoEmptyParkException e) {
            throw new ServiceException("无法执行陪同任务：没有空闲停车位！", HttpStatus.ERROR);
        } catch (NoReachablePathException e) {
            throw new ServiceException("无法执行陪同任务：找不到停车位路径！", HttpStatus.ERROR);
        }

        return false;
    }

    /**
     * 导航到最近的十字路口（无方向，到了就行）
     */
    @Override
    public boolean navigateToNearestCrossPoint(long robotId) {
        RobotContext rc = RobotContext.get(robotId);
        List<IPosition> crossPoints = CalcRobotPathHelper.getNearestCrossPoint(robotId);
        if (CollectionUtils.isEmpty(crossPoints)) {
            throw new ServiceException("没有找到最近的十字路口", HttpStatus.ERROR);
        }

        try {
            Optional<PathChain<IPath>> pathChain = PathPlanner
                    .calcPathChainOptimal(rc.getRobotId(), rc.getPosition(), crossPoints.get(0), false);
            if (pathChain.isPresent() && Chain.isNotEmpty(pathChain.get())) {
                // 无方向
                pathChain.get().forEach(path -> {
                    ((Path) path).setDirection(DirectionEnum.NONE.getCode());
                });
                self.navigate(robotId, 0L, pathChain.get());
                return true;
            } else {
                return false;
            }
        } catch (NoReachablePathException e) {
            log.error("无法导航到最近的十字路口：{}", e.getMessage());
            throw new ServiceException("无法导航到最近的十字路口：" + e.getMessage(), HttpStatus.ERROR);
        }
    }

    // endregion
}
