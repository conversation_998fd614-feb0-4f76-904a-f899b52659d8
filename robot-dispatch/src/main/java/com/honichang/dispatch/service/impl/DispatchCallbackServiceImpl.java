package com.honichang.dispatch.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.NumberUtil;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.*;
import com.honichang.dispactch.service.DispatchCallbackService;
import com.honichang.dispactch.service.DispatchCommandService;
import com.honichang.dispactch.service.RobotDrivenService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispactch.util.LogUtil;
import com.honichang.dispactch.util.ResultToString;
import com.honichang.netty.domain.IndustrialControlResult;
import com.honichang.netty.dto.MrcStatusResponse;
import com.honichang.netty.enums.RobotExecCodeEnum;
import com.honichang.point.domain.RobotChargingPile;
import com.honichang.point.domain.TaskEscort;
import com.honichang.point.service.RobotChargingPileService;
import com.honichang.point.service.TaskEscortService;
import com.honichang.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

import static com.honichang.dispactch.model.enums.PointClassNameEnum.PARKING;
import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.ESCORT_RETURN;
import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.STANDBY;
import static com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum.ESCORT_TEMP_ABORT;
import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.*;

/**
 * 工控回调服务实现类
 *
 * <p>主要功能指令分类：</p>
 *
 * <h3>导航指令</h3>
 * <ul>
 *   <li>暂停导航：pauseNavigation</li>
 *   <li>取消导航：cancelNavigation</li>
 *   <li>导航任务下发：taskDistribution</li>
 *   <li>恢复导航：resumeNavigation</li>
 * </ul>
 *
 * <h3>云台指令</h3>
 * <ul>
 *   <li>云台复位：resetCloudPlatform</li>
 *   <li>云台旋转：rotateCloudPlatform</li>
 *   <li>云台俯仰：pitchCloudPlatform</li>
 * </ul>
 *
 * <h3>伸缩杆指令</h3>
 * <ul>
 *   <li>伸缩杆复位：resetTelescopic</li>
 *   <li>伸缩杆升降：liftTelescopic</li>
 * </ul>
 *
 * <h3>摄像头指令</h3>
 * <ul>
 *   <li>摄像头聚焦：focusCamera</li>
 *   <li>摄像头变倍：zoomCamera</li>
 *   <li>摄像头自动聚焦：resetCamera</li>
 * </ul>
 *
 * <h3>机器人状态</h3>
 * <ul>
 *   <li>获取机器人状态：getRobotStatus</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DispatchCallbackServiceImpl implements DispatchCallbackService {

    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    @Lazy
    private DispatchCommandService dispatchCommandService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private TaskService taskService;
    @Resource
    private RobotChargingPileService robotChargingPileService;
    @Resource
    private RobotDrivenService robotDrivenService;
    @Resource
    private TaskEscortService taskEscortService;


    // region 👇被调


    // region 👇机器人在线状态

    /**
     * 更改机器人在线与离线状态
     * 由殿勇的netty自动调用
     */
    @Override
    public void setRobotConnectionStatus(@NonNull Long robotId, boolean connected) {
        RobotContext rc = RobotContext.get(robotId);
        if (connected) {
            if (!rc.isOnline()) {
                eventPublisher.publishEvent(new DispatchEvent(
                        DispatchEventType.ROBOT_ONLINE_STATUS_UPDATE_EVENT,
                        "DispatchCallbackService.setRobotConnectionStatus",
                        ImmutableMap.of("robotId", robotId,
                                "status", true,
                                "reason", "机器人在线"),
                        true));
            }
        } else {
            if (rc.isOnline()) {
                eventPublisher.publishEvent(new DispatchEvent(
                        DispatchEventType.ROBOT_ONLINE_STATUS_UPDATE_EVENT,
                        "DispatchCallbackService.setRobotConnectionStatus",
                        ImmutableMap.of("robotId", robotId,
                                "status", false,
                                "reason", "机器人离线"),
                        true));
            }
        }
    }

    // endregion


    // region 👇获取机器人状态

    @Override
    public void getRobotStatus(@NonNull IndustrialControlResult result) {
        Long robotId = result.getRobotId();
        RobotContext rc = RobotContext.get(robotId);

        if (result.getCode() == 0) {
            // 将result.getParams()转换JSON到MrcStatusResponse对象
            MrcStatusResponse data = JSON.parseObject(result.getParams(), MrcStatusResponse.class);

            // 可能需要变更机器人状态
            if (data.getResult() == RobotExecCodeEnum.SUCCESS.getCode()) {

                RobotWorkModeEnum workMode = null;
                boolean newChargeFlag;


                // region 👇更新机器人位置信息、累计里程数、累计运行时间
                rc.setTotalOdom(data.getChassis().getOdom());
                rc.setTotalRunning((long) (data.getChassis().getTime() * 60));

                Position p = new Position(data.getLocalization().getX(), data.getLocalization().getY(), data.getLocalization().getTheta());
                if (data.getLocalization().getPoint_id() == 0) {
                    // 不在点位上
                    rc.setPosition(p);
                } else {
                    // 在点位上
                    p = MapContext.getXmapPoint(String.valueOf(data.getLocalization().getPoint_id()), rc.getMapId());
                    rc.setPosition(p);
                }

                if (data.getLocalization().getPath_id() == 0) {
                    rc.setXmapPathId(null);
                } else {
                    rc.setXmapPathId(StringUtils.EMPTY + data.getLocalization().getPath_id());
                }
                // endregion


                // region 👇工作模式是否更新
                if ("MANU".equals(data.getMode()) && rc.getWorkMode() != RobotWorkModeEnum.MANUAL_MODE) {
                    workMode = RobotWorkModeEnum.MANUAL_MODE;
                } else if ("AUTO".equals(data.getMode()) && rc.getWorkMode() == RobotWorkModeEnum.MANUAL_MODE) {
                    workMode = RobotWorkModeEnum.AUTO_MODE;
                }
                if (workMode != null) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.ROBOT_WORK_MODE_UPDATE_EVENT,
                            "DispatchCallbackService.getRobotStatus",
                            ImmutableMap.of("robotId", robotId, "workMode", workMode),
                            false));
                }
                // endregion


                // region 👇充电状态处理
                newChargeFlag = data.getBattery().isCharging() && data.getBattery().getPower() < 1.0;
                boolean oldChargeFlag = rc.isCharging();

                rc.setCharging(newChargeFlag);
                rc.setBattery(data.getBattery().getPower());

                // 开始充电：原不在充电，现充电中
                if (!oldChargeFlag && newChargeFlag) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.START_CHARGING_EVENT,
                            "DispatchCallbackService.getRobotStatus",
                            ImmutableMap.of("robotId", robotId),
                            false));
                }
                // 结束充电：原在充电，现不在充电
                if (oldChargeFlag && !newChargeFlag) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.FINISH_CHARGING_EVENT,
                            "DispatchCallbackService.getRobotStatus",
                            ImmutableMap.of("robotId", robotId),
                            false));
                }
                // endregion


                // 👇更新已走完的路径
                __refreshCompletedPaths(rc, data);


                // 充电与升级不参与下面的变更状态
                if (!newChargeFlag && !rc.isUpgrading()) {

                    // region 【重要】遭遇阻碍

                    // 手动模式不判断障碍
                    if (workMode != RobotWorkModeEnum.MANUAL_MODE) {

                        if (!rc.isBlocked() && data.getChassis().isBlocked()) {
                            // 遭遇阻碍
                            eventPublisher.publishEvent(new DispatchEvent(
                                    DispatchEventType.BLOCK_EVENT,
                                    "DispatchCallbackService.getRobotStatus",
                                    ImmutableMap.of("robotId", robotId),
                                    true));
                        } else if (rc.isBlocked() && !data.getChassis().isBlocked()) {
                            // 障碍解除
                            eventPublisher.publishEvent(new DispatchEvent(
                                    DispatchEventType.UNBLOCK_EVENT,
                                    "DispatchCallbackService.getRobotStatus",
                                    ImmutableMap.of("robotId", robotId),
                                    true));
                        }
                    }

                    // endregion


                    String reason = StringUtils.EMPTY;

                    // region 👇确定是否是无法导航 (return)
                    // 👇检查地图状态: 判断地图是否成功加载
                    boolean canNotNavigation = !"OK".equals(data.getMap().getStatus());
                    if (canNotNavigation) reason = "地图未加载成功";

                    // 👇检查定位状态
                    // 定位失败 | 定位中 | 置信度不足75%
                    if (!canNotNavigation && data.getLocalization().getStatus().equals("FAILED")) {
                        canNotNavigation = true;
                        reason = "定位失败";
                    }
                    if (!canNotNavigation && data.getLocalization().getStatus().equals("RUNNING")) {
                        canNotNavigation = true;
                        reason = "定位中";
                    }
                    if (!canNotNavigation && data.getLocalization().getReliability() < 0.8) {
                        canNotNavigation = true;
                        reason = "置信度不足80%";
                    }

                    // 👇检查导航状态【导航失败】
                    // 导航失败不是机器人未准备好，只是本次的导航指令有问题
//                    if (!canNotNavigation && "FAILED".equals(data.getNavigation().getStatus())) {
//                        canNotNavigation = true;
//                        reason = "导航失败";
//                    }

                    // 改变状态为【无法导航】
                    if (canNotNavigation) {
                        if (rc.getStatus() != RobotStatusEnum.CAN_NOT_NAVIGATION) {
                            eventPublisher.publishEvent(new DispatchEvent(
                                    DispatchEventType.ROBOT_STATUS_UPDATE_EVENT,
                                    "DispatchCallbackService.getRobotStatus",
                                    ImmutableMap.of("robotId", robotId,
                                            "status", RobotStatusEnum.CAN_NOT_NAVIGATION,
                                            "reason", reason),
                                    true));
                        }
                        return;
                    }
                    // endregion

                    // 👇成功：
                    // PAUSED【导航暂停】
                    // WORKING【导航中】
                    // STANDBY【导航退出】【导航完成】【无导航点任务】
                    RobotStatusEnum newState = RobotStatusEnum.STANDBY;
                    switch (data.getNavigation().getStatus()) {
                        // 导航暂停
                        case "PAUSED":
                            newState = RobotStatusEnum.PAUSED;
                            reason = "导航暂停";
                            break;
                        // 导航中
                        case "RUNNING":
                            newState = RobotStatusEnum.WORKING;
                            reason = "导航中";
                            break;
                        // 导航退出
                        case "ABORTED":
                            reason = "导航退出";
                            break;
                        // 导航完成
                        case "FINISH":
                            reason = "导航完成";
                            break;
                        // 无导航点任务
                        case "IDLE":
                            reason = "无导航点任务";
                            break;
                        default:
                            throw new ServiceException("不支持的导航状态：" + data.getNavigation().getStatus());
                    }

                    if (rc.getStatus() != newState) {
                        eventPublisher.publishEvent(new DispatchEvent(
                                DispatchEventType.ROBOT_STATUS_UPDATE_EVENT,
                                "DispatchCallbackService.getRobotStatus",
                                ImmutableMap.of("robotId", robotId,
                                        "status", newState,
                                        "reason", reason),
                                true));
                    }
                }
                // endregion
            }
            return;
        }

        // 其他都是错误：1~5
        // 获取机器人状态失败：[2]请求授权码（AppCode）错误
        String err = StringUtils.format("获取机器人状态失败：[{}]{}", result.getCode(), result.getMessage());
        log.error("获取机器人状态失败：robotId={}，{}", robotId, err);
        LogUtil.addRobotLog(LogRobotTypeEnum.FAULT, err, robotId, "system");
    }

    /**
     * 异步：刷新已完成的路径
     *
     * @param rc   机器人上下文
     * @param data 返回的机器人实时状态数据
     */
    private void __refreshCompletedPaths(@NonNull RobotContext rc, @NonNull MrcStatusResponse data) {
        // 已经没有预分配路径了，说明抵达目的地，不用再往下了
        if (rc.getPreAllocatedPaths().isEmpty()) {
            return;
        }

        // 未完成导航的路径点
        int[] uncompletedPathId = data.getNavigation().getUncompleted_path();
        // 已完成导航的路径点
        int[] completedPath = data.getNavigation().getCompleted_path();
        if (completedPath == null || completedPath.length == 0) {
            return;
        }

        // 这里传递过来的应该是偏移量的地址，所以应该按C语音的逻辑处理，0表示路径点无效
        int compLen = completedPath.length;
        for (int i = 0; i < completedPath.length; i++) {
            if (completedPath[i] == 0) {
                compLen = i;
                break;
            }
        }

        if (compLen <= 1) {
            return; // 至少需要2个点才能形成一条路径
        }

        if (uncompletedPathId == null)
            uncompletedPathId = new int[] {};

        int unCompLen = uncompletedPathId.length;
        for (int i = 0; i < uncompletedPathId.length; i++) {
            if (uncompletedPathId[i] == 0) {
                unCompLen = i;
                break;
            }
        }


        // 完成的路径点数据是[1,2,3]，即完成的路径数量是completedPath.length - 1
        int completedPathCount = compLen - 1;
        if (rc.getCompletedPaths().size() >= completedPathCount) {
            return;
        }

        // 将预分配路径中已完成的路径移动到已完成路径中
        // 已经完成的路径点
        for (int i = rc.getCompletedPaths().size() + 1; i <= completedPathCount; i++) {
            if (rc.getPreAllocatedPaths().isEmpty()) {
                log.warn("机器人[{}]预分配路径已空但仍有未处理的完成路径！", rc.getRobotId());
                break;
            }

            int prePid = completedPath[i - 1];
            int currPid = completedPath[i];

            IPath preHead = rc.getPreAllocatedPaths().getHead();
            if (Objects.equals(preHead.getStartPos().getXmapId(), String.valueOf(prePid))
                    && Objects.equals(preHead.getEndPos().getXmapId(), String.valueOf(currPid))) {

                rc.getPreAllocatedPaths().popHead(); // 移出最近的未执行路径
                rc.getCompletedPaths().add(preHead); // 加入已完成路径
            } else {
                log.error("机器人[{}]预分配路径与实际不符，预分配路径：{}，实际路径：{}",
                        rc.getRobotId(), preHead.getEndPos().getXmapId(), currPid);
                log.error("预期是：{}->{}；实际是：{}->{}",
                        prePid, currPid,
                        preHead.getStartPos().getXmapId(), preHead.getEndPos().getXmapId());
                break;
            }
        }

        // 最后的验证，不出意外应该匹配的，不匹配记录日志
        if (rc.getPreAllocatedPaths().size() != unCompLen) {
            log.error("机器人[{}]预分配路径数量与实际不符，预分配路径数量：{}，实际数量：{}",
                    rc.getRobotId(), rc.getPreAllocatedPaths().size(), unCompLen);
            return;
        }

        final AtomicInteger i = new AtomicInteger(0);
        final int[] finalUncompletedPathId = uncompletedPathId;
        rc.getPreAllocatedPaths().forEach(p -> {
            int pid = finalUncompletedPathId[i.get()];
            if (!Objects.equals(p.getEndPos().getXmapId(), String.valueOf(pid))) {
                log.warn("机器人[{}]预分配路径与实际不符，预分配路径：{}，实际路径：{}",
                        rc.getRobotId(), p.getEndPos().getXmapId(), pid);
                log.warn("预分配路径：{}", ResultToString.toString(rc.getPreAllocatedPaths()));
                log.warn("已完成路径：{}", ResultToString.toString(rc.getCompletedPaths()));
                log.warn("未完成路径：{}", Arrays.toString(finalUncompletedPathId));
            }
            i.incrementAndGet();
        });

        // popHead()之后就没有了
        // 与本函数第一行不同
        // 写在这里，确保只触发一次！
        if (rc.getPreAllocatedPaths().isEmpty()) {
            // 【重要】触发各类任务节点事件
            __checkArriveSchedule(rc);
        }
    }

    /**
     * 预分配路径已经走完
     * @param rc 机器人上下文
     */
    private void __checkArriveSchedule(RobotContext rc) {
        // 可以监听机器人流动路线的状态：
        // 在线、自动模式、非导航失败与故障
        if (rc.isListeningStatus()) {
            TaskEscortContext tec = TaskEscortContext.getByRobot(rc.getRobotId());
            if (tec != null) {
                __checkArriveEscort(rc, tec);
            }

            // *判断是否抵达充电桩，如果抵达则开始充电
            __checkArrivePile(rc);

            // *判断是否抵达停车位，如果抵达则开始升级
            __checkArriveUpgrade(rc);

        }
    }

    /**
     * 判断是否抵达停车位，如果是则开始升级
     */
    private void __checkArriveUpgrade(RobotContext rc) {
        // 工作状态为升级，但是还没升级上
        if (rc.getWorkStatus() == RobotWorkStatusEnum.UPGRADING
                && !rc.isUpgrading()
                && rc.getPreAllocatedPaths().isEmpty()
                && rc.getCompletedPaths().isNotEmpty()
                && PARKING.getCode().equals(rc.getCompletedPaths().getTail().getEndPos().getClassName())) {

            try {
                eventPublisher.publishEvent(new DispatchEvent(
                        DispatchEventType.START_UPGRADE_EVENT,
                        "CoreScheduler.checkArriveSchedule",
                        ImmutableMap.of("robotId", rc.getRobotId()),
                        true));
            } catch (Exception e) {
                log.error("到达升级点位后，下达升级指令异常：{}", e.getMessage());
            }
        }
    }

    /**
     * 判断是否抵达充电桩，如果是则充电
     */
    private void __checkArrivePile(RobotContext rc) {
        // 去充电，但还没充上
        if (rc.isWorkForCharging() && !rc.isCharging() && rc.getBattery() < 1) {
            // 判断是否到充电位置，如果到了就充电
            Optional<RobotChargingPile> pile = Optional.empty();
            try {
                pile = robotChargingPileService.arriveChargingPile(rc.getRobotId());
            } catch (Exception e) {
                log.error("判断是否到达充电点位异常：{}", e.getMessage());
            }
            try {
                pile.ifPresent(p -> robotDrivenService.startCharging(rc.getRobotId(), p.getId()));
            } catch (Exception e) {
                log.error("到达充电点位后，下达充电指令异常：{}", e.getMessage());
            }
        }
    }

    /**
     * 检查是否抵达陪同任务中的各种阶段
     * @param rc 机器人上下文
     * @param tec 陪同任务上下文
     */
    private void __checkArriveEscort(RobotContext rc, TaskEscortContext tec) {
        if (!taskService.canWorkForEscort(rc).equals(StringUtils.EMPTY)) {
            return;
        }

        TaskEscort te = tec.getTaskEscort();
        TaskEscortStatusEnum status = TaskEscortStatusEnum.fromCode(te.getStatus());
        switch (status) {
            case GOTO_VISITOR: // 接访客
                // 101->102
                if (tec.getWaitPosition().equalCo(rc.getPosition())) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.ARRIVE_WAIT_POINT_EVENT,
                            "CoreScheduler.checkArriveSchedule",
                            ImmutableMap.of("robotId", rc.getRobotId(),
                                    "status", GOTO_VISITOR),
                            true)); // 101->102
                }
                break;
            case WAIT_VISITOR_ARRIVE: // 等待访客
                // 由回调触发UWB签到  102->201
                // 或由CoreScheduler超时触发 102->109
                break;
            case ESCORTING: // 陪同中
                // 签到后进入陪同中状态
                // 判断是否抵达目的地由回调机器人预分配路径为空触发 201->301
                // 或由CoreScheduler超时触发 201->209
                if (tec.getArrivePosition().equalCo(rc.getPosition())) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.ARRIVE_TARGET_POINT_EVENT,
                            "CoreScheduler.checkArriveSchedule",
                            ImmutableMap.of("robotId", rc.getRobotId()),
                            true));
                } // 201->301
                break;
            case ARRIVED_DESTINATION: // 到达目的地
                // 由工控按钮回调触发下一个动作 301->401
                // 或由CoreScheduler超时触发 301->309
                break;
            case PAUSED_ALARM: // 暂停报警
                // 手动暂停报警（TaskEscortController） 301->302 | 302->301
                break;
            case GOTO_EXIT: // 送访客
                // 401->402
                if (tec.getWaitPosition().equalCo(rc.getPosition())) {
                    eventPublisher.publishEvent(new DispatchEvent(
                            DispatchEventType.ARRIVE_WAIT_POINT_EVENT,
                            "CoreScheduler.checkArriveSchedule",
                            ImmutableMap.of("robotId", rc.getRobotId(),
                                    "status", GOTO_EXIT),
                            true)); // 401->402
                }
                break;
            case WAIT_VISITOR_LEAVE: // 等待访客离开
                // 由工控按钮回调触发下一个动作 402->501
                // 由回调触发UWB距离超时 402->501
                break;
            case COMPLETED: // 已完成
            case TERMINATED_009: // 终止
            case TERMINATED_109:
            case TERMINATED_209:
            case TERMINATED_309:
            case TERMINATED_409:
            default: // 终止或完成
                // 判断预分配路径是否走完，且机器人当前在十字路口
                // 才算安全
                if (ESCORT_RETURN == rc.getWorkStatus() && rc.getPosition().isCrossroads()) {
                    rc.setWorkStatus(STANDBY);
                    if (rc.getStatus() == RobotStatusEnum.WORKING) {
                        rc.setStatus(RobotStatusEnum.STANDBY);
                    }
                    rc.clearPathChain();
                }
                break;
        }
    }

    // endregion


    // region 👇UWB卡签到

    /**
     * UWB卡签到
     *
     * @param robotId 机器人id
     * @param cardId  UWB卡id
     */
    @Override
    public void uwbCardIn(@NonNull Long robotId, @NonNull String cardId) {
        TaskEscortContext tec = TaskEscortContext.getByRobot(robotId);
        if (tec == null) {
            return;
        }

        boolean isCorrectCard = Objects.equals(cardId, tec.getTaskEscort().getUwbCardId());
        if (isCorrectCard) {
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.ARRIVE_VISITOR_EVENT,
                    "DispatchCallbackService.uwbCardIn",
                    ImmutableMap.of("robotId", robotId, "taskEscortId", tec.getTaskEscort().getId()),
                    true)); // 102->201
        }

        // 通知工控端，签到成功或失败
        try {
            dispatchCommandService.uwbCheckIn(robotId, isCorrectCard);
        } catch (Exception e) {
            log.error("通知工控端签到结果失败", e);
        }
    }

    // endregion


    // region 👇UWB卡距离推送

    /**
     * UWB距离推送
     * 陪同中：201->209
     * 暂停报警：302->309
     * 送访客离开：401->409
     * 到出口等待离开：402->501
     *
     * @param robotId  机器人id
     * @param distance 距离米
     */
    @Override
    public void uwbDistance(@NonNull Long robotId, double distance) {
        TaskEscortContext tec = TaskEscortContext.getByRobot(robotId);
        if (tec == null) {
            return;
        }

        RobotContext rc = RobotContext.get(robotId);
        // 当前状态
        TaskEscort te = tec.getTaskEscort();
        String status = te.getStatus();

        // 是否是暂停报警中
        if (PAUSED_ALARM.getCode().equals(status)) {
            // 判断暂停报警是否需要自动关闭
            if (te.getRecoveryTime() != null && te.getRecoveryTime().before(DateUtils.getNowDate())) {
                taskEscortService.restoreAlarm(te);
            }
            // 暂停报警，直接跳过
            return;
        }

        String confDistance = sysConfigService.selectConfigByKey("uwbOutOfRangeDistance");
        String confMinutes = sysConfigService.selectConfigByKey("uwbOutOfRangeMinutes");
        double outOfRangeDistance = NumberUtil.defaultDouble(confDistance, 5);
        int outOfRangeMinutes = NumberUtil.defaultInt(confMinutes, 60);

        if (distance > outOfRangeDistance) {
            if (tec.getOutOfRangeTime() == null) {
                tec.setOutOfRangeTime(DateUtils.getNowDate());

                // 送访客的时候不报警了，而是就是单纯的判断是否离开
                // 因为访客可能不会触摸【离开】按钮
                if (!WAIT_VISITOR_LEAVE.getCode().equals(status)) {
                    // 暂停导航+发语音
                    try {
                        dispatchCommandService.pauseNavigation(robotId);
                    } catch (Exception e) {
                        log.error("暂停导航失败", e);
                    }
                    // 播放语音：4你离我过远
                    try {
                        dispatchCommandService.playVoice(robotId, 4);
                    } catch (Exception e) {
                        log.error("播放语音失败", e);
                    }
                }
            }
        } else {
            if (tec.getOutOfRangeTime() != null) {
                // 超出距离，但现在已经回来了，清空超出时间
                tec.setOutOfRangeTime(null);
                // 暂停语音
                try {
                    dispatchCommandService.pauseVoice(robotId);
                } catch (Exception e) {
                    log.error("暂停语音失败", e);
                }
                // 继续导航
                if (rc.getPreAllocatedPaths().isNotEmpty()) {
                    try {
                        dispatchCommandService.resumeNavigation(robotId);
                    } catch (Exception e) {
                        log.error("继续导航失败", e);
                    }
                    // 继续播放语音：7请跟随
                    try {
                        dispatchCommandService.playVoice(robotId, 7);
                    } catch (Exception e) {
                        log.error("暂停语音失败", e);
                    }
                }
            }
        }

        if (tec.getOutOfRangeTime() != null) {
            long outOfRangeDuration = System.currentTimeMillis() - tec.getOutOfRangeTime().getTime();
            if (outOfRangeDuration > outOfRangeMinutes * 60 * 1000L) {
                if (WAIT_VISITOR_LEAVE.getCode().equals(status)) {
                    // 402->501
                    // 访客离开超时判定，任务结束
                    taskEscortService.completeLeave(te);
                } else {
                    // 201->209 | 301->309
                    String reason = "访客中途离开，且超过指定时间";
                    taskEscortService.abortBySchedule(robotId, reason, ESCORT_TEMP_ABORT);
                }
            }
        }
    }

    // endregion


    // region 👇升级固件反馈

    @Override
    public void firmwareUpdateStatus(@NonNull Long robotId, boolean success, String reason) {
        if (success) {
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.COMPLETE_UPGRADE_EVENT,
                    "DispatchCallbackService.firmwareUpdateStatus",
                    ImmutableMap.of("robotId", robotId),
                    true));
        } else {
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.FAULT_UPGRADE_EVENT,
                    "DispatchCallbackService.firmwareUpdateStatus",
                    ImmutableMap.of("robotId", robotId, "reason", reason),
                    true));
        }
    }

    // endregion


    // region 👇触摸屏按钮

    /**
     * 访客触摸按钮【完成任务】
     * 送访客 301->401
     */
    @Override
    public void touchButtonComplete(@NonNull Long robotId) {
        TaskEscortContext tec = TaskEscortContext.getByRobot(robotId);
        if (tec == null) {
            return;
        }

        taskEscortService.visitorComplete(tec.getTaskEscort());
    }

    /**
     * 访客触摸按钮【离开】
     * 陪同任务结束 402->501
     */
    @Override
    public void touchButtonLeave(@NonNull Long robotId) {
        TaskEscortContext tec = TaskEscortContext.getByRobot(robotId);
        if (tec == null) {
            return;
        }

        taskEscortService.completeLeave(tec.getTaskEscort());
    }

    // endregion


    // endregion


}
