package com.honichang.dispatch.service.impl;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.TaskChain;
import com.honichang.dispactch.model.TaskNode;
import com.honichang.dispatch.helper.AlgorithmHelper;
import com.honichang.dispatch.service.TaskService;
import com.honichang.exception.NoReachablePathException;
import com.honichang.exception.PositioningFailedException;
import com.honichang.exception.RobotNotOnAnyPathException;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.service.TaskDefNodeService;
import com.honichang.point.service.TaskDefService;
import com.honichang.point.service.TaskInstanceNodeService;
import com.honichang.point.service.TaskInstanceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class TaskServiceImpl implements TaskService {

    @Resource
    private TaskDefService taskDefService;

    @Resource
    private TaskDefNodeService taskDefNodeService;

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;

    @Override
    public boolean initTask() {
        return false;
    }

    @Override
    public int restoreTask(long robotId) {
        return 0;
    }

    @Override
    public boolean pauseCurrentTask(long robotId, String pauseReason) {
        return false;
    }

    @Override
    public boolean resumeCurrentTask(long robotId) {
        return false;
    }

    @Override
    public boolean cancelCurrentTask(long robotId, String cancelReason) {
        return false;
    }

    @Override
    public TaskChain getCurrentTaskChain(long robotId) {
        return null;
    }

    @Override
    public boolean updateTaskChain(long robotId, TaskChain taskChain) {
        return false;
    }

    @Override
    public boolean addTaskToQueue(long robotId, long taskDefId, int priority) {
        return false;
    }

    /**
     * 给任务排列节点，并规划路径，规划好后将其写入DB和RobotContext中<br/>
     * 要求RobotContext状态必须是0待机|1工作|4暂停
     *
     * @param robotId          机器人id
     * @param taskDefId        任务定义ID
     * @return 成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean planTaskPath(long robotId, long taskDefId) {
        // 1. 获取机器人当前坐标、当前所在路径
        RobotContext robotContext = RobotContext.get(robotId);
        if (!robotContext.getStatus().equals("0") && !robotContext.getStatus().equals("1") && !robotContext.getStatus().equals("4")) {
            throw new ServiceException("机器人状态异常，不能执行任务！");
        }

        // 2. 获取任务定义的所有任务节点定义
        List<TaskDefNode> taskDefNodes = taskDefNodeService.getByTaskId(taskDefId);
        if (taskDefNodes.isEmpty()) {
            throw new ServiceException("任务节点不存在！");
        }

        TaskInstance taskInstance = taskInstanceService.createTaskByDef(taskDefId, taskDefNodes, robotContext.getMapId());
        List<TaskInstanceNode> taskInstanceNodes = taskInstanceNodeService.createTaskNodesByDef(taskDefId, taskInstance, taskDefNodes);

        // 3. 计算当前机器人距离每一个任务节点的距离，如果到A节点经过了B节点说明B是更理想下一个节点，最终循环完将最理想的推入TaskChain
        List<Long> unreachableNodes = new ArrayList<>();
        PathChain<Path> paths = new PathChain<>();
        TaskChain<TaskNode> taskChain = new TaskChain<>();
        taskChain.setTaskInstance(taskInstance);

        try {
            AlgorithmHelper.planTaskPath(robotId, taskInstance, taskInstanceNodes, taskChain, unreachableNodes, paths);
        } catch (RobotNotOnAnyPathException e) {
            throw new RuntimeException(e.getMessage() + "发送走入最近路径指令，等待10秒后重试！");
        } catch (NoReachablePathException | PositioningFailedException e) {
            throw new RuntimeException(e.getMessage());
        }

        // 4. 直到所有节点都排完为止，不可达节点做出特殊标记

        return false;
    }

    @Override
    public boolean removeTaskFromQueue(long robotId, long taskId) {
        return false;
    }

    @Override
    public boolean canExecuteTask(long robotId, long taskDefId) {
        return false;
    }

    @Override
    public String getTaskStatus(long robotId, long taskId) {
        return "";
    }

    @Override
    public boolean updateTaskStatus(long robotId, long taskId, String status, String result) {
        return false;
    }
}
