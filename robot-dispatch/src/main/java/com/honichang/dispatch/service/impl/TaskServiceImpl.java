package com.honichang.dispatch.service.impl;

import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.config.RobotDispatchConfig;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispatch.loader.ContextLoader;
import com.honichang.dispatch.planner.RobotTaskPlanner;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.*;
import com.honichang.point.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.WAIT_VISITOR_ARRIVE;
import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.WAIT_VISITOR_LEAVE;
import static com.honichang.dispactch.model.enums.TaskStatusEnum.EXECUTING;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    // region 👇IoC

    @Resource
    private RobotDispatchConfig robotDispatchConfig;

    @Resource
    private TaskDefService taskDefService;

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;

    @Resource
    private TaskEscortService taskEscortService;

    @Resource
    private DispatchService dispatchService;

    @Resource
    private MapPointService mapPointService;

    @Resource
    private TaskService self;


    // endregion


    // region 👇所有状态判定

    @NonNull
    @Override
    public String canWorkForEscort(RobotContext rc) {
        // 离线与升级
        if (!rc.isOnline())
            return "机器人离线，不能执行陪同任务！";
        if (rc.isUpgrading())
            return "机器人处于【升级】状态，不能执行陪同任务！";

        // 模式
        RobotWorkModeEnum wm = rc.getWorkMode();
        if (wm != RobotWorkModeEnum.AUTO_MODE)
            return StringUtils.format("机器人处于【{}】模式，不能执行陪同任务！", wm.getDescription());

        // 故障
        RobotStatusEnum s = rc.getStatus();
        if (s == RobotStatusEnum.MANUAL_FAULT || s == RobotStatusEnum.HARDWARE_ALARM)
            return "机器人处于【故障】状态，不能执行陪同任务！";

        if (s == RobotStatusEnum.CAN_NOT_NAVIGATION)
            return "机器人处于【无法导航】状态，可能是定位失败或置信度不足，不能执行陪同任务！";

        return StringUtils.EMPTY;
    }

    @NonNull
    @Override
    public String canWorkForTask(RobotContext rc) {
        // 离线与升级
        if (!rc.isOnline())
            return "机器人离线，不能执行任务！";
        if (rc.isUpgrading())
            return "机器人处于【升级】状态，不能执行任务！";

        if (rc.isCharging())
            return "机器人处于【充电】状态，不能执行任务！";

        // 模式
        RobotWorkModeEnum wm = rc.getWorkMode();
        if (wm != RobotWorkModeEnum.AUTO_MODE)
            return StringUtils.format("机器人处于【{}】模式，不能执行任务！", wm.getDescription());

        // 故障
        RobotStatusEnum s = rc.getStatus();
        if (s == RobotStatusEnum.MANUAL_FAULT || s == RobotStatusEnum.HARDWARE_ALARM)
            return "机器人处于【故障】状态，不能执行任务！";

        if (s == RobotStatusEnum.CAN_NOT_NAVIGATION)
            return "机器人处于【无法导航】状态，可能是定位失败或置信度不足，不能执行任务！";

        // 陪同中
        if (rc.isEscorting())
            return "机器人处于【陪同任务】中，不能执行循环任务！";

        return StringUtils.EMPTY;
    }


    // endregion


    // region 👇重新安排所有机器人的任务（含DB中的陪同）


    /**
     * 重新安排所有机器人的任务
     * 遍历所有的任务定义（非陪同和周期任务）
     * 优先主机器人排满，主机器人数量不足（即处于非工作状态，包括离线超时，短暂离线不算）
     * 则随机调度备用机器人（准工作状态且3小时内无陪同任务）
     */
    @Override
    public void distributeTasksForRobots() {

        if (ContextLoader.isReady()) {
            // 非服务启动的首次加载
            // 👇判断【陪同任务】是否满足开启的条件
            // 满足条件直接触发
            __triggerEscortTask();

            // 👇判断【周期任务】是否满足开启的条件
            // 只分配，并未真正触发，需要等核心调度器
            __distributePeriodTask();

            // 👇循环推入任务队列
            // 只分配，并未真正触发，需要等核心调度器
            __distributeCycleTask();

        } else {
            // 👇服务重启，先将运行中的【陪同任务】重新加载到上下文中
            __resumeEscortTask();

            // 循环所有机器人，初始化DB中当前的任务
            RobotContext.values().forEach(rc -> {
                if (rc.getTaskQueue().isEmpty()) {
                    // 👇再将DB中【未完成】的【任务】推入上下文任务链顶端
                    __resumeNormalTask(rc);
                }
            });
        }
    }

    // region 分配普通任务

    /**
     * 分配周期任务：
     * 按执行时间顺序推入任务队列<br/>
     * 如果主机器人在线数量不足，推入备用机器人
     */
    private void __distributePeriodTask() {
        List<TaskDef> taskDefs = taskDefService.selectAllPeriodTask();
        __distributeTasks(taskDefs, true);
    }

    /**
     * 分配循环任务：
     * 按任务优先级推入任务队列<br/>
     * 如果主机器人在线数量不足，推入备用机器人
     */
    private void __distributeCycleTask() {
        List<TaskDef> taskDefs = taskDefService.selectAllCycleTask();
        __distributeTasks(taskDefs, false);
    }

    private void __distributeTasks(@NonNull List<TaskDef> taskDefs, boolean isPeriod) {
        taskDefs.forEach(def -> {
            // 任务执行机器人
            // 2025-06-13: 佳哥说了，现在允许有多个主机器人一起跑一个任务
            List<TaskRobot> masters = def.getTaskRobotList().stream()
                    .filter(x -> x.getMasterFlag().equals("1"))
                    .collect(Collectors.toList());
            List<TaskRobot> slaves = def.getTaskRobotList().stream()
                    .filter(x -> x.getMasterFlag().equals("0"))
                    .collect(Collectors.toList());

            long needTimes = masters.size();
            if (isPeriod) {
                // 周期任务判定是否【到时间】
                String hm = DateUtils.parseDateToStr("HH:mm", DateUtils.getNowDate());
                if (hm.compareTo(def.getCycleTrigger()) < 0) {
                    return;
                }
                // 当天是否执行完了
                needTimes -= taskInstanceService.periodTaskPassedNum(def.getId());
                if (needTimes <= 0) {
                    return;
                }
            }


            // *判断主机器人是否都在线且在忙于非陪同任务
            AtomicInteger restTimes = new AtomicInteger((int) needTimes);
            masters.forEach(r -> {
                if (restTimes.get() > 0 && RobotContext.exists(r.getRobotId())) {
                    RobotContext rc = RobotContext.get(r.getRobotId());
                    // 是否在线，如果离线判断超时时间
                    // 离线超过N分钟，则不调度，由备用机器人顶替
                    if (!rc.isOnline() && rc.getOfflineTime() != null
                            && System.currentTimeMillis() - rc.getOfflineTime().getTime() > (long) robotDispatchConfig.getMinutesToSwitchMasterOffline() * 60 * 1000) {
                        return;
                    }
                    // 周期任务检查是否已经做完了这个机器人
                    if (isPeriod && taskInstanceService.periodTaskPassedToday(def.getId(), r.getRobotId())) {
                        return;
                    }
                    // 非陪同中的准工作或工作状态
                    if (rc.getWorkMode() == RobotWorkModeEnum.AUTO_MODE && rc.isStandByOrWorkingStatus() && !rc.isEscorting()) {
                        __distributeTasksForRobot(rc, def, isPeriod);
                        restTimes.getAndDecrement();
                    }
                }
            });


            // *如果主机器人数量不足，则顺序调度备用机器人
            // 检查备用机器人状态是否正常 && 3小时内无陪同任务
            slaves.forEach(r -> {
                if (restTimes.get() > 0 && RobotContext.exists(r.getRobotId())) {
                    RobotContext rc = RobotContext.get(r.getRobotId());
                    // 周期任务检查是否已经做完了这个机器人
                    if (isPeriod && taskInstanceService.periodTaskPassedToday(def.getId(), r.getRobotId())) {
                        return;
                    }
                    // 非陪同中的准工作状态或工作状态
                    // 5小时内无陪同任务，不能紧3小时算
                    if (rc.isOnline() && rc.getWorkMode() == RobotWorkModeEnum.AUTO_MODE
                            && rc.isStandByOrWorkingStatus() && !rc.isEscorting()
                            && !taskEscortService.checkRobotHasEscortInHours(r.getRobotId(), 5)) {

                        __distributeTasksForRobot(rc, def, isPeriod);
                        restTimes.getAndDecrement();
                    }
                }
            });
        });
    }

    /**
     * 给机器人安排普通任务
     * 推入rc.taskQueue中
     */
    private void __distributeTasksForRobot(@NonNull RobotContext rc, @NonNull TaskDef taskDef, boolean isPeriod) {
        // 检查队列里是否存在
        AtomicBoolean exists = new AtomicBoolean(false);
        rc.getTaskQueue().forEach(t -> {
            if (t.getTaskInstance().getTaskId().equals(taskDef.getId())) {
                exists.set(true);
            }
        });
        if (exists.get()) {
            return;
        }

        try {
            TaskInstance instance = taskInstanceService.createTaskByDef(taskDef.getId(), rc.getMapId());
            List<TaskInstanceNode> nodes = taskInstanceNodeService.createTaskNodesByDef(taskDef.getId(), rc.getMapId());
            List<TaskNode> taskNodes = new ArrayList<>(nodes.size());
            nodes.forEach(n -> taskNodes.add(new TaskNode(n)));
            Task task = new Task();
            task.setTaskInstance(instance);
            task.setTaskNodes(taskNodes);

            if (isPeriod) {
                // 检查机器人队列里是否有其他周期计划，如果有则终止那个周期计划，去执行这个
                TaskDirectiveChain taskDirectiveChain = rc.getTaskDirectiveChain();
                if (taskDirectiveChain.isNotEmpty()) {
                    TaskInstance taskInstance = taskDirectiveChain.getTaskInstance();
                    if (!taskInstance.getTaskId().equals(taskDef.getId())) {
                        if (taskInstance.getPlanType() == 1) {
                            taskInstanceService.abortTask(rc.getRobotId(), "周期任务开始执行");
                        } else {
                            taskInstanceService.pauseTask(rc.getRobotId(), "周期任务开始执行");
                        }
                    }
                }
                rc.getTaskQueue().pushHead(task);
            } else {
                rc.getTaskQueue().add(task);
            }
        } catch (Exception e) {
            // 任务不存在，或者任务节点没有定义！
            log.error("给机器人[{}]安排循环任务[{}]异常", rc.getRobotName(), taskDef.getTaskName(), e);
        }
    }

    // endregion


    // region 判定是否满足条件，如果满足直接触发去接人或充电

    private void __triggerEscortTask() {
        // 当前尚未开始的陪同任务
        List<TaskEscort> pendingList = taskEscortService.getPendingEscortTasks();

        pendingList.forEach(te -> {
            RobotContext rc = RobotContext.get(te.getRobotId());

            // TODO: 先排除已经在running的机器人的陪同，正常不会出现这种情况
            if (rc.isEscorting() || !canWorkForEscort(rc).equals(StringUtils.EMPTY)) {
                // 检查机器人状态，不能执行陪同任务则跳过
                return;
            }

            // 未开始：
            // 1.最初的预约时间大于3小时，目前进入3小时内 -> 调度去充电
            // 2.预约时间进入3小时内，电量小于80% -> 调度去充电
            Date execTime = te.getExecTime();
            long initToExec = execTime.getTime() - te.getCreateTime().getTime();
            long timeToExec = execTime.getTime() - System.currentTimeMillis();

            boolean toCharge = initToExec > 3 * 60 * 60 * 1000 && timeToExec <= 3 * 60 * 60 * 1000;
            if (!toCharge && timeToExec <= 3 * 60 * 60 * 1000 && rc.getBattery() < 0.8) {
                toCharge = true;
            }
            boolean toVisit = timeToExec <= 0;

            if (toVisit || toCharge) {
                try {
                    taskEscortService.startToVisit(rc.getRobotId(), te, !toVisit);
                } catch (Exception e) {
                    // ignore
                    // return;
                }
            }
        });
    }

    // endregion


    // region 服务重启后恢复进行中的陪同任务和普通任务

    private void __resumeEscortTask() {
        // 当天进行中的陪同任务
        List<TaskEscort> runningList = taskEscortService.getRunningEscortTasks();

        runningList.forEach(te -> {
            try {
                RobotContext rc = RobotContext.get(te.getRobotId());

                // 检查机器人状态，不能执行陪同任务则跳过
                if (!TaskEscortContext.exists(te.getId())) {
                    // 已经在（RUNNING)，但不在 EscortContext 中，说明服务重启了，因为服务器运行中，RC和TC的状态一定是连续的
                    Position waitPos = mapPointService.getXmapPosition(te.getWaitPointId(), rc.getMapId());
                    Position arrivePos = mapPointService.getXmapPosition(te.getArrivePointId(), rc.getMapId());
                    TaskEscortContext tec = TaskEscortContext.put(te, waitPos, arrivePos);

                    // 重新计算等待超时时间
                    if (WAIT_VISITOR_ARRIVE.getCode().equals(te.getStatus())
                            || WAIT_VISITOR_LEAVE.getCode().equals(te.getStatus())) {
                        tec.setWaitArriveTime(DateUtils.getNowDate());
                    }

                    // 写RC状态
                    if (rc.getStatus() == RobotStatusEnum.STANDBY) {
                        rc.setStatus(RobotStatusEnum.WORKING);
                    }
                }
            } catch (Exception e) {
                log.error("推送进行中的陪同任务[{}]到上下文异常", te.getId(), e);
            }
        });
    }


    /**
     * 服务器重启后才会用到
     */
    private void __resumeNormalTask(@NonNull RobotContext rc) {
        // ①按计划类型排序，周期任务在前；
        // ②按优先级排序，优先级高的在前；
        List<TaskInstance> runningTasks = taskInstanceService.getUnfinishedTasks(rc.getRobotId());

        runningTasks.forEach(taskInstance -> {
            Task task = new Task(taskInstance);
            // 任务节点
            List<TaskInstanceNode> taskInstanceNodes = taskInstanceNodeService.getListByTaskInstance(taskInstance);
            if (taskInstanceNodes.isEmpty()) {
                return;
            }

            List<TaskNode> taskNodes = new ArrayList<>();
            taskInstanceNodes.forEach(n -> taskNodes.add(new TaskNode(n)));
            task.setTaskNodes(taskNodes);

            // 正在进行的任务推入最顶
            rc.getTaskQueue().add(task);
        });
    }

    // endregion


    // endregion


    // region 👇恢复任务执行或开始新任务

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean runTaskInQueue(RobotContext rc) {
        SimpleChain<Task> taskQueue = rc.getTaskQueue();
        if (Chain.isEmpty(taskQueue)) {
            return false;
        }

        TaskDirectiveChain directiveChain = rc.getTaskDirectiveChain();
        boolean needResume = directiveChain.getTaskInstance() == null || directiveChain.isEmpty();

        if (needResume) {
            Task task;
            while ((task = taskQueue.getHead()) != null) {
                TaskInstance taskInstance = task.getTaskInstance();

                // 执行状态说明是【服务重启】恢复
                // 先取消导航，然后重新开始
                if (taskInstance.getId() != null && EXECUTING.getCode().equals(taskInstance.getStatus())) {
                    dispatchService.abortNavigation(rc.getRobotId());
                }

                // 【暂停】状态：被陪同、充电、遥控暂停
                // 如果id==null需要新建；
                // 需要重新安排未执行完成的任务节点到指令链中，已完成的不再处理它了
                if (taskInstanceService.startOrResume(task, rc.getRobotId(), "恢复任务")) {
                    // 导航
                    dispatchService.navigateTaskDistribution(rc.getRobotId(), directiveChain);
                    return true;
                }
                taskQueue.popHead(); // 已完成就踢掉
                taskInstanceService.finishedTask(rc.getRobotId(), taskInstance.getId());
            }
        }
        return false;
    }

    // endregion


    // region 👇规划任务节点以及路径

    /**
     * 规划任务节点以及路径
     * 不可达的排除在结果之外！
     *
     * @param rc        机器人
     * @param taskNodes 任务节点列表（副作用：将会刷新节点的不可达状态）
     * @return 任务指令链
     */
    @NonNull
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskDirectiveChain planTaskNodeOrders(RobotContext rc, @NonNull List<TaskInstanceNode> taskNodes) throws NoReachablePathException {
        // 重新安排点位的执行顺序
        // 可能会重置不可达状态
        TaskChain<TaskNode> nodeChain = RobotTaskPlanner.planTasksOrder(rc.getRobotId(), rc.getPosition(), taskNodes);
        // 实时保存重新计算完的点位不可达状态
        taskInstanceNodeService.mergeAndReflush(taskNodes);
        // 所有可达的点位，排好具体的路径
        // 不可达的不在期内
        return RobotTaskPlanner.planPathsByTaskChain(rc.getRobotId(), rc.getPosition(), nodeChain);
    }

    // endregion


}
