package com.honichang.dispatch.service;

import com.honichang.dispactch.model.TaskChain;

/**
 * 普通任务服务接口
 * 
 * <AUTHOR>
 */
public interface TaskService {
    
    /**
     * 初始化所有机器人的任务队列
     * 说明：初始化所有机器人的任务队列
     * 场景：服务初启动时调用一次
     * 从task_def/task_instance中读取
     * - task_def周期任务已到期的推入顶层
     * - task_instance未完成的任务推入
     * - task_def按优先级推入
     * 
     * @return 成功返回true，失败返回false
     */
    boolean initTask();
    
    /**
     * 恢复指定机器人的任务队列
     * 说明：恢复指定机器人的任务队列
     * 场景：①进入遥控模式再次恢复回自动模式时；②陪同任务完成或终止时；③新布置机器人时
     * 返回：有周期任务返回1，有普通任务返回2，没有任务返回0
     * 事务：@Transactional(propagation = Propagation.REQUIRES_NEW)
     * 
     * @param robotId 机器人ID
     * @return 有周期任务返回1，有普通任务返回2，没有任务返回0
     */
    int restoreTask(long robotId);
    
    /**
     * 暂停当前任务
     * 说明：暂停当前任务
     * 场景：toChargingEvent事件触发、remoteControlEvent事件触发、toEscortEvent事件触发
     * 
     * @param robotId 机器人ID
     * @param pauseReason 暂停原因
     * @return 成功返回true，失败返回false
     */
    boolean pauseCurrentTask(long robotId, String pauseReason);
    
    /**
     * 继续当前任务
     * 说明：继续当前任务
     * 
     * @param robotId 机器人ID
     * @return 成功返回true，失败返回false
     */
    boolean resumeCurrentTask(long robotId);
    
    /**
     * 取消当前任务
     * 说明：取消当前任务
     * 
     * @param robotId 机器人ID
     * @param cancelReason 取消原因
     * @return 成功返回true，失败返回false
     */
    boolean cancelCurrentTask(long robotId, String cancelReason);
    
    /**
     * 获取机器人当前任务队列
     * 
     * @param robotId 机器人ID
     * @return 任务队列
     */
    TaskChain getCurrentTaskChain(long robotId);
    
    /**
     * 更新机器人任务队列
     * 
     * @param robotId 机器人ID
     * @param taskChain 任务队列
     * @return 成功返回true，失败返回false
     */
    boolean updateTaskChain(long robotId, TaskChain taskChain);
    
    /**
     * 添加任务到队列
     * 
     * @param robotId 机器人ID
     * @param taskDefId 任务定义ID
     * @param priority 优先级
     * @return 成功返回true，失败返回false
     */
    boolean addTaskToQueue(long robotId, long taskDefId, int priority);
    
    /**
     * 从队列中移除任务
     * 
     * @param robotId 机器人ID
     * @param taskId 任务ID
     * @return 成功返回true，失败返回false
     */
    boolean removeTaskFromQueue(long robotId, long taskId);
    
    /**
     * 检查任务是否可以执行
     * 
     * @param robotId 机器人ID
     * @param taskDefId 任务定义ID
     * @return 可以执行返回true，否则返回false
     */
    boolean canExecuteTask(long robotId, long taskDefId);
    
    /**
     * 获取任务执行状态
     * 
     * @param robotId 机器人ID
     * @param taskId 任务ID
     * @return 任务状态
     */
    String getTaskStatus(long robotId, long taskId);
    
    /**
     * 更新任务执行状态
     * 
     * @param robotId 机器人ID
     * @param taskId 任务ID
     * @param status 新状态
     * @param result 执行结果
     * @return 成功返回true，失败返回false
     */
    boolean updateTaskStatus(long robotId, long taskId, String status, String result);
}
