package com.honichang.dispatch.service;

/**
 * 充电任务服务接口
 * 
 * <AUTHOR>
 */
public interface ChargingService {
    
    /**
     * 开始充电
     * 说明：机器人前往充电桩充电
     * 场景：预约陪同触发或立即陪同、任务中电量低于阈值
     * 
     * @param robotId 机器人ID
     * @return 成功返回true，失败返回false
     */
    boolean toCharge(long robotId);
    
    /**
     * 开始充电到指定充电桩
     * 
     * @param robotId 机器人ID
     * @param chargingPileId 充电桩ID
     * @return 成功返回true，失败返回false
     */
    boolean toChargeAtPile(long robotId, long chargingPileId);
    
    /**
     * 完成充电
     * 说明：充电完成，机器人离开充电桩
     * 场景：充电桩检测到电量充满
     * 
     * @param robotId 机器人ID
     * @return 成功返回true，失败返回false
     */
    boolean finishCharging(long robotId);
    
    /**
     * 取消充电
     * 说明：取消充电任务
     * 
     * @param robotId 机器人ID
     * @param reason 取消原因
     * @return 成功返回true，失败返回false
     */
    boolean cancelCharging(long robotId, String reason);
    
    /**
     * 检查机器人是否需要充电
     * 
     * @param robotId 机器人ID
     * @return 需要充电返回true，否则返回false
     */
    boolean needsCharging(long robotId);
    
    /**
     * 检查机器人是否正在充电
     * 
     * @param robotId 机器人ID
     * @return 正在充电返回true，否则返回false
     */
    boolean isCharging(long robotId);
    
    /**
     * 获取机器人当前电量
     * 
     * @param robotId 机器人ID
     * @return 电量百分比（0-100）
     */
    double getBatteryLevel(long robotId);
    
    /**
     * 获取充电桩状态
     * 
     * @param chargingPileId 充电桩ID
     * @return 充电桩状态
     */
    String getChargingPileStatus(long chargingPileId);
    
    /**
     * 预约充电桩
     * 
     * @param robotId 机器人ID
     * @param chargingPileId 充电桩ID
     * @return 成功返回true，失败返回false
     */
    boolean reserveChargingPile(long robotId, long chargingPileId);
    
    /**
     * 释放充电桩预约
     * 
     * @param robotId 机器人ID
     * @param chargingPileId 充电桩ID
     * @return 成功返回true，失败返回false
     */
    boolean releaseChargingPileReservation(long robotId, long chargingPileId);
    
    /**
     * 获取充电历史记录
     * 
     * @param robotId 机器人ID
     * @param limit 记录数量限制
     * @return 充电历史记录列表
     */
    java.util.List<Object> getChargingHistory(long robotId, int limit);
    
    /**
     * 估算充电时间
     * 
     * @param robotId 机器人ID
     * @param targetBatteryLevel 目标电量百分比
     * @return 预计充电时间（分钟）
     */
    int estimateChargingTime(long robotId, double targetBatteryLevel);
}
