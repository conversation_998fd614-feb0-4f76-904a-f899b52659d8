package com.honichang.dispatch.service;

import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.TaskChain;
import com.honichang.dispatch.context.RobotContext;

import java.util.Collection;
import java.util.List;

/**
 * 调度核心服务接口
 * 
 * <AUTHOR>
 */
public interface DispatchService {
    
    // ========== 查找点位相关方法 ==========
    
    /**
     * 查找当前位置的最近点位
     * 应用场景：AI报警，报告最近柱位
     *
     * @param x 当前坐标（米）
     * @param y 当前坐标（米）
     * @param xmapPathId 当前坐标所在xmap路径id
     * @param mapId 地图id
     * @return 成功返回最近xmap点位，失败返回null
     */
    String getNearestPoint(double x, double y, String xmapPathId, long mapId);
    
    /**
     * 查找机器人当前位置的最近点位
     * 应用场景：AI报警，报告最近柱位
     *
     * @param robotId 机器人id
     * @return 成功返回最近xmap点位，失败返回null
     */
    String getNearestPoint(long robotId);
    
    /**
     * 查找机器人背后的最近点位
     * 应用场景：遭遇障碍后反方向就近停靠
     *
     * @param robotId 机器人id
     * @return 成功返回最近xmap点位，失败返回null
     */
    String getNearestPointBackward(long robotId);
    
    /**
     * 查找已走过或预分配路线中，离点位最近的十字路口
     * @param robotId 机器人id
     * @param xmapPointId 目标地图点位id
     * @return 成功返回十字路口xmap点位，失败返回null
     */
    String getNearestCrossPoint(long robotId, String xmapPointId);
    
    /**
     * 查找最近的十字路口
     * @param robotId 机器人id
     * @return 成功返回十字路口xmap点位，失败返回null
     */
    String getNearestCrossPoint(long robotId);
    
    // ========== 组合控制（遥控模式）相关方法 ==========
    
    /**
     * 想要进行组合控制，必须先切换机器人工作模式到102=遥控模式
     * 需要特殊权限
     * 1.智能机器人页面锁头打开进入遥控模式，无操作下倒计时N分钟自动切回自动模式
     * 2.点位配置进入遥控模式，不会自动切换回来
     *
     * @param robotId 机器人id
     * @param workMode 工作模式：101自动模式 | 102遥控模式(后端遥控)
     * @param timeout 无操作自动切回自动模式的超时时间，单位秒，0无限
     * @return 成功返回true，失败返回false
     */
    boolean switchWorkMode(long robotId, int workMode, int timeout);
    
    /**
     * 让当前机器人复位到初始状态
     * 注意：这个点位是xmap点位，不是biz点位
     * @param robotId 机器人id
     * @param x 坐标x
     * @param y 坐标y
     * @param theta 朝向角度rad
     * @return 成功返回true，失败返回false
     */
    boolean resetRobot(long robotId, double x, double y, double theta);
    
    /**
     * 让当前机器人走入前方最近点位，如果当前在十字路口，则根据theta确定哪里是前方，没有前方则放弃
     * 注意：这个点位是xmap点位，不是biz点位
     * @param robotId 机器人id
     * @return 成功返回前进点位，失败返回null
     */
    String forwardStepPoint(long robotId);
    
    /**
     * 让当前机器人退到后方最近点位，如果当前在十字路口，则根据theta确定哪里是后方，没有后方则放弃
     * 注意：这个点位是xmap点位，不是biz点位
     * @param robotId 机器人id
     * @return 成功返回后退点位，失败返回null
     */
    String backwardStepPoint(long robotId);
    
    // ========== 调度（非手动模式）相关方法 ==========
    
    /**
     * 走一步
     * 应用场景：遥控模式单一路径指令；自动模式所有调度都要用到这个函数
     *
     * @param robotId 机器人id
     * @param pathId 要走的路径id
     * @param startPointId 起始点位id
     * @param endPointId 走向终止点位id
     * @param direction 1正走 | 2倒走
     * @return 成功true，失败返回false
     */
    boolean runStepPoint(long robotId, String pathId, String startPointId, String endPointId, int direction);
    
    /**
     * 让当前机器人走临近路径的百分比长度
     * 期间遇障碍不汇报，障碍超过1分钟原地暂停
     * 函数体内部分多步+实时监控完成
     *
     * @param robotId 机器人id
     * @param pathId 要走的路径id
     * @param startPointId 起始点位id
     * @param endPointId 走向终止点位id
     * @param percent 百分比，范围0.1-1.0，step=0.1
     * @param direction 1正走 | 2倒走
     * @return 成功返回true，失败返回false
     */
    boolean runStepPercent(long robotId, String pathId, String startPointId, String endPointId, double percent, int direction);
    
    /**
     * 让当前机器人运行到指定点位
     * 必须处于遥控模式下
     *
     * @param robotId 机器人id
     * @param pointId runTo的点位id
     * @return 成功返回true，失败返回false，如果失败见机器人日志
     */
    boolean runToPoint(long robotId, String pointId);
    
    // ========== 路径规划相关方法 ==========
    
    /**
     * 从当前点位到目标点位规划路径，中间不允许跳路径，必须连续
     * ignoreObstacle=false, 要求排除当前地图其他人发现的障碍点
     *
     * @param robotId 机器人id
     * @param startPointId 起始点位id
     * @param endPointId 目标点位id
     * @param ignoreObstacle 是否忽略障碍
     * @return 成功返回路径，失败返回null
     */
    PathChain planPath(long robotId, String startPointId, String endPointId, boolean ignoreObstacle);
    
    /**
     * 找最近的无人预约的充电桩
     *
     * @param robotId 机器人id
     * @return 成功返回充电桩id，失败没找到返回null
     */
    Long findNearestAvailableCharger(long robotId);
    
    /**
     * 找充电排队最少的充电桩，同样排队数量的找最近的
     *
     * @param robotId 机器人id
     * @return 成功返回充电桩id，失败没找到返回null
     */
    Long findLeastChargerQueue(long robotId);
    
    /**
     * 找某点附件最近的空闲停车位
     *
     * @param robotId 机器人id
     * @param pointId 目标点位id
     * @return 成功返回停车位id，失败没找到返回null
     */
    Long findNearestAvailableParking(long robotId, String pointId);
    
    // ========== 交通管制相关方法 ==========
    
    /**
     * 预测未来N步是否与其他机器人预分配路径冲突
     *
     * @param robotId 机器人id
     * @param steps 预测步数
     * @return 成功返回true，失败返回false
     */
    boolean predictFutureOccupancy(long robotId, int steps);
    
    /**
     * 处理机器人冲突，控制优先级低的机器人退到走过的最近十字路口再让出十字路口
     *
     * @param robotId 机器人id
     * @return 成功返回true，失败返回false
     */
    boolean resolveConflict(long robotId);
    
    // ========== 任务管理相关方法 ==========
    
    /**
     * 任务主备切换
     *
     * @param robotId 机器人id
     * @return 成功返回true，失败返回false
     */
    boolean switchTaskMaster(long robotId);
    
    /**
     * 给任务排列节点，并规划路径
     *
     * @param robotId 机器人id
     * @param taskDefId 任务定义ID
     * @param taskChain 引用参数返回规划后的任务队列
     * @param unreachableNodes 引用参数返回不可达节点
     * @param paths 引用参数返回规划后的路径
     * @return 成功返回true，失败返回false
     */
    boolean planTaskPath(long robotId, long taskDefId, TaskChain taskChain, List<Long> unreachableNodes, PathChain<Path> paths);
    
    // ========== 机器人上下文管理 ==========
    
    /**
     * 获取机器人上下文
     */
    RobotContext getRobotContext(long robotId);
    
    /**
     * 更新机器人上下文
     */
    void updateRobotContext(RobotContext robotContext);
    
    /**
     * 获取所有机器人上下文
     */
    Collection<RobotContext> getAllRobotContexts();
}
