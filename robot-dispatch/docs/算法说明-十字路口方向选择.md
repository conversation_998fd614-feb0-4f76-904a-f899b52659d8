# 十字路口方向选择算法说明

## 问题背景

在机器人导航系统中，当机器人到达十字路口时，需要决定以什么方向（正走或倒走）进入目标路径。这个决策非常重要，因为：

1. **小巷内不允许调头**：一旦进入小巷，机器人无法调头，必须在十字路口就选对方向
2. **效率考虑**：选择正确的方向可以避免不必要的路径调整
3. **安全考虑**：错误的方向选择可能导致机器人卡在死胡同

## 算法核心思想

`calcDirection` 算法通过综合考虑以下两个因素来决定机器人的行进方向：

1. **机器人当前朝向角** (40%权重)：机器人当前面向的方向
2. **期望朝向角** (60%权重)：从十字路口到目标点的直线方向

算法选择与这两个角度综合匹配度最高的路径方向。

## 算法步骤

### 1. 输入参数验证
```java
public static int calcDirection(double angle, IPosition crossPointId, IPosition targetPointId, List<IPath> pathsCrossToTarget)
```

- `angle`: 机器人当前朝向角（弧度）
- `crossPointId`: 十字路口点位坐标
- `targetPointId`: 目标点位坐标  
- `pathsCrossToTarget`: 从十字路口到目标点的路径列表

### 2. 计算期望朝向角
```java
double expectedAngle = calcAngleBetweenPoints(crossX, crossY, targetX, targetY);
```

使用 `atan2(y2-y1, x2-x1)` 计算从十字路口到目标点的直线角度。

### 3. 获取路径方向角度
```java
double pathForwardAngle = getPathForwardAngle(firstPath, crossPointId);
double pathBackwardAngle = normalizeAngle(pathForwardAngle + Math.PI);
```

- 正向角度：路径定义的正向方向
- 反向角度：正向角度 + π（180度）

### 4. 计算角度差值
```java
double forwardAngleDiff = Math.abs(normalizeAngleDiff(angle - pathForwardAngle));
double backwardAngleDiff = Math.abs(normalizeAngleDiff(angle - pathBackwardAngle));
double expectedForwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathForwardAngle));
double expectedBackwardDiff = Math.abs(normalizeAngleDiff(expectedAngle - pathBackwardAngle));
```

计算当前朝向和期望朝向与路径正向、反向的角度差。

### 5. 综合评分
```java
double forwardScore = forwardAngleDiff * 0.4 + expectedForwardDiff * 0.6;
double backwardScore = backwardAngleDiff * 0.4 + expectedBackwardDiff * 0.6;
```

使用加权平均计算正向和反向的综合评分。

### 6. 选择最优方向
```java
return forwardScore <= backwardScore ? 1 : 2;
```

选择评分更低（角度差更小）的方向。

## 关键函数说明

### `normalizeAngle(double angle)`
将角度标准化到 [-π, π] 范围内，确保角度计算的一致性。

### `calcAngleBetweenPoints(double x1, double y1, double x2, double y2)`
使用 `atan2` 函数计算两点间的角度，返回值在 [-π, π] 范围内。

### `getPathForwardAngle(IPath path, IPosition fromPoint)`
根据路径的起点和终点，以及机器人所在的点位，确定路径的正向角度。

### `isSamePoint(IPosition point1, IPosition point2)`
使用精度阈值（1e-6）判断两个点是否相同，避免浮点数精度问题。

## 测试场景

### 场景1：朝向一致
- 机器人朝向：0° (向右)
- 目标方向：0° (向右)
- 路径方向：0° (向右)
- **结果**：正走 (1)

### 场景2：朝向相反
- 机器人朝向：180° (向左)
- 目标方向：0° (向右)
- 路径方向：0° (向右)
- **结果**：倒走 (2)

### 场景3：朝向垂直
- 机器人朝向：90° (向上)
- 目标方向：0° (向右)
- 路径方向：0° (向右)
- **结果**：正走 (1) - 期望朝向权重更高

### 场景4：复杂角度
- 机器人朝向：45° (右上)
- 目标方向：45° (右上)
- 路径方向：45° (右上)
- **结果**：正走 (1)

## 权重调整说明

当前权重设置：
- 当前朝向：40%
- 期望朝向：60%

**为什么期望朝向权重更高？**
1. 目标导向：最终目标是到达目标点，期望朝向更重要
2. 效率优先：选择更接近目标方向的路径可以减少后续调整
3. 实践验证：在实际应用中，这个权重分配效果更好

## 边界情况处理

1. **空路径列表**：返回默认值 1（正走）
2. **找不到匹配路径**：返回默认值 1（正走）
3. **角度差相等**：优先选择正走（forwardScore <= backwardScore）
4. **浮点数精度**：使用 1e-6 作为比较阈值

## 性能考虑

- 时间复杂度：O(n)，其中 n 是路径数量
- 空间复杂度：O(1)
- 所有计算都是基本的数学运算，性能开销很小

## 使用示例

```java
// 创建十字路口点位
IPosition crossPoint = new Position("CROSS_001", 0.0, 0.0, 0.0, "十字路口");

// 创建目标点位
IPosition targetPoint = new Position("TARGET_001", 10.0, 0.0, 0.0, "目标点");

// 机器人当前朝向（弧度）
double robotAngle = 0.0; // 向右

// 路径列表
List<IPath> paths = Arrays.asList(pathFromCrossToTarget);

// 计算方向
int direction = AlgorithmHelper.calcDirection(robotAngle, crossPoint, targetPoint, paths);

if (direction == 1) {
    System.out.println("机器人应该正走");
} else {
    System.out.println("机器人应该倒走");
}
```

## 扩展可能性

1. **动态权重**：根据路径复杂度动态调整权重
2. **多路径支持**：同时考虑多条可选路径
3. **障碍物考虑**：将障碍物信息纳入决策
4. **历史数据**：基于历史成功率调整算法参数
