package com.honichang.deploy.utiles;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.Session;
import ch.ethz.ssh2.StreamGobbler;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 远程服务器工具类
 */
@Slf4j
public class ServerUtil {

    /**
     * 登录ssh
     * @param ip
     * @param username
     * @param password
     * @return
     */
    public static Connection login(String ip, String username, String password) {
        boolean flag = false;
        Connection connection = null;
        try {
            connection = new Connection(ip);
            // 连接
            connection.connect();
            // 认证
            flag = connection.authenticateWithPassword(username, password);
            if (flag) {
                log.info("ssh " + ip + " 连接成功");
                return connection;
            }
        } catch (Exception e) {
            if (connection != null) {
                connection.close();
            }
            connection = null;
        }
        return connection;
    }

    /**
     * 执行shell脚本或者命令
     * @param cmd 即将执行的命令
     * @return 命令执行完后返回的结果值
     */
    public static String execCommand(Connection connection, String cmd) {
        String result = "";
        try {
            if (connection != null) {
                // 打开一个会话
                Session session = connection.openSession();
                // 执行命令
                session.execCommand(cmd);
                result = processStdout(session.getStdout());
                log.info("操作命令：" + cmd);
                session.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 解析脚本执行的结果
     * @param in 输入流对象
     * @return 文本
     */
    private static String processStdout(InputStream in) {
        InputStream stdout = new StreamGobbler(in);
        StringBuffer buffer = new StringBuffer();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(stdout, StandardCharsets.UTF_8));
            String line = null;
            while ((line = br.readLine()) != null) {
                buffer.append(line).append("\n");
            }
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer.toString();
    }
}
