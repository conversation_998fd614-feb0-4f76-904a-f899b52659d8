package com.honichang.deploy.utiles;

import com.honichang.deploy.domain.Deploy;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import org.apache.commons.net.ftp.FTPClient;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 传输文件工具类
 */
public class FileUploadUtil {

    /**
     * 向服务器传输文件
     *
     * @param filename 文件名
     * @param input    文件的输入流
     */
    public static void uploadFile(String filename, InputStream input, Deploy deploy) {
        FTPClient ftp = new FTPClient();
        Session sshSession = null;
        Channel channel = null;
        ChannelSftp sftp = null;
        // ip
        String host = deploy.getHost();
        // 用户名
        String userName = deploy.getUserName();
        // 密码
        String password = deploy.getPassword();
        // 文件储存路径
        String path = deploy.getPath();
        try {
            JSch jsch = new JSch();
            // 获取sshSession  账号-ip-端口
            sshSession = jsch.getSession(userName, host, 22);
            // 添加密码
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            // 严格主机密钥检查
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            // 开启sshSession链接
            sshSession.connect();
            // 获取sftp通道
            channel = sshSession.openChannel("sftp");
            // 开启
            channel.connect();
            sftp = (ChannelSftp) channel;
            //设置为被动模式
            ftp.enterLocalPassiveMode();
            // 进入到要上传的目录
            sftp.cd(path);
            // 上传文件
            sftp.put(input, filename);
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (sftp.isClosed()) {
                sftp.disconnect();
            }
            if (channel.isClosed()) {
                channel.disconnect();
            }
            if (sshSession.isConnected()) {
                sshSession.disconnect();
            }
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                    ioe.printStackTrace();
                }
            }

        }
    }


    public static String readZipFile(InputStream input) throws IOException {
        String str = "";
        ZipEntry zipEntry = null;
        ZipInputStream zipInputStream = new ZipInputStream(input, Charset.forName("GBK")); //解决包内文件存在中文时的中文乱码问题
        while ((zipEntry = zipInputStream.getNextEntry()) != null) {
            if (zipEntry.isDirectory()) { //遇到文件夹就跳过
                continue;
            } else {
                str += zipEntry.getName().substring(zipEntry.getName().lastIndexOf("/") + 1);
            }
        }
        return str;

    }
}
