package com.honichang.deploy.controller;

import ch.ethz.ssh2.Connection;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.redis.RedisCache;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.ip.IpUtils;
import com.honichang.deploy.domain.Deploy;
import com.honichang.deploy.utiles.FileUploadUtil;
import com.honichang.deploy.utiles.ServerUtil;
import com.sun.management.OperatingSystemMXBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/deploy")
@Slf4j
public class DeployController extends BaseController {

    @Autowired
    private RedisCache redisCache;

    StringBuilder sb = new StringBuilder();

    /**
     * 连接服务器
     */
    public Connection login(Deploy deploy) {
        // ip
        String host = deploy.getHost();
        // 用户名
        String userName = deploy.getUserName();
        // 密码
        String password = deploy.getPassword();
        Connection login = ServerUtil.login(host, userName, password);
        return login;
    }

    /**
     * 上传更新包及备份操作
     */
    @PostMapping("/importFile")
    public String importFile(MultipartFile file, Deploy deploy) {
        sb = new StringBuilder();
        Connection login = null;
        // 1后台更新 2前端更新
        int editType = deploy.getEditType();
        // 文件储存路径
        String path = deploy.getPath();
        // 文件名称
        String filename = file.getOriginalFilename();

        try {
            String zipFileName = FileUploadUtil.readZipFile(file.getInputStream());
            if (editType == 1) {
                if (!zipFileName.contains(".jar")) {
                    return DateUtils.getTime() + "  请检查压缩包中内容是否正确\n\n";
                }
            }
            if (editType == 2) {
                if (!zipFileName.contains("index.html")) {
                    return DateUtils.getTime() + "  请检查压缩包中内容是否正确\n\n";
                }
            }

            // 停掉DC采集
//            if (editType == 1) {
//                try {
//                    this.DCstop(deploy);
//                } catch (Exception e) {
//                    return DateUtils.getTime() + "  数据采集服务器连接失败，请检查IP是否正确\n\n";
//                }
//            }

            // 文件上传
            FileUploadUtil.uploadFile(file.getOriginalFilename(), file.getInputStream(), deploy);
            sb.append(DateUtils.getTime() + "  文件传输成功\n");

            login = this.login(deploy);

            // 备份
            String historyjarrep = this.backups(login, path, editType);

            // 压缩包处理
            this.packageDispose(login, path, filename);

            if (editType == 1) {
                sb.append("\n------------------- 服务器后台程序，即将重新启动，请不要刷新页面 -------------------\n\n#" + historyjarrep);
            } else {
                sb.append(DateUtils.getTime() + "  前端页面部署完成~~~\n\n");
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }
        return sb.toString();
    }

    /**
     * zip更新包操作
     */
    public void packageDispose(Connection login, String path, String filename) {
        // 解压压缩包
        String unzip = "unzip " + path + filename + " -d " + path;
        sb.append(DateUtils.getTime() + "  命令： " + unzip + "\n");
        ServerUtil.execCommand(login, unzip);
        sb.append(DateUtils.getTime() + "  压缩包解压成功\n");
        // 删除压缩包
        String delzip = "rm -rf " + path + filename;
        sb.append(DateUtils.getTime() + "  命令： " + delzip + "\n");
        ServerUtil.execCommand(login, delzip);
        sb.append(DateUtils.getTime() + "  压缩包删除成功\n");
    }

    /**
     * 更新包备份
     */
    public String backups(Connection login, String path, int editType) {
        // 创建备份根文件夹
        String backup = "mkdir " + path + "backup/";
        ServerUtil.execCommand(login, backup);
        // 查询备份目录下
        if (editType == 1) {
            String getBackup = "find " + path + "backup -maxdepth 1";
            String backupNumber = ServerUtil.execCommand(login, getBackup);
            String[] split = backupNumber.split("\n");
            // 备份目录已存在5个
            if (split.length >= 6) {
                String minbackup = split[1].split("/")[5];
                for (int i = 2; i < split.length; i++) {
                    String s = split[i].split("/")[5];
                    if (Integer.valueOf(s) < Integer.valueOf(minbackup)) {
                        minbackup = s;
                    }
                }
                String delbackup = "rm -R " + path + "backup/" + minbackup;
                ServerUtil.execCommand(login, delbackup);
            }
        }
        if (editType == 2) {
            String getBackup = "find " + path + "backup -maxdepth 1";
            String backupNumber = ServerUtil.execCommand(login, getBackup);
            String[] split = backupNumber.split("\n");
            // 备份目录已存在5个
            if (split.length >= 6) {
                String minbackup = split[1].split("/")[4];
                for (int i = 2; i < split.length; i++) {
                    String s = split[i].split("/")[4];
                    if (Integer.valueOf(s) < Integer.valueOf(minbackup)) {
                        minbackup = s;
                    }
                }
                String delbackup = "rm -R " + path + "backup/" + minbackup;
                ServerUtil.execCommand(login, delbackup);
            }
        }

        // 创建备份文件夹
        String mkdir = "mkdir -p " + path + "backup/" + DateUtils.dateTime() + " 2";
        ServerUtil.execCommand(login, mkdir);

        String historyjarrep = "";
        // 备份原有文件(后台)
        if (editType == 1) {
            String cp = "cp " + path + "robot-admin-*.jar " + path + "backup/" + DateUtils.dateTime();
            sb.append(DateUtils.getTime() + "  命令： " + cp + "\n");
            ServerUtil.execCommand(login, cp);
            //上个jar版本
            String historyjar = "find " + path + " -name robot-admin-*.jar -maxdepth 1 -print";
            historyjarrep = ServerUtil.execCommand(login, historyjar);

            String deljar = "rm -r " + path + "robot-admin-*.jar";
            sb.append(DateUtils.getTime() + "  命令： " + deljar + "\n");
            ServerUtil.execCommand(login, deljar);
        }
        // 备份原有文件(前端)
        if (editType == 2) {
            String cp2 = "cp -r " + path + "dist " + path + "backup/" + DateUtils.dateTime();
            sb.append(DateUtils.getTime() + "  命令： " + cp2 + "\n");
            ServerUtil.execCommand(login, cp2);
            String deldist = "rm -R " + path + "dist";
            sb.append(DateUtils.getTime() + "  命令： " + deldist + "\n");
            ServerUtil.execCommand(login, deldist);
        }
        sb.append(DateUtils.getTime() + "  文件备份成功\n");
        return historyjarrep;
    }

    /**
     * 后台jar重启操作
     */
    @PostMapping("/restartJar")
    public String restartJar(@RequestBody Deploy deploy) {
        StringBuilder sb = new StringBuilder();
        // 1后台更新 2前端更新
        int editType = deploy.getEditType();
        // 历史jar名称
        String historyjarrep = deploy.getHistoryjarrep();
        Connection login = null;
        try {
            login = this.login(deploy);
            // 重启后台程序
            if (editType == 1) {
                String pidres = "";
                if (!historyjarrep.equals("")) {
                    String pid = "ps -ef|grep " + historyjarrep.split("/")[4].replace("\n", "") + "|grep -v grep|awk '{print $2}'";
                    sb.append(DateUtils.getTime() + "  命令： " + pid + "\n");
                    pidres = ServerUtil.execCommand(login, pid);
                }
//                int i = this.OperatingSystem();
                log.info(DateUtils.getTime() + "  ———————————————— 服务器后台程序重新部署，即将重新启动 ————————————————");
                String start = (!pidres.equals("") ? ("kill -9 " + pidres.replace("\n", "") + "; ") : "") +
                        "cd /home/<USER>/robot-admin/; " +
                        "source /etc/profile; " +
                        "nohup java -XX:NewRatio=1 -XX:SurvivorRatio=3 -jar /home/<USER>/robot-admin/robot-admin-*.jar " +
                        "--spring.config.location=/home/<USER>/robot-admin/application.yml " +
                        ">/dev/null 2>&1 &";
                sb.append(DateUtils.getTime() + "  命令： " + start + "\n");
                ServerUtil.execCommand(login, start);

                sb.append(DateUtils.getTime() + "  后台程序部署完成~~~\n\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }
        return sb.toString();
    }

    public void DCstop(Deploy deploy) {
        String host = deploy.getDcHost();
        String userName = deploy.getUserName();
        String password = deploy.getPassword();
        Connection login = null;
        try {
            login = ServerUtil.login(host, userName, password);
            String stop = "pkill WD; pkill DC";
            ServerUtil.execCommand(login, stop);
        } finally {
            if (login != null) {
                login.close();
            }
        }
    }

    /**
     * DC采集启动
     */
    @PostMapping("/DCstart")
    public String DCstart(@RequestBody Deploy deploy) {
        String host = deploy.getDcHost();
        String userName = deploy.getUserName();
        String password = deploy.getPassword();
        Connection login = null;
        try {
            login = ServerUtil.login(host, userName, password);
            String findDC = "find /home/<USER>";
            String findDCrep = ServerUtil.execCommand(login, findDC);
            if (findDCrep.equals("")) {
                findDCrep = ServerUtil.execCommand(login, "find /home/<USER>");
            }
            if (findDCrep.equals("")) {
                findDCrep = ServerUtil.execCommand(login, "find /home/<USER>/ -name Linux_x64_DC_V* -maxdepth 1");
            }
            String[] split = findDCrep.split("\n");
            List<String> collect = Arrays.stream(split).sorted().collect(Collectors.toList());
            String stop = "nohup " + collect.get(collect.size() - 1) + "/WatchDog/WD.EXE &";
            ServerUtil.execCommand(login, stop);
            return DateUtils.getTime() + "  数据采集器启动成功~~~\n\n";

        } catch (Exception e) {
            return DateUtils.getTime() + "  数据采集服务器连接失败\n\n";
        } finally {
            if (login != null) {
                login.close();
            }
        }

    }

    /**
     * 控制台手动命令
     */
    @PostMapping("/handMovementOrder")
    public String handMovementOrder(@RequestBody Deploy deploy) {
        StringBuilder sb = new StringBuilder();
        Connection login = null;
        // 命令
        String order = deploy.getOrder();

        try {
            login = this.login(deploy);
            sb.append(DateUtils.getTime() + "  命令： " + order + "\n");
            String res = ServerUtil.execCommand(login, order);
            sb.append(DateUtils.getTime() + "  结果： " + res + "\n\n");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }

        return sb.toString();
    }

    /**
     * 版本号查询
     */
    @GetMapping("/getCacheValue/{cacheKey}")
    public String getCacheValue(@PathVariable String cacheKey) {
        return redisCache.getCacheObject(cacheKey);
    }

    /**
     * 查询备份的历史jar包版本
     */
    @GetMapping("/getJarVersionList")
    public List getJarVersionList(Deploy deploy) {
        ArrayList List = new ArrayList();
        Connection login = null;
        try {
            login = this.login(deploy);
            String jarPlace = ServerUtil.execCommand(login, "find /home/<USER>/robot-admin/backup/ -name robot-admin-*.jar");
            String[] jarAddrList = jarPlace.split("\n");
            for (String jarAddr : jarAddrList) {
                String jarBackupDate = jarAddr.split("/")[5];
                String jarName = jarAddr.split("/")[6];
                HashMap<String, String> map = new HashMap<>();
                map.put("jarBackupDate", jarBackupDate);
                map.put("jarName", jarName);
                List.add(map);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }

        return List;
    }

    /**
     * 查询备份的历史前端包
     */
    @GetMapping("/getHtmlVersionList")
    public List getHtmlVersionList(Deploy deploy) {
        String path = deploy.getPath();
        ArrayList List = new ArrayList();
        Connection login = null;
        try {
            login = this.login(deploy);
            String distPlace = ServerUtil.execCommand(login, "find " + path + "backup/ -name dist");
            String[] distAddrList = distPlace.split("\n");
            for (String distAddr : distAddrList) {
                String htmlBackupDate = "";
                if (path.equals("/home/<USER>/")) {
                    htmlBackupDate = distAddr.split("/")[4];
                } else {
                    htmlBackupDate = distAddr.split("/")[5];
                }
                HashMap<String, String> map = new HashMap<>();
                map.put("htmlBackupDate", htmlBackupDate);
                List.add(map);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }

        return List;
    }

    /**
     * 后台jar版本回滚操作
     */
    @PostMapping("/jarVersionRollBack")
    public void jarVersionRollBack(@RequestBody Deploy deploy) {
        Connection login = null;
        try {
            login = this.login(deploy);
            String rollbackHistory = deploy.getRollbackHistory();
            // 停掉DC采集
            try {
                this.DCstop(deploy);
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 运行中的jar
            String historyjar = "find " + "/home/<USER>/robot-admin/" + " -name robot-admin-*.jar -maxdepth 1 -print";
            String historyjarrep = ServerUtil.execCommand(login, historyjar);
            String pid = "ps -ef|grep " + historyjarrep.split("/")[4].replace("\n", "") + "|grep -v grep|awk '{print $2}'";
            String pidres = ServerUtil.execCommand(login, pid);
            // 删除正常运行的jar包
            String deljar = "rm -r " + "/home/<USER>/robot-admin/" + "robot-admin-*.jar";
            ServerUtil.execCommand(login, deljar);
            // 将准备回滚的历史版本拷贝到运行目录下
            String cp = "cp " + "/home/<USER>/robot-admin/backup/" + rollbackHistory + " /home/<USER>/robot-admin";
            ServerUtil.execCommand(login, cp);

//            int i = this.OperatingSystem();

            // 杀掉正在运行的包运行回滚的
            String start = (!pidres.equals("") ? ("kill -9 " + pidres.replace("\n", "") + "; ") : "") +
                    "cd /home/<USER>/robot-admin/; source /etc/profile; nohup java -XX:NewRatio=1 -XX:SurvivorRatio=3 -jar /home/<USER>/robot-admin/robot-admin-*.jar " +
                    "--spring.config.location=/home/<USER>/robot-admin/application.yml,/home/<USER>/robot-admin/application-prod.yml " +
                    ">/dev/null 2>&1 &";
            ServerUtil.execCommand(login, start);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }
    }

    // TODO 获取物理内存
    public int OperatingSystem() {
        OperatingSystemMXBean mem = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        // 获取内存总容量
        long totalMemorySize = mem.getTotalPhysicalMemorySize();
        long l = totalMemorySize / 1024 / 1024 / 1024;
        int i = Integer.parseInt(l + "") / 2;
        log.info("物理内存总容量：" + i);
        return i;
    }

    /**
     * 前端dist版本回滚操作
     */
    @PostMapping("/htmlVersionRollBack")
    public String htmlVersionRollBack(@RequestBody Deploy deploy) {
        String path = deploy.getPath();
        Connection login = null;
        try {
            login = this.login(deploy);
            String rollbackHistory = deploy.getRollbackHistory();
            // 删除正常运行的dist
            String deldist = "rm -R " + path + "dist";
            ServerUtil.execCommand(login, deldist);
            // 将准备回滚的历史版本拷贝到运行目录下
            String cp = "cp -r " + path + "backup/" + rollbackHistory + "/dist" + " " + path.substring(0, path.length() - 1);
            ServerUtil.execCommand(login, cp);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (login != null) {
                login.close();
            }
        }
        return DateUtils.getTime() + "  前端页面回滚完成~~~\n\n";
    }

    /**
     * 证书上传
     */
    @PostMapping("/CAUpload")
    public AjaxResult CAUpload(MultipartFile file, Deploy deploy) {
        deploy.setHost(IpUtils.getHostIp());
        deploy.setPath("/home/<USER>/robot-admin/KS/");
        Connection login = null;
        try {
            login = this.login(deploy);
            String mkdir = "mkdir /home/<USER>/robot-admin/KS/";
            ServerUtil.execCommand(login, mkdir);
            FileUploadUtil.uploadFile(file.getOriginalFilename(), file.getInputStream(), deploy);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("服务器连接失败");
        } finally {
            if (login != null) {
                login.close();
            }
        }
        return success();
    }
}
