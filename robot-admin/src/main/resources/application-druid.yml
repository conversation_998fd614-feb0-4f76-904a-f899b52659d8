# 数据源配置
spring:
    # redis 配置
    redis:
        # 地址
        host: *************
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 0
        # 密码
        password:
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: org.postgresql.Driver
        druid:
            # 主库数据源
            master:
                url: *****************************************************************************************************************************
                username: postgres
                password: ky888888
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: hyc
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

        tdengine:
            driver-class-name: com.taosdata.jdbc.TSDBDriver
            # create database test duration 1d keep 365 replica 3; 建表语句
            url: ***************************************************************
            username: root
            password: taosdata
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 100
            #连接超时时间
            connectionTimeout: 30000
            maxLifetime: 60000
    flyway:
        enabled: false # 开启
        encoding: UTF-8 #字符编码
        clean-disabled: false # 禁止清理数据表
        table: flyway_schema_history # 版本控制信息表名，默认 flyway_schema_history
        out-of-order: false # 是否允许不按顺序迁移
        baseline-on-migrate: true  # 如果数据库不是空表，需要设置成 true，否则启动报错
        baseline-version: 0 # 与 baseline-on-migrate: true 搭配使用，小于此版本的不执行
        locations: classpath:db/migration
    netty:
        event-loop:
            thread-name-prefix: message_test
            client-group-threads: 4  # 客户端线程数
            use-epoll: false # 是否使用 Epoll（Linux only）
            task-timeout-ms: 3000 # 任务执行超时时间（毫秒）
        service: #工控接口调用超时时间
            timeout-ms:3000