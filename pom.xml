<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.honichang</groupId>
    <artifactId>robot</artifactId>
    <version>3.8.9</version>

    <name>robot</name>
    <description>智能机器人系统</description>

    <properties>
        <robot.version>3.8.9</robot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <spring-boot.version>2.5.15</spring-boot.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.53</fastjson.version>
        <oshi.version>6.8.1</oshi.version>
        <commons.io.version>2.19.0</commons.io.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>

        <postgresql.version>42.3.3</postgresql.version>
        <mybatisplus.version>3.5.3.2</mybatisplus.version>
        <!--        <mybatisplus.version>3.3.2</mybatisplus.version>-->
        <mybatis.version>3.5.10</mybatis.version>
        <lombok.version>1.18.24</lombok.version>
        <tdengine.driver.version>3.2.4</tdengine.driver.version>
        <kafka.version>2.8.1</kafka.version>
        <netty.version>4.1.90.Final</netty.version>
        <redisson.version>3.17.2</redisson.version>
        <batik.version>1.14</batik.version>
        <flyway.version>6.5.7</flyway.version>
        <kaptcha.version>2.3.3</kaptcha.version>

        <!-- override dependency version -->
        <tomcat.version>9.0.105</tomcat.version>
        <logback.version>1.2.13</logback.version>
        <spring-security.version>5.7.12</spring-security.version>
        <spring-framework.version>5.3.39</spring-framework.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- 覆盖SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 覆盖SpringSecurity的依赖配置-->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 覆盖logback的依赖配置-->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- 覆盖tomcat的依赖配置-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- SpringBoot集成mybatis框架 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>icu.mhb</groupId>
                <artifactId>mybatis-plus-join</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- Swagger3依赖 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-all</artifactId>
                <version>${batik.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>

            <!-- netty -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-quartz</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-generator</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-framework</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-system</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-common</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 对外接口-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-api</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- 业务模块-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-machine</artifactId>
                <version>${robot.version}</version>
            </dependency>
            <!-- netty-->
            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-netty</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-camera</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-point</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-deploy</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-dispatch</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.honichang</groupId>
                <artifactId>robot-cartography</artifactId>
                <version>${robot.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>robot-admin</module>
        <module>robot-framework</module>
        <module>robot-system</module>
        <module>robot-quartz</module>
        <module>robot-generator</module>
        <module>robot-common</module>
        <module>robot-api</module>
        <module>robot-machine</module>
        <module>robot-netty</module>
        <module>robot-camera</module>
        <module>robot-point</module>
        <module>robot-deploy</module>
        <module>robot-dispatch</module>
        <module>robot-cartography</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
