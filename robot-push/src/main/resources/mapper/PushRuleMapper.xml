<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.push.mapper.PushRuleMapper">

    <resultMap id="BaseResultMap" type="com.honichang.push.domain.PushRule">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
            <result property="pushChannel" column="push_channel" jdbcType="VARCHAR"/>
            <result property="pushType" column="push_type" jdbcType="VARCHAR"/>
            <result property="alarmLevel" column="alarm_level" jdbcType="VARCHAR"/>
            <result property="templateConfigId" column="template_config_id" jdbcType="INTEGER"/>
            <result property="userIds" column="user_ids" jdbcType="VARCHAR"/>
            <result property="pointIds" column="point_ids" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="repeatStatus" column="repeat_status" jdbcType="INTEGER"/>
            <result property="repeatTime" column="repeat_time" jdbcType="DOUBLE"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_name,push_channel,
        push_type,alarm_level,template_config_id,
        user_ids,point_ids,remark,
        repeat_status,repeat_time,status,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>
    <select id="selectPageList" resultType="com.honichang.push.domain.PushRule">

        SELECT
        a.*,
        (
        SELECT
        string_agg ( dict_label || '', ',' )
        FROM
        sys_dict_data u
        WHERE
        u.dict_type = 'push_channel' and u.dict_value  IN ( SELECT * FROM regexp_split_to_table( a.push_channel, ',' ) )
        ) pushChannelNames,
        (
        SELECT
        string_agg ( dict_label || '', ',' )
        FROM
        sys_dict_data u
        WHERE
        u.dict_type = 'rule_result' and u.dict_value  IN ( SELECT * FROM regexp_split_to_table( a.alarm_level, ',' ) )
        ) alarmLevelNames,
        (
        SELECT
        string_agg ( user_name || '', ',' )
        FROM
        sys_user u
        WHERE
        u.user_id :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( a.user_ids, ',' ) )
        ) userNames,
        (
        SELECT
        string_agg ( instance_name || '', ',' )
        FROM
        biz_point vm
        WHERE
        vm.ID :: VARCHAR IN ( SELECT * FROM regexp_split_to_table( a.point_ids, ',' ) )
        ) pointNames,
        ( SELECT template_name FROM push_template_config tc WHERE tc.ID = a.template_config_id ) templateName
        FROM
        push_rule a
        where 1=1
        and a.del_flag = '0'
        <if test="ruleName != null">
            AND a.rule_name  LIKE '%'||#{ruleName}||'%'
        </if>
        <if test="pushType != null">
            AND a.push_type  =  #{pushType}
        </if>
        order by id desc

    </select>
    <select id="getAlarmPointRule" resultType="com.honichang.push.domain.PushRule">
        SELECT
            *
        FROM
            push_rule
        WHERE
            1 = 1
            AND status = '0'
            AND push_type = '1'
            AND #{bizPointId} = ANY ( string_to_array( point_ids, ',' ) )
            AND #{alarmLevel} = ANY ( string_to_array( alarm_level, ',' ) )
    </select>
</mapper>
