<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.push.mapper.PushSmtpConfigMapper">

    <resultMap id="BaseResultMap" type="com.honichang.push.domain.PushSmtpConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="smtpHost" column="smtp_host" jdbcType="VARCHAR"/>
            <result property="smtpPort" column="smtp_port" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="sslStatus" column="ssl_status" jdbcType="VARCHAR"/>
            <result property="isHtml" column="is_html" jdbcType="BOOLEAN"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,smtp_host,smtp_port,
        account,password,ssl_status,
        is_html,status,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
