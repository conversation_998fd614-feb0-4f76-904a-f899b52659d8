package com.honichang.push.mapper;

import com.honichang.push.domain.PushRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.honichang.push.domain.PushRule
 */
@Mapper
public interface PushRuleMapper extends BaseMapper<PushRule> {

    List<PushRule> selectPageList(PushRule pushRule);

    List<PushRule> getAlarmPointRule(@Param("bizPointId") String bizPointId, @Param("alarmLevel") String alarmLevel);
}




