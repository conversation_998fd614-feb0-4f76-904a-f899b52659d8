package com.honichang.push.service;

import com.honichang.common.core.domain.AjaxResult;
import com.honichang.push.domain.PushResult;

public interface IMailService {
    /**
     * 测试连接
     */
    void testConnection();

    /**
     * 发送简单邮件
     *
     * @param pushResult      发送参数
     * @param system         系统发送
     */
    public AjaxResult sendTextMail(PushResult pushResult, Boolean system);

    /**
     * 重置smtp配置
     */
    public void reset();
}
