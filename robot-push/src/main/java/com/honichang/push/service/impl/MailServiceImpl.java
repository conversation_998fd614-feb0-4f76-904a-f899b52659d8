package com.honichang.push.service.impl;


import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.push.domain.PushResult;
import com.honichang.push.domain.PushSmtpConfig;
import com.honichang.push.enums.OlyStageRoot;
import com.honichang.push.service.AbstractMailService;
import com.honichang.push.service.PushResultService;
import com.honichang.push.service.PushSmtpConfigService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.File;
import java.nio.file.Paths;

@Service
public class MailServiceImpl extends AbstractMailService {

    @Autowired
    private PushSmtpConfigService pushSmtpConfigService;

    @Autowired
    private PushResultService pushResultService;

    protected MailServiceImpl(PushSmtpConfigService pushSmtpConfigService){
        super(pushSmtpConfigService);
    }


    @Override
    public AjaxResult sendTextMail(PushResult pushResult, Boolean system) {
        PushSmtpConfig pushSmtpConfig = pushSmtpConfigService.getSmtpConfig();
        if(pushSmtpConfig == null){
            return AjaxResult.error(1000, "邮件未配置");
        }
        AjaxResult ajaxResult = sendMailTemplate(false, pushResult, messageHelper -> {
            try {
                commonEmail(pushResult, system, messageHelper);
                String context = pushResult.getContent();
//                context = context.replaceAll("\r\n", "<br/>");
                if(pushSmtpConfig.getIsHtml() != null && pushSmtpConfig.getIsHtml().booleanValue() == true){
                    context = context.replaceAll("\n", "<br/>");
                    messageHelper.setText(context, true);//解决邮件内容换行问题
                }else {
//                    context = context.replaceAll("\n", "<br/>");
                    messageHelper.setText(context, false);//解决邮件内容换行问题
                }

            } catch (MessagingException e) {
                throw new ServiceException(e.getMessage(), 1000);
//                throw new RuntimeException("Failed to set message subject, to or test!", e);
            }
        });
        if(Integer.parseInt(StringUtils.format(ajaxResult.get("code"))) == 200){
            pushResult.setStatus("1");
        }else{
            pushResult.setStatus("0");
        }
        pushResult.setCreateTime(DateUtils.getNowDate());
        pushResultService.save(pushResult);
        return ajaxResult;
    }

    @Override
    public void reset() {
        clearCache();
    }

    private void commonEmail(PushResult pushResult, Boolean system, MimeMessageHelper messageHelper) {
        String to = pushResult.getReceiver();
        String subject = pushResult.getTitle();
//        String cc = smtpSendResult.getCopyTo();
//        String bcc = smtpSendResult.getBccTo();
//        String attachFilePath = smtpSendResult.getAttachKeys();
        String cc = null;
        String bcc = null;
        String attachFilePath = null;
        try{
            messageHelper.setTo(to);
            messageHelper.setSubject(subject);
            if (StringUtils.isNotEmpty(cc)) {
                for (String cs : cc.split(";")) {
                    messageHelper.addCc(cs);
                }
            }
            if (StringUtils.isNotEmpty(bcc)) {
                for (String ms : bcc.split(";")) {
                    messageHelper.addCc(ms);
                }
            }
            File g = null;
            if (StringUtils.isNotEmpty(attachFilePath)) {

                for (String a : attachFilePath.split(";")) {
                    if (!system) {
                        g = Paths.get(OlyStageRoot.MAIL_DIR.getRoot(a)).toFile();
                    } else {
                        g = Paths.get(a).toFile();
                    }
                    if (g.exists()) {
                        messageHelper.addAttachment(FilenameUtils.getName(g.toString()), g);
                    }

                }
            }

        }catch (MessagingException e){

        }


    }

}
