package com.honichang.push.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.honichang.common.core.domain.entity.SysDictData;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.DictUtils;
import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.push.domain.PushResult;
import com.honichang.push.domain.PushRule;
import com.honichang.push.domain.PushSmtpConfig;
import com.honichang.push.domain.PushTemplateConfig;
import com.honichang.push.enums.PushChannelEnums;
import com.honichang.push.model.dto.AlarmEscortTemplateParams;
import com.honichang.push.service.*;
import com.honichang.push.util.ReplaceUtils;
import com.honichang.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PushMessageServiceImpl implements PushMessageService {

    @Autowired
    private PushRuleService pushRuleService;

    @Autowired
    private PushTemplateConfigService pushTemplateConfigService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISmsService smsService;

    @Autowired
    private IMailService mailService;

    @Autowired
    private PushSmtpConfigService pushSmtpConfigService;


    @Override
    public void sendAlarmPointMessage(TaskInstanceNode taskInstanceNode) {

    }

    @Override
    public void sendAlarmEscortMessage(AlarmEscort alarmEscort) {


        List<PushRule> pushRuleList = pushRuleService.getAlarmEscortRule();


        List<SysDictData> datas = DictUtils.getDictCache("escort_alarm_type");
        if(datas != null ){
            SysDictData dictEscortAlarmType = datas.stream().filter(b -> b.getDictValue().equals(String.valueOf(alarmEscort.getAlarmType()))).findAny().orElse(null);

            AlarmEscortTemplateParams alarmEscortTemplateParams = new AlarmEscortTemplateParams();
            alarmEscortTemplateParams.setAlarmType(dictEscortAlarmType.getDictLabel());
            alarmEscortTemplateParams.setAlarmTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, alarmEscort.getCreateTime()));
            alarmEscortTemplateParams.setAlarmContent(alarmEscort.getAlarmContent());

            for(PushRule pushRule :pushRuleList){

                Integer templateConfigId = pushRule.getTemplateConfigId();//获取模板
                String userIds = pushRule.getUserIds();//获取模本配置了哪些推送用户
                String pushChannels = pushRule.getPushChannel();//获取推送渠道

                if(templateConfigId != null && pushChannels != null && !pushChannels.equals("") && userIds!= null && !userIds.equals("")){
                    List<Integer> _pushChannels_ = Arrays.stream(pushChannels.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    List<Long> _userIds_ = Arrays.stream(userIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                    List<SysUser> sysUserList = sysUserService.selectUserByUserIds(_userIds_.toArray(new Long[_userIds_.size()]));

                    PushTemplateConfig templateConfig = pushTemplateConfigService.getById(templateConfigId);
                    if(templateConfig != null && sysUserList != null && sysUserList.size() > 0){//模板不为空并且设置推送用户也不为空

                        for(Integer pushChannel : _pushChannels_){

                            sendAlarmEscort(templateConfig, pushChannel, alarmEscortTemplateParams, sysUserList, String.valueOf(alarmEscort.getId()));

                        }

                    }else{
                        log.error("陪同报警发送失败--规则获取失败，templateConfig：{}" , JSONObject.toJSONString(templateConfig));
                        log.error("陪同报警发送失败--规则获取失败，sysUserList：{}" , JSONArray.toJSONString(sysUserList));
                    }
                }else{
                    log.error("陪同报警发送失败--规则获取失败，templateConfigId：{}，userIds：{}" , templateConfigId, userIds);
                }
            }
        }else {
            log.error("陪同报警发送失败--规则陪同报警类型异常，AlarmEscort：{}" , com.alibaba.fastjson.JSONObject.toJSONString(alarmEscort));

        }

    }


    /**
     *
     * @param templateConfig    推送模板
     * @param pushChannel       推送渠道，例如邮件，短信
     * @param alarmEscortTemplateParams    推送模板需要的参数，例如${paramName}
     * @param sysUserList       推送的人员列表
     * @param businessId        真实设备id
     */
    void sendAlarmEscort(PushTemplateConfig templateConfig, Integer pushChannel, AlarmEscortTemplateParams alarmEscortTemplateParams, List<SysUser> sysUserList, String businessId){


        if(pushChannel.intValue() == Integer.parseInt(PushChannelEnums.MAIL.getValue())){//邮件

            PushSmtpConfig pushSmtpConfig = pushSmtpConfigService.getSmtpConfig();
            if(pushSmtpConfig == null){
                return;
            }

            String templateContent = templateConfig.getTemplateContent();//获取模板配置内容
            String templateTitle = templateConfig.getTemplateTitle();//获取模板标题
//            templateTitle = templateTitle + process.getValue();//标题后缀增加首次或者重复标记

            String context = ReplaceUtils.replaceAlarmEscortStr(templateContent, alarmEscortTemplateParams, new ArrayList<>());//替换发送内容里参数

            for(SysUser sysUser : sysUserList){

                String toMail = sysUser.getEmail();
                String niceName = sysUser.getNickName();
                if(toMail != null && !toMail.equals("") && niceName != null && !niceName.equals("")){
                    PushResult pushResult = new PushResult();
                    pushResult.setReceiver(toMail);
                    pushResult.setSender("robot系统");
                    pushResult.setTitle(templateTitle);
                    pushResult.setContent(context);
                    pushResult.setTemplateConfigId(templateConfig.getId());
                    pushResult.setBusinessId(businessId);
                    mailService.sendTextMail(pushResult, false);
                }else{
                    log.warn("邮件发送失败--发送人获取失败，sysUser：{}" , JSONObject.toJSONString(sysUser));
                }
            }

        }

        if(pushChannel.intValue() == Integer.parseInt(PushChannelEnums.SMS.getValue())){//短信

            String templateContent = templateConfig.getTemplateContent();//获取模板配置内容
            String templateTitle = templateConfig.getTemplateTitle();//获取模板标题
//            templateTitle = templateTitle + process.getValue();//标题后缀增加首次或者重复标记

            String context = templateTitle + "，" + ReplaceUtils.replaceAlarmEscortStr(templateContent, alarmEscortTemplateParams, new ArrayList<>());//替换发送内容里参数

            for(SysUser sysUser : sysUserList){

                String mobile = sysUser.getPhonenumber();
                if(mobile != null && !mobile.equals("") ){
                    PushResult pushResult = new PushResult();
                    pushResult.setReceiver(mobile);
                    pushResult.setContent(context);
                    pushResult.setSendTime(new Date());
                    pushResult.setTemplateConfigId(templateConfig.getId());
                    pushResult.setBusinessId(businessId);
                    pushResult.setCreateTime(new Date());
                    smsService.sendMessage(pushResult);
                }else{
                    log.warn("短信发送失败--发送人获取失败，sysUser：{}" , JSONObject.toJSONString(sysUser));
                }
            }
        }
    }

}
