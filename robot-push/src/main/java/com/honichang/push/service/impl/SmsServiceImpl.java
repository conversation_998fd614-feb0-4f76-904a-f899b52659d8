package com.honichang.push.service.impl;

import com.honichang.common.core.domain.entity.SysDictData;
import com.honichang.common.utils.DictUtils;
import com.honichang.push.domain.PushResult;
import com.honichang.push.handler.SmsCommHandler;
import com.honichang.push.handler.SmsSocketHandler;
import com.honichang.push.service.ISmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SmsServiceImpl implements ISmsService {

    /**
     * 网口发送短信接口
     */
    @Autowired
    private SmsSocketHandler smsSocketHandler;

    /**
     * 串口发送短信接口
     */
    @Autowired
    private SmsCommHandler smsCommHandler;


    @Override
    public void sendMessage(PushResult pushResult) {

        List<SysDictData> datas = DictUtils.getDictCache("sms_config");
        if(datas != null ){
            SysDictData dictSendMode = datas.stream().filter(b -> b.getDictLabel().equals("send_mode")).findAny().orElse(null);
            if(dictSendMode != null && dictSendMode.getDictValue().equals("2")){//1串口2网口（如果不配做默认串口，因为第一个上线的厂子用的是串口，当时没有这个功能配置）
                smsSocketHandler.sendMessage(pushResult);
            }else {
                smsCommHandler.sendMessage(pushResult);
            }
        }

    }

    @Override
    public void sendSimpleMessage(String mobile, String content) {
        List<SysDictData> datas = DictUtils.getDictCache("sms_config");
        if(datas != null ){
            SysDictData dictSendMode = datas.stream().filter(b -> b.getDictLabel().equals("send_mode")).findAny().orElse(null);
            if(dictSendMode != null && dictSendMode.getDictValue().equals("2")){//1串口2网口（如果不配做默认串口，因为第一个上线的厂子用的是串口，当时没有这个功能配置）
                smsSocketHandler.sendSimpleMessage(mobile, content);
            }else {
                smsCommHandler.sendSimpleMessage(mobile, content);
            }
        }
    }

}
