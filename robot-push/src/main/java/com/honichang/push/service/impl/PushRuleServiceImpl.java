package com.honichang.push.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.push.domain.PushRule;
import com.honichang.push.mapper.PushRuleMapper;
import com.honichang.push.service.PushRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Slf4j
@Service
public class PushRuleServiceImpl extends ServiceImpl<PushRuleMapper, PushRule>
    implements PushRuleService{

    @Autowired
    private PushRuleMapper pushRuleMapper;

    @Override
    public List<PushRule> selectPageList(PushRule pushRule) {
        return pushRuleMapper.selectPageList(pushRule);
    }

    @Override
    public void add(PushRule pushRule) {

        pushRule.setPushChannel(StringUtils.join(pushRule.getPushChannelList(), ","));
        pushRule.setAlarmLevel(pushRule.getPushType().equals("1")  ? StringUtils.join(pushRule.getAlarmLevelList(), ",") : null);//只有为报警提醒才能设置报警等级
        pushRule.setUserIds(pushRule.getPushType().equals("1") ? StringUtils.join(pushRule.getUserIdsList(), ",") : null);
        pushRule.setPointIds(pushRule.getPushType().equals("1") ? StringUtils.join(pushRule.getPointIdsList(), ",") : null);

        pushRule.setStatus("0");
        pushRule.setDelFlag("0");
        pushRule.setCreateTime(DateUtils.getNowDate());
        pushRule.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(pushRule);

    }

    @Override
    public void update(PushRule pushRule) {

        this.lambdaUpdate()
                .set(PushRule::getRuleName, pushRule.getRuleName())
                .set(PushRule::getPushChannel, StringUtils.join(pushRule.getPushChannelList(), ","))
                .set(PushRule::getAlarmLevel, pushRule.getPushType().equals("1") ? StringUtils.join(pushRule.getAlarmLevelList(), ",") : null)
                .set(PushRule::getPushType, pushRule.getPushType())
                .set(PushRule::getTemplateConfigId, pushRule.getTemplateConfigId())
                .set(PushRule::getUserIds, pushRule.getPushType().equals("1") ? StringUtils.join(pushRule.getUserIdsList(), ",") : null)
                .set(PushRule::getPointIds, pushRule.getPushType().equals("1") ?StringUtils.join(pushRule.getPointIdsList(), ",") : null)
                .set(PushRule::getRemark, pushRule.getRemark())
                .set(PushRule::getStatus, pushRule.getStatus())

                .set(PushRule::getUpdateTime, DateUtils.getNowDate())
                .set(PushRule::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(PushRule::getId, pushRule.getId())
                .update();
    }

    @Override
    public void delete(Long id) {

        PushRule pushRule = this.getById(id);
        if (pushRule == null) {
            throw new ServiceException("删除失败，未查询到！", HttpStatus.ERROR);
        }
        this.lambdaUpdate()
                .set(PushRule::getDelFlag, "2")
                .eq(PushRule::getId, id)
                .update();
    }

    @Override
    public List<PushRule> getAlarmPointRule(String bizPointId, String alarmLevel) {

        return null;
    }

    @Override
    public List<PushRule> getAlarmEscortRule() {
        LambdaQueryWrapper<PushRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PushRule::getDelFlag, "0");
        queryWrapper.eq(PushRule::getPushType, "2");
        return this.list(queryWrapper);
    }
}




