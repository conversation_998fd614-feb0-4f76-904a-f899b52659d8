package com.honichang.push.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.push.domain.PushSmtpConfig;
import com.honichang.push.service.PushSmtpConfigService;
import com.honichang.push.mapper.PushSmtpConfigMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class PushSmtpConfigServiceImpl extends ServiceImpl<PushSmtpConfigMapper, PushSmtpConfig>
    implements PushSmtpConfigService{

    @Override
    public PushSmtpConfig getSmtpConfig() {
        LambdaQueryWrapper<PushSmtpConfig> queryWrapper = new LambdaQueryWrapper<>();
        List<PushSmtpConfig> list = this.list(queryWrapper);
        if(list.size() > 0){
            return  list.get(0);
        }
        return null;
    }

}




