package com.honichang.push.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;

import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.push.config.DefaultMailProperties;
import com.honichang.push.config.MailSenderFactory;
import com.honichang.push.domain.PushResult;
import com.honichang.push.domain.PushSmtpConfig;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.lang.Nullable;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

@Slf4j
public abstract class AbstractMailService implements IMailService{

    private static final int DEFAULT_POOL_SIZE = 5;

    private JavaMailSender cachedMailSender;

    private DefaultMailProperties cachedMailProperties;

    private String cachedFromName;

    protected final PushSmtpConfigService pushSmtpConfigService;

    @Nullable
    private ExecutorService executorService;

    protected AbstractMailService(PushSmtpConfigService pushSmtpConfigService) {
        this.pushSmtpConfigService = pushSmtpConfigService;
    }

    private PushSmtpConfig pushSmtpConfig;

    @Override
    public void testConnection() {

        JavaMailSender javaMailSender = getMailSender();
        if (javaMailSender instanceof JavaMailSenderImpl) {
            JavaMailSenderImpl mailSender = (JavaMailSenderImpl) javaMailSender;
            try {
                mailSender.testConnection();
            } catch (MessagingException e) {
                log.info("无法连接到邮箱服务器，请检查邮箱配置.[" + e.getMessage() + "]", e);

            }
        }
    }

    protected void clearCache() {
        this.cachedMailSender = null;
        this.cachedFromName = null;
        this.cachedMailProperties = null;
    }


    protected AjaxResult sendMailTemplate(@Nullable Consumer<MimeMessageHelper> callback, PushResult pushResult) {

        if(pushResult.getSender() == null || pushResult.getSender().equals("")){
            log.error("邮件发送失败--发送者邮箱为空,smtpSendResult:{}", JSONObject.toJSONString(pushResult));
            return AjaxResult.error(1000, "发送者邮箱为空");
        }
        // 获取邮件 sender
        JavaMailSender mailSender = getMailSender();
        // 打印邮件配置
        printMailConfig();
        try {
            if(this.pushSmtpConfig == null){
                this.pushSmtpConfig = pushSmtpConfigService.getSmtpConfig();;
            }
            // create mime message helper
            MimeMessageHelper messageHelper = null;
            if(pushSmtpConfig != null && pushSmtpConfig.getIsHtml() != null && pushSmtpConfig.getIsHtml().booleanValue() == false){
                log.warn("邮件发送--文本格式发送,smtpConfig:{}", JSONObject.toJSONString(pushSmtpConfig));
                messageHelper = new MimeMessageHelper(mailSender.createMimeMessage(), false, "us-ascii");
            }else{
                log.warn("邮件发送--html格式发送,smtpConfig:{}", JSONObject.toJSONString(pushSmtpConfig));
                messageHelper = new MimeMessageHelper(mailSender.createMimeMessage(), true);
            }
            // set from-name
            messageHelper.setFrom(getFromAddress(mailSender , pushResult.getSender()));
            // handle message set separately
            callback.accept(messageHelper);

            // get mime message
            MimeMessage mimeMessage = messageHelper.getMimeMessage();

            pushResult.setSendTime(DateUtils.getNowDate());
            pushResult.setSender(getMailProperties().getUsername());

            // send email
            mailSender.send(mimeMessage);
            log.info("Sent an email to [{}] successfully, subject: [{}], sent date: [{}]",
                    Arrays.toString(mimeMessage.getAllRecipients()), mimeMessage.getSubject(),
                    mimeMessage.getSentDate());

            return AjaxResult.success("发送成功");
        } catch (Exception e) {
            log.error("邮件发送失败--：{}", e.getMessage());
            return AjaxResult.error(1000, e.getMessage());
//            throw new ServiceException(e.getMessage(), 1000);
//            throw new RuntimeException("邮件发送失败，请检查 SMTP 服务配置是否正确", e);
        }
    }

    /**
     * Send mail template if executor service is enable.
     *
     * @param callback   callback message handler
     * @param tryToAsync if the send procedure should try to asynchronous
     */
    protected AjaxResult sendMailTemplate(boolean tryToAsync, PushResult pushResult,
                                          @Nullable Consumer<MimeMessageHelper> callback) {
        ExecutorService executorService = getExecutorService();
        if (tryToAsync && executorService != null) {
            // send mail asynchronously
            executorService.execute(() -> sendMailTemplate(callback, pushResult));
            return AjaxResult.success();
        } else {
            // send mail synchronously
            return sendMailTemplate(callback, pushResult);
        }
    }

    @NonNull
    public ExecutorService getExecutorService() {
        if (this.executorService == null) {
            this.executorService = Executors.newCachedThreadPool();
        }
        return executorService;
    }

    /**
     * Print mail configuration.
     */
    private void printMailConfig() {
        if (!log.isDebugEnabled()) {
            return;
        }

        // get mail properties
        MailProperties mailProperties = getMailProperties();
        log.debug(mailProperties.toString());
    }

    /**
     * Get from-address.
     *
     * @param javaMailSender java mail sender.
     * @return from-name internet address
     * @throws UnsupportedEncodingException throws when you give a wrong character
     *                                      encoding
     */
    private synchronized InternetAddress getFromAddress(@NonNull JavaMailSender javaMailSender, String fromMail)
            throws UnsupportedEncodingException {
        Assert.notNull(javaMailSender, "Java mail sender must not be null");

//        if (StringUtils.isBlank(this.cachedFromName)) {
//            // set personal name
//            this.cachedFromName = fromMail;
//        }
        this.cachedFromName = fromMail;


        if (javaMailSender instanceof JavaMailSenderImpl) {
            // get user name(email)
            JavaMailSenderImpl mailSender = (JavaMailSenderImpl) javaMailSender;
            String username = mailSender.getUsername();

            // build internet address
            return new InternetAddress(username, this.cachedFromName, mailSender.getDefaultEncoding());
        }

//        throw new UnsupportedOperationException("Unsupported java mail sender: " + javaMailSender.getClass().getName());
        throw new ServiceException("Unsupported java mail sender: " + javaMailSender.getClass().getName(), 1000);
    }

    @NonNull
    private synchronized JavaMailSender getMailSender() {
        if (this.cachedMailSender == null) {
            // create mail sender factory
            MailSenderFactory mailSenderFactory = new MailSenderFactory();
            // get mail sender
            this.cachedMailSender = mailSenderFactory.getMailSender(getMailProperties());
        }

        return this.cachedMailSender;
    }

    @NonNull
    protected synchronized DefaultMailProperties getMailProperties() {
        if (cachedMailProperties == null) {

            PushSmtpConfig pushSmtpConfig = pushSmtpConfigService.getSmtpConfig();
            this.pushSmtpConfig = pushSmtpConfig;
            // create mail properties
            DefaultMailProperties mailProperties = new DefaultMailProperties();
            // set properties
            mailProperties.setHost(pushSmtpConfig.getSmtpHost());
            mailProperties.setPort(Integer.parseInt(pushSmtpConfig.getSmtpPort()));
            mailProperties.setProtocol("smtp");
            if(pushSmtpConfig.getPassword()!=null && !pushSmtpConfig.getPassword().equals("")){
                mailProperties.setPassword(pushSmtpConfig.getPassword());
            }
            mailProperties.setUsername(pushSmtpConfig.getAccount());
            Map<String, String> starttls = new HashMap<>();
            if (pushSmtpConfig.getSslStatus().equals("0")) {
                starttls.put("mail.smtp.starttls.enable", "true");
            }
            if(pushSmtpConfig.getPassword()!=null && !pushSmtpConfig.getPassword().equals("")){
                starttls.put("mail.smtp.auth", "true");
            }else{
                starttls.put("mail.smtp.auth", "false");
            }
            mailProperties.setProperties(starttls);
            this.cachedMailProperties = mailProperties;
        }

        return this.cachedMailProperties;
    }
}

