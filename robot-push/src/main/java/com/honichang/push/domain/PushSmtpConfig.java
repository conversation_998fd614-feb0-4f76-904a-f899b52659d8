package com.honichang.push.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 邮件配置
 * @TableName push_smtp_config
 */
@TableName(value ="push_smtp_config")
@Data
public class PushSmtpConfig implements Serializable {
    /**
     * id
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * smtp地址
     */
    @TableField(value = "smtp_host")
    private String smtpHost;

    /**
     * smtp端口
     */
    @TableField(value = "smtp_port")
    private String smtpPort;

    /**
     * 邮箱账号
     */
    @TableField(value = "account")
    private String account;

    /**
     * 邮箱密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * ssl加密
     */
    @TableField(value = "ssl_status")
    private String sslStatus;

    /**
     * 邮件正文格式
     */
    @TableField(value = "is_html")
    private Boolean isHtml;

    /**
     * 
     */
    @TableField(value = "status")
    private String status;

    /**
     * 
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     * 
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}