package com.honichang.push.util;

import com.honichang.common.utils.StringUtils;
import com.honichang.push.model.dto.AlarmPointTemplateParams;
import com.honichang.push.model.dto.AlarmEscortTemplateParams;

import java.lang.reflect.Field;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class ReplaceUtils {

    /**
     * 获取表达式中${}中的值
     *
     * @param content
     * @return
     */
    static Pattern regex = Pattern.compile("\\$\\{([^}]*)\\}");

    /**
     * 替换${}里的值，属性名和${}一致
     * @param templateContent   模版
     * @param alarmPointTemplateParams    替换模版中参数的对象
     * @param styleFileds       需要处理增加颜色的参数几个（只有邮件支持，因为邮件发送内容是以html，所有支持样式），但是如果是邮件但是以文本发送的，就不能增加样式啦
     * @return
     */
    public static String replaceAlarmPointStr(String templateContent, AlarmPointTemplateParams alarmPointTemplateParams, List<String> styleFileds) {
        String spliteStr = getContentInfo(templateContent);
        String spliteRepliceStr = checkAlarmPoint(spliteStr, alarmPointTemplateParams);
        String replaceStr = ReplaceStr(templateContent, spliteStr, spliteRepliceStr, styleFileds);
        return replaceStr;
    }


    /**
     * 替换${}里的值，属性名和${}一致
     * @param templateContent   模版
     * @param alarmEscortTemplateParams    替换模版中参数的对象
     * @param styleFileds       需要处理增加颜色的参数几个（只有邮件支持，因为邮件发送内容是以html，所有支持样式），但是如果是邮件但是以文本发送的，就不能增加样式啦
     * @return
     */
    public static String replaceAlarmEscortStr(String templateContent, AlarmEscortTemplateParams alarmEscortTemplateParams, List<String> styleFileds) {
        String spliteStr = getContentInfo(templateContent);
        String spliteRepliceStr = checkAlarmEscort(spliteStr, alarmEscortTemplateParams);
        String replaceStr = ReplaceStr(templateContent, spliteStr, spliteRepliceStr, styleFileds);
        return replaceStr;
    }



    /**
     * 查找${}中的参数
     * @param content
     * @return
     */
    public static String getContentInfo(String content) {
        Matcher matcher = regex.matcher(content);
        StringBuilder sql = new StringBuilder();
        while (matcher.find()) {
            sql.append(matcher.group(1) + ",");
        }
        if (sql.length() > 0) {
            sql.deleteCharAt(sql.length() - 1);
        }
        return sql.toString();
    }

    /**
     * 从对象里获取模版参数的值
     * @param spliteStr
     * @param alarmPointTemplateParams
     * @return
     */
    private static String checkAlarmPoint(String spliteStr, AlarmPointTemplateParams alarmPointTemplateParams) {
        String[] strings = spliteStr.split(",");
        StringBuffer stringBuffer = new StringBuffer();
        for (String string : strings) {

            Field[] fields = alarmPointTemplateParams.getClass().getDeclaredFields();
            for (Field field : fields) {
                String fieldName = field.getName();
                // 设置私有属性的访问权限
                field.setAccessible(true);

                // 获取属性类型
                Class<?> type = field.getType();
                String typeName = field.getType().getName();
                String typeSimpleName = field.getType().getSimpleName();

                Object fieldValue = null;
                try {
                    // 获取属性类型值
                    fieldValue = field.get(alarmPointTemplateParams);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

                if(string.equals(fieldName)){
                    stringBuffer.append(StringUtils.format(fieldValue) + ",");
                }
            }
        }
        //替换的数据 以,分割
        String nowStr = stringBuffer.toString();
        return nowStr;
    }



    /**
     * 从对象里获取模版参数的值
     * @param spliteStr
     * @param alarmEscortTemplateParams
     * @return
     */
    private static String checkAlarmEscort(String spliteStr,  AlarmEscortTemplateParams alarmEscortTemplateParams) {
        String[] strings = spliteStr.split(",");
        StringBuffer stringBuffer = new StringBuffer();
        for (String string : strings) {

            Field[] fields = alarmEscortTemplateParams.getClass().getDeclaredFields();
            for (Field field : fields) {
                String fieldName = field.getName();
                // 设置私有属性的访问权限
                field.setAccessible(true);

                // 获取属性类型
                Class<?> type = field.getType();
                String typeName = field.getType().getName();
                String typeSimpleName = field.getType().getSimpleName();

                Object fieldValue = null;
                try {
                    // 获取属性类型值
                    fieldValue = field.get(alarmEscortTemplateParams);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

                if(string.equals(fieldName)){
                    stringBuffer.append(StringUtils.format(fieldValue) + ",");
                }
            }
        }
        //替换的数据 以,分割
        String nowStr = stringBuffer.toString();
        return nowStr;
    }



    /**
     * 替换${}符号中的数据
     *
     * @param str   例如:欢迎用户:${user},您的密码${password}
     * @param param user,password
     * @param data  阿呆,123456
     * @return 替换后的字符串
     */
    private static String ReplaceStr(String str, String param, String data, List<String> styleFileds) {
        //将要替换的参数转成数组
        String[] params = param.split(",");
        String[] datas = data.split(",",-1);
        //替换原字符串
        for (int i = 0; i < params.length; i++) {
            if(styleFileds != null && styleFileds.contains(params[i])){//特殊处理的字段，标红
                str = str.replace("${" + params[i] + "}", "<span style=\"color: red;\">"+datas[i]+"</span>");
            }else{
                str = str.replace("${" + params[i] + "}", datas[i]);
            }
        }
        return str;
    }










    /**
     * 模拟取出数据
     * @param spliteStr
     * @return 阿呆,123456
     */
    private static String checkStr(String spliteStr) {
        String[] strings = spliteStr.split(",");
        StringBuffer stringBuffer = new StringBuffer();
        for (String string : strings) {
            switch (string) {
                case "user":
                    String username = "阿呆";
                    stringBuffer.append(username + ",");
                    break;
                case "password":
                    String password = "123456";
                    stringBuffer.append(password + ",");
                    break;
            }

        }
        //替换的数据 以,分割
        String nowStr = stringBuffer.toString();
        return nowStr;
    }




    public static void main(String[] args) {
        //1.获取模板
        String str = "欢迎用户:${user},您的密码${password}";
        System.out.println("替换前的字符:" + str);
        //2.截取关键字符 ${user} ${password}
        String spliteStr = getContentInfo(str);
        //3.查找准备进行替换的参数 阿呆,123456
        String spliteRepliceStr = checkStr(spliteStr);
        //4.替换模板中的内容
        String replaceStr = ReplaceStr(str, spliteStr, spliteRepliceStr, null);
        System.out.println("替换后的字符:" + replaceStr);

    }
}

