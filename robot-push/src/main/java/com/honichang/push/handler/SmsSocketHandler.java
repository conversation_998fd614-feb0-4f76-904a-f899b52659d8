package com.honichang.push.handler;

import com.honichang.common.core.domain.entity.SysDictData;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.DictUtils;
import com.honichang.push.domain.PushResult;
import com.honichang.push.service.PushResultService;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.util.concurrent.GlobalEventExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.List;


@Slf4j
@ChannelHandler.Sharable
@Component
public class SmsSocketHandler extends ChannelInboundHandlerAdapter {
    public static final ChannelGroup clients = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);

    @Autowired
    private PushResultService pushResultService;


    /**
     * 网口发送短信(不记录发送日志)
     * @param mobile
     * @param content
     * @return
     */
    public boolean sendSimpleMessage(String mobile, String content) {

        boolean isTele = true;

        boolean flag = false;
        String msg;
        msg = (isTele ? "86" : "") + mobile + ":0:" + content + "";//是否为电信，如果是电信，前缀需要加上86
        log.info("msg:{}", msg);

        for (Channel client : clients) {
            log.info("发送数据给客户端,ID：{},内容：{}",client.id().toString(),msg);
            try {
                client.writeAndFlush(msg.getBytes("GBK"));
                flag = true;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return flag;

    }

    /**
     * 网口发送短信(记录发送日志)
     * @param pushResult
     */
    public void sendMessage(PushResult pushResult) {

        //发送短信
//        String mobile = pushResult.getMobile();
        String mobile = pushResult.getReceiver();
        String content = pushResult.getContent();

        boolean isTele = false;

        List<SysDictData> datas = DictUtils.getDictCache("sms_config");
        if(datas != null ){
            SysDictData dictSerialPort = datas.stream().filter(b -> b.getDictLabel().equals("serial_port")).findAny().orElse(null);
            SysDictData dictIsTele = datas.stream().filter(b -> b.getDictLabel().equals("is_tele")).findAny().orElse(null);

            if(dictSerialPort != null && dictIsTele != null){

                isTele = dictIsTele.getDictValue().equals("1") ? true :false;//0否1是

            }
        }

        boolean flag = false;
        String msg;
        msg = (isTele ? "86" : "") + mobile + ":0:" + content + "";//是否为电信，如果是电信，前缀需要加上86
        log.info("msg:{}", msg);

        for (Channel client : clients) {
            log.info("发送数据给客户端,ID：{},内容：{}",client.id().toString(),msg);
            try {
                client.writeAndFlush(msg.getBytes("GBK"));
                flag = true;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }

        if(flag){
            pushResult.setStatus("0");
        }else {
            pushResult.setStatus("1");
        }
        pushResult.setCreateTime(DateUtils.getNowDate());
        pushResultService.save(pushResult);

    }

    /**
     * 读取到客户端发来的消息
     *
     * @param ctx ChannelHandlerContext
     * @param msg msg
     * @throws Exception e
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        String data =new String((byte[]) msg,  "GBK");
        log.info("收到客户端消息==================================================================：" + data);
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        log.info("新的客户端链接==================================================================：" + ctx.channel().id().asShortText());
        clients.add(ctx.channel());
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        log.info("客户端退出链接==================================================================：" + ctx.channel().id().asShortText());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.info("客户端异常退出链接==================================================================：" + ctx.channel().id().asShortText());
    }
}
