package com.honichang.push.handler;

import com.honichang.common.core.domain.entity.SysDictData;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.DictUtils;
import com.honichang.push.domain.PushResult;
import com.honichang.push.service.PushResultService;
import gnu.io.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Enumeration;
import java.util.List;
import java.util.TooManyListenersException;

@Slf4j
@Service
public class SmsCommHandler implements SerialPortEventListener{

    @Autowired
    private PushResultService pushResultService;



    static CommPortIdentifier portId;
    static Enumeration portList;
    InputStream inputStream;
    OutputStream outputStream;
    SerialPort serialPort;

    public class zlyInt                //查询字符流的函数需要获取输出参数的办法
    {
        int i = 0;
    }

    SmsCommHandler.zlyInt iHead = new SmsCommHandler.zlyInt();         //查找包头
    SmsCommHandler.zlyInt iTail = new SmsCommHandler.zlyInt();         //查找包尾
    SmsCommHandler.zlyInt iSucess = new SmsCommHandler.zlyInt();       //查找是否发送成功
    SmsCommHandler.zlyInt iFail = new SmsCommHandler.zlyInt();         //查找是否发送失败

    byte[] rcvHead = new byte[5];        //包头 "+CMS:";
    byte[] rcvTail = new byte[4];        //包尾   "\r\n\0\0"
    byte[] rcvSucess = new byte[15];     //"SMS_SEND_SUCESS"
    byte[] rcvFail = new byte[13];       //"SMS_SEND_FAIL"

    int bHeadFinded;                     //接收使用
    byte[] bufSms = new byte[500];       //接收缓冲区
    int ibufSms;                         //接收缓冲区中，当前收到的字符数

    boolean isTele;

    public void sendMessage(PushResult pushResult) {

        if (outputStream == null) {
            List<SysDictData> datas = DictUtils.getDictCache("sms_config");
            if(datas != null ){
                SysDictData dictSerialPort = datas.stream().filter(b -> b.getDictLabel().equals("serial_port")).findAny().orElse(null);
                SysDictData dictIsTele = datas.stream().filter(b -> b.getDictLabel().equals("is_tele")).findAny().orElse(null);

                if(dictSerialPort != null && dictIsTele != null){

                    isTele = dictIsTele.getDictValue().equals("1") ? true :false;//0否1是

                    String portIdName = dictSerialPort.getDictValue();
                    jButton1ActionPerformed(portIdName);
                }else{//未配置不发送
                    return;
                }
            }
        }
        //发送短信
//        String mobile = pushResult.getMobile();
        String mobile = pushResult.getReceiver();
        String content = pushResult.getContent();
        boolean flag = jButton2ActionPerformed(mobile, content);
        if(flag){
            pushResult.setStatus("0");
        }else {
            pushResult.setStatus("1");
        }
        pushResult.setCreateTime(DateUtils.getNowDate());
        pushResultService.save(pushResult);
    }

    public void sendSimpleMessage(String mobile, String content) {

        if (outputStream == null) {
            List<SysDictData> datas = DictUtils.getDictCache("sms_config");
            if(datas != null ){
                SysDictData dictSerialPort = datas.stream().filter(b -> b.getDictLabel().equals("serial_port")).findAny().orElse(null);
                SysDictData dictIsTele = datas.stream().filter(b -> b.getDictLabel().equals("is_tele")).findAny().orElse(null);

                if(dictSerialPort != null && dictIsTele != null){

                    isTele = dictIsTele.getDictValue().equals("1") ? true :false;//0否1是

                    String portIdName = dictSerialPort.getDictValue();
                    jButton1ActionPerformed(portIdName);
                }else{//未配置不发送
                    return;
                }
            }
        }
        //发送短信
        jButton2ActionPerformed(mobile, content);

    }



    /**
     * 开始串口
     * @param portIdName
     */
    private void jButton1ActionPerformed(String portIdName) {//GEN-FIRST:event_jButton1ActionPerformed

        log.info(System.getProperty("java.library.path"));

        log.info("portIdName:{}", portIdName);

        // TODO add your handling code here:
        portList = CommPortIdentifier.getPortIdentifiers();
        while (portList.hasMoreElements()) {
            portId = (CommPortIdentifier) portList.nextElement();
            if (portId.getPortType() == CommPortIdentifier.PORT_SERIAL) {
                log.info("portId.getName():" + portId.getName());
                if (portId.getName().equals(portIdName)) {
                    try {
                        log.info("serialPort开始时间:{}", DateUtils.getTime());
                        serialPort = (SerialPort) portId.open("sunder", 2000);
                        log.info("serialPort结束:{}", DateUtils.getTime());

                    } catch (PortInUseException e) {
                        log.error(e.getMessage());
                    }
                    try {
                        inputStream = serialPort.getInputStream();
                        outputStream = serialPort.getOutputStream();
                    } catch (IOException e) {
                        log.error(e.getMessage());
                    }

                    try {
                        serialPort.addEventListener((SerialPortEventListener) this);
                    } catch (TooManyListenersException e) {
                        log.error(e.getMessage());
                    }

                    serialPort.notifyOnDataAvailable(true);

                    try {
                        serialPort.setSerialPortParams(9600, SerialPort.DATABITS_8, SerialPort.STOPBITS_1, SerialPort.PARITY_NONE);
                    } catch (UnsupportedCommOperationException e) {
                        log.error(e.getMessage());
                    }
                    log.info("portIdName5:{}", portIdName);

                    log.info(portIdName + "  start!");
                }
            }
        }
    }

    /**
     *  串口发送内容
     * @param mobile
     * @param content
     */
    private boolean jButton2ActionPerformed(String mobile, String content) {//GEN-FIRST:event_jButton2ActionPerformed
        boolean flag = false;
        String s1;
        s1 = (isTele ? "86" : "") + mobile + ":0:" + content + "";//是否为电信，如果是电信，前缀需要加上86
        log.info("s1:{}", s1);
        try {
            outputStream.write(s1.getBytes("GBK"), 0, s1.getBytes("GBK").length);
            outputStream.flush();
            flag = true;
        } catch (IOException ex) {
            //Logger.getLogger(javaapplication1.NewJFrame.class.getName()).log(Level.SEVERE, null, ex);
        }
        return flag;
    }


    //查询字符流的函数
    public int SearchStrInStream2(byte[] strToCmp, byte c, SmsCommHandler.zlyInt i) {
        if (i.i > strToCmp.length) {
            i.i = 0;
            return 0;
        }
        if (i.i != 0) {
            if (c != strToCmp[i.i]) {
                i.i = 0;
            } else {
            }
        }
        if (c == strToCmp[i.i]) {
            (i.i)++;
        }
        if ((int) (i.i) == strToCmp.length) {

            i.i = 0;
            return 1;
        }
        return 0;
    }

    @Override
    public void serialEvent(SerialPortEvent event) {

        switch (event.getEventType()) {
            case SerialPortEvent.BI:
            case SerialPortEvent.OE:
            case SerialPortEvent.FE:
            case SerialPortEvent.PE:
            case SerialPortEvent.CD:
            case SerialPortEvent.CTS:
            case SerialPortEvent.DSR:
            case SerialPortEvent.RI:
            case SerialPortEvent.OUTPUT_BUFFER_EMPTY:
                break;
            case SerialPortEvent.DATA_AVAILABLE:
                byte[] readBuffer = new byte[200];
                byte[] outBuffer = new byte[200];
                int numBytes = 0;
                int j;
                String s;
                try {
                    while (inputStream.available() > 0) {
                        numBytes = inputStream.read(readBuffer);
                        for (j = 0; j < numBytes; j++) {
                            if (1 == SearchStrInStream2(rcvSucess, readBuffer[j], iSucess))
//                                log.info("发送成功");
                                if (1 == SearchStrInStream2(rcvFail, readBuffer[j], iFail))
//                                log.info("发送失败");
                                    if (bHeadFinded == 1) {
                                        bufSms[ibufSms] = readBuffer[j];
                                        ibufSms++;
                                        if (1 == SearchStrInStream2(rcvTail, readBuffer[j], iTail)) {
//                                    jTextArea1.setText(jTextArea1.getText()+"接收完成\n");
                                            bHeadFinded = 0;
                                            s = new String(bufSms, 0, ibufSms, "gbk");
                                            bufSms = new byte[500];
//                                    jTextArea1.setText(jTextArea1.getText()+"收到短信："+s+"\n");
                                        }
                                    }
                            if (1 == SearchStrInStream2(rcvHead, readBuffer[j], iHead)) {
                                bHeadFinded = 1;
                                ibufSms = 0;
//                                jTextArea1.setText(jTextArea1.getText()+"找到包头\n");
                            }
                        }
                    }
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
                break;
        }

    }
}
