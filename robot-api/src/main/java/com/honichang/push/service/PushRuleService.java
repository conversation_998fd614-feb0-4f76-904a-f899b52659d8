package com.honichang.push.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.push.domain.PushRule;

import java.util.List;

/**
 *
 */
public interface PushRuleService extends IService<PushRule> {

    /**
     * 列表
     * @param pushRule
     * @return
     */
    List<PushRule> selectPageList(PushRule pushRule);

    /**
     * 新增
     * @param pushRule
     */
    void add(PushRule pushRule);

    /**
     * 编辑
     * @param pushRule
     */
    void update(PushRule pushRule);
    /**
     * 删除
     * @param id
     */
    void delete (Long id);


    /**
     * 匹配，点位报警的规则
     * @param bizPointId    点位id
     * @param alarmLevel    报警等级
     * @return
     */
    List<PushRule> getAlarmPointRule(String bizPointId, String alarmLevel);



    /**
     * 匹配，陪同报警规则，因为陪同报警和规则关联不上，所以不加条件，获取所有陪同报警规则
     * @return
     */
    List<PushRule> getAlarmEscortRule();


}
