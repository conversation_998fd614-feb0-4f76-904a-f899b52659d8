package com.honichang.push.service;

import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.domain.TaskInstanceNode;

public interface PushMessageService {

    /**
     *  点位报警消息推送
     * @param taskInstanceNode
     */
    void sendAlarmPointMessage(TaskInstanceNode taskInstanceNode);



    /**
     *  陪同报警消息推送
     * @param alarmEscort
     */
    void sendAlarmEscortMessage(AlarmEscort alarmEscort);
}
