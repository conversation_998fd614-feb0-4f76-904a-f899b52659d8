package com.honichang.netty.enums;

import lombok.Getter;

/**
 * 工控枚举
 */
@Getter
public enum IndustrialEnum {

    GetRobotInformation("GetRobotInformation", "获取机器人基本信息"),
    FirmwareUpdate("FirmwareUpdate", "下发固件更新"),
    FirmwareUpdateConfirm("FirmwareUpdateConfirm", "更新固件"),
    MusicUpdate("MusicUpdate", "更新语音音频文件"),
    MusicPlay("MusicPlay", "播放预定义音频"),
    MusicPause("MusicPause", "停止预定义音频"),
    RestartRobot("RestartRobot", "重启机器人"),
    TaskDistribution("TaskDistribution", "批任务下发"),
    CacelTask("CacelTask", "取消当前导航任务"),
    GetRobotStatus("GetRobotStatus", "获取机器人运行状态"),
    GetRobotVariable("GetRobotVariable", "读取多个变量值"),
    SetRobotVariable("SetRobotVariable", "写入多个变量值"),
    SetRobotMode("SetRobotMode", "手自动切换"),
    SetRobotPosition("SetRobotPosition", "机器人手动定位"),
    ConfirmRobotPosition("ConfirmRobotPosition", "确认机器人位置"),
    PauseOrResumeOrAbortNavigation("PauseOrResumeOrAbortNavigation", "暂停/继续/取消导航(Pause/Resume/Abort)"),
    SetOccupiedPath("SetOccupiedPath", "通知交通管理资源占据结果"),
    StartNavigationDirection("StartNavigationDirection", "开始导航（指定路径和指定路径方向）"),
    OpenAndCloseChargingPort("OpenAndCloseChargingPort", "充电"),
    GimbalReset("GimbalReset", "云台复位"),
    TelescopicPole("TelescopicPole", "伸缩杆控制");

    /**
     * 指令
     * -- GETTER --
     * 获取指令
     */
    private final String instruction;
    /**
     * 注释
     * -- GETTER --
     * 获取注释
     */
    private final String description;

    // 构造函数
    IndustrialEnum(String instruction, String note) {
        this.instruction = instruction;
        this.description = note;
    }

}
