package com.honichang.netty.enums;

import lombok.Getter;

@Getter
public enum UploadEnum {
    LogUpload("LogUpload", "工控日志上传"),
    FirmwareUpdateStatus("FirmwareUpdateStatus", "固件更新后通知服务端是否成功"),
    HardwareWarning("HardwareWarning", "硬件报警内容上传"),
    RealTimeData("RealTimeData", "实时数据上传（包括车辆状态等）"),
    TaskUploadResults("TaskUploadResults", "任务点位结果推送上传"),
    GetBaseDistance("GetBaseDistance", "获取定位卡与基站之间的距离"),
    UwbCardIn("UwbCardIn", "UWB卡签到");
    /**
     * 指令
     */
    private final String instruction;
    /**
     * 注释
     */
    private final String description;

    // 构造函数
    UploadEnum(String instruction, String note) {
        this.instruction = instruction;
        this.description = note;
    }

}
