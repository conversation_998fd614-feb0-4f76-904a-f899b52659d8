package com.honichang.netty.service;

import com.honichang.netty.domain.IndustrialLog;
import com.honichang.netty.domain.ResponseMessage;
import com.honichang.netty.domain.RobotConnectionLog;
import org.springframework.scheduling.annotation.Async;

/**
 * 工控接口LOG
 */
public interface IndustrialLogService {

    /**
     * 接收接口log
     *
     * @param industrialLog
     */
    void saveServiceLog(IndustrialLog industrialLog);


    void saveServiceLog(ResponseMessage responseMessage);

    /**
     * 接收设备log
     */
    void saveRobotLog(RobotConnectionLog robotConnectionLog);
}
