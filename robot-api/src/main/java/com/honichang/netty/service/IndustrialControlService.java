package com.honichang.netty.service;

import com.honichang.netty.domain.IndustrialControlResult;

/**
 * 工控部分接口
 * 接口均采用异步回调方式处理
 */
public interface IndustrialControlService {


    /**
     * 添加/变更设备
     *
     * @param robotId 机器人id
     * @param ip ip
     * @param port 端口
     */
    void robotConnection(Long robotId, String ip, int port);

    /**
     * 删除设备
     *
     * @param robotId
     */
    void robotRemove(Long robotId);

    /**
     * 固件下发
     *
     * @param robotId 机器人id
     * @param filePath 文件绝对路径
     * @return IndustrialControlResut 
     */
    IndustrialControlResult firmwareUpdate(Long robotId, String  filePath);

    /**
     * 固件更新
     *
     * @param robotId 机器人id
     * @param params 参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult firmwareUpdateConfirm(Long robotId, String  params);
    /**
     * 更新语音音频文件
     * @param robotId 机器人id
     * @param  fileId 文件id
     * @param filePath 文件绝对路径
     * @return IndustrialControlResut
     */
    IndustrialControlResult musicUpdate(Long robotId,Long fileId, String filePath);

    /**
     * 播放预定义音频
     * @param robotId 机器人id
     * @param params 自定义参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult musicPlay(Long robotId, String params);

    /**
     * 停止预定义音频
     * @param robotId 机器人id
     * @param fileId 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult musicPause(Long robotId, Long fileId);

    /**
     * 重启机器人
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult restartRobot(Long robotId, String params);

    /**
     * 批任务下发
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult taskDistribution(Long robotId, String params);

//    /**
//     * 任务点位结果推送上传
//     * @param robotId 机器人id
//     * @param params 字符串参数
//     * @return IndustrialControlResut
//     */
//    IndustrialControlResult taskUploadResults(Long robotId, String params);

    /**
     * 取消当前导航任务
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult cacelTask(Long robotId, String params);

    /**
     * 获取机器人运行状态
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult getRobotStatus(Long robotId, String params);

    /**
     * 读取多个变量值
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult getRobotVariable(Long robotId, String params);

    /**
     * 写入多个变量值
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult setRobotVariable(Long robotId, String params);

    /**
     * 手自动切换
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult setRobotMode(Long robotId, String params);

    /**
     * 机器人手动定位
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult setRobotPosition(Long robotId, String params);

    /**
     * 确认机器人位置
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult confirmRobotPosition(Long robotId, String params);

    /**
     * 暂停导航
     * @param robotId 机器人id
     * @return IndustrialControlResut
     */
    IndustrialControlResult pausetNavigation(Long robotId);

    /**
     * 继续导航
     * @param robotId 机器人id
     * @return IndustrialControlResut
     */
    IndustrialControlResult resumeNavigation(Long robotId);

    /**
     * 取消导航
     * @param robotId 机器人id
     * @return IndustrialControlResut
     */
    IndustrialControlResult abortNavigation(Long robotId);


    /**
     * 开始导航（指定路径和指定路径方向）
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult startNavigationDirection(Long robotId, String params);

    /**
     * 充电
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult openAndCloseChargingPort(Long robotId, String params);

    /**
     * 云台复位
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult gimbalReset(Long robotId, String params);

    /**
     * 伸缩杆控制
     * @param robotId 机器人id
     * @param params 字符串参数
     * @return IndustrialControlResut
     */
    IndustrialControlResult telescopicPole(Long robotId, String params);



}
