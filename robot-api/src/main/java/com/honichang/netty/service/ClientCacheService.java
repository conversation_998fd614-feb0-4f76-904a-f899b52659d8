package com.honichang.netty.service;

import com.honichang.netty.domain.MessageCache;

import java.util.Date;

/**
 * 消息数据缓存
 */
public interface ClientCacheService {

    /**
     * 获取消息
     *
     * @param robotId   机器人id
     * @param commandId 消息key
     * @param channelId channelId
     * @return
     */
    MessageCache getMessage(Long robotId, Long commandId, String channelId);

    /**
     * 添加消息数据缓存
     * @param messageCache
     */
    void addMessage(MessageCache messageCache);

    void removeMessage(MessageCache messageCache);

    void updateAckTime(String messageId, Date ackTime);

    void updateResultTime(String messageId, Date resultTime);
}
