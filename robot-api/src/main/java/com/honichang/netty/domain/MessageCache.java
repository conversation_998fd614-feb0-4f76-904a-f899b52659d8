package com.honichang.netty.domain;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * 消息缓存
 **/

@Data
public class MessageCache implements Serializable {
    /**
     * 通道id
     **/
    private String channelId;
    /***消息唯一标识***/
    private Long commandId;
    /***消息唯一标识***/
    private String commandName;
    /***机器人id***/
    private Long robotId;
    /***参数***/
    private String params;
    /****创建时间***/
    private Date createTime;
    /****ack时间***/
    private Date ackTime;
    /****返回时间***/
    private Date resultTime;
    /****异步Future***/
    private CompletableFuture<ResponseMessage> responseFuture;

    public MessageCache(Long robotId,Long commandId,String channelId,String commandName,String params,CompletableFuture<ResponseMessage> responseFuture){
        this.robotId = robotId;
        this.commandId = commandId;
        this.channelId = channelId;
        this.commandName = commandName;
        this.params = params;
        this.responseFuture = responseFuture;
    }


    /****获取消息id***/
    public String getMessageId() {
        return "rId" + robotId + "cmId" + commandId + "chId" + channelId;
    }

}
