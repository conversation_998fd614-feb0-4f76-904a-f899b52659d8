package com.honichang.machine.domain;

import com.honichang.common.core.page.TableDataInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class HistoryTableDto extends TableDataInfo implements Serializable {

    //头
    private String[] tableHeader;
    //主题列表<Map<String,Object>>
    private List<Object> list = new ArrayList<>();


}
