package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.common.core.domain.entity.SysRole;
import com.honichang.point.domain.IdenTypeParam;

import java.util.List;

/**
 *
 */
public interface IdenTypeParamService extends IService<IdenTypeParam> {
    /**
     * 根据条件分页查询识别类型参数数据
     *
     * @param idenTypeParam 识别类型参数信息
     * @return 参数数据集合信息
     */
    List<IdenTypeParam> selectParamList(IdenTypeParam idenTypeParam);

    /**
     * 编辑设别类型参数
     *
     * @param idenTypeParam 识别类型参数信息
     * @return
     */
    void add(IdenTypeParam idenTypeParam);

    /**
     * 编辑设别类型参数
     *
     * @param idenTypeParam 识别类型参数信息
     * @return
     */
    void update(IdenTypeParam idenTypeParam);

    /**
     * 删除设别类型参数
     *
     * @param id 识别类型参数信息
     * @return
     */
    void delete(Long id);


    /**
     * 根据识别类型删除设别类型参数
     *
     * @param idenTypeId 识别类型id
     * @return
     */
    void deleteByIdenTypeId(Long idenTypeId);


    /**
     * 编辑设别类型参数状态
     *
     * @param idenTypeParam 识别类型参数信息
     * @return
     */
    void updateParamStatus(IdenTypeParam idenTypeParam);
}
