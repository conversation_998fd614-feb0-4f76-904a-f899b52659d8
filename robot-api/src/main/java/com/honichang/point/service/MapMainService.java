package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.MapMain;
import com.honichang.point.model.vo.MapResponseBase;

/**
 *
 */
public interface MapMainService extends IService<MapMain> {

    void updateNameAndScale(Long id, String mapName, Double scale);

    /**
     * 获取最新改动的一张地图
     */
    MapMain getLatest();

    /**
     * 生成合并后的SVG文件路径，带随机数防止缓存
     * @param mapId 地图ID
     * @return 合并后的SVG文件路径<br/>
     * <code>http://[host]:[port]/profile/map/merge_[mapId].svg?v=[tm]</code>
     */
    String generateMergeSvgPathWithRandom(long mapId);

    /**
     * 加载点位和路径
     * @param response 响应对象
     */
    <T extends MapResponseBase> void loadPointsAndPaths(T response);

    /**
     * 加载障碍点
     * @param response 响应对象
     */
    <T extends MapResponseBase> void loadObstacles(T response);
}
