package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum;
import com.honichang.point.domain.TaskEscort;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface TaskEscortService extends IService<TaskEscort> {

    /**
     * 检查机器人在N小时内是否有陪同任务
     * @param robotId 机器人id
     * @param hours  小时数
     * @return true-有陪同任务，false-没有陪同任务
     */
    boolean checkRobotHasEscortInHours(@NonNull Long robotId, int hours);

    /**
     * 获取机器人正在执行的陪同任务（当天只能有一个，完成的不算）<br/>
     * 考虑陪同人员可能凌晨工作，这里条件多计算一天
     *
     * @return 正在执行的陪同任务
     */
    List<TaskEscort> getRunningEscortTasks();

    /**
     * 获取当日等待执行的陪同任务
     * @return 等待执行的陪同任务
     */
    List<TaskEscort> getPendingEscortTasks();

    /**
     * 获取机器人正在执行或未开始的陪同任务（当天只能有一个，完成的不算）
     * @param robotId 机器人id
     * @param date yyyy-MM-dd
     * @return 正在执行或未开始的陪同任务
     */
    TaskEscort getPendingOrRunningEscortTasks(long robotId, String date);

    /**
     * 由调度触发的终止陪同任务
     * 1.UWB距离过远且超时
     * 2.等待访客超时
     * 3.人工切换手动模式
     *
     * @param robotId      机器人id
     * @param reason       终止原因
     * @param pushTemplate 推送消息模板，即终止原因
     */
    void abortBySchedule(long robotId, String reason, TaskEscortPushTemplateEnum pushTemplate);

    /**
     * 手动终止陪同任务
     */
    void abortByTaskEscortId(long taskEscortId, String reason);

    /**
     * 检查当天是否有时间冲突
     * @param taskEscort 条件：新建时id=null，修改时需要排除自身
     * @return true-有时间冲突，false-没有时间冲突
     */
    boolean checkTimeConflict(TaskEscort taskEscort);

    /**
     * 手工触发立即执行
     * 开始陪同任务，前往接人点<p/>
     * 操作：<br/>
     * 1. 检查机器人状态<br/>
     * 2. 如果处于充电中，且电量大于50%，则停止充电<br/>
     * 3. 停止机器人当前普通任务<br/>
     * 4. 清空RobotContext中的队列（仅保留任务队列）<br/>
     * 5. 更新陪同任务状态即开始时间并save<br/>
     * 6. 写日志<br/>
     * 失败的可能：<br/>
     * 1. 机器人处于充电中，且电量小于50%<br/>
     * 2. 机器人状态非法<br/>
     *
     * @param robotId           机器人ID
     * @param taskEscort 正在执行的陪同任务
     */
    void startToVisit(long robotId, TaskEscort taskEscort);

    /**
     * 规划路径，并下发导航指令<br/>
     * 【重要】陪同期间一直是正走！
     *
     * @param robotId 机器人id
     * @param taskEscortId 陪同任务id
     * @param p 目标点位
     * @param alwaysForward 陪同期间一直是正走！
     * @param avoidVisitor 是否避开身后的访客
     * @return true需要走，false就在脚下
     */
    boolean planPathAndNavigation(long robotId, long taskEscortId, Position p, boolean alwaysForward, boolean avoidVisitor);

    /**
     * 开始陪同任务：可能是去充电
     * 由调度中心自动触发
     * @param toCharge 是否去充电
     */
    void startToVisit(long robotId, TaskEscort taskEscort, boolean toCharge);

    /**
     * 访客抵达，去目标地点
     * @return true需要走，false就在脚下
     */
    boolean escortConfirmVisit(Long robotId, Long taskEscortId);

    /**
     * 机器人到达等待点
     */
    void arriveWaitPoint(Long taskEscortId);

    /**
     * 访客完成任务，送访客到出口
     * <p>
     * 401=>402
     */
    void arriveExitPoint(Long taskEscortId);

    /**
     * 更新陪同任务
     * @param te 陪同任务
     */
    void updateTaskEscort(TaskEscort te);

    /**
     * 删除陪同任务
     * @param taskEscort 陪同任务
     */
    void removeTaskEscort(TaskEscort taskEscort);

    /**
     * 机器人到达目标点
     */
    void arriveTargetPoint(Long taskEscortId);

    /**
     * 恢复报警
     */
    void restoreAlarm(TaskEscort taskEscort);

    /**
     * 完成离开，任务结束
     * @param taskEscort 陪同任务
     */
    void completeLeave(TaskEscort taskEscort);

    /**
     * 访客完成任务
     * 触摸了按钮
     */
    boolean visitorComplete(TaskEscort taskEscort);

}
