package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.BizPointIdenConf;
import com.honichang.point.domain.IdenModelParam;

import java.util.List;

/**
 *
 */
public interface IdenModelParamService extends IService<IdenModelParam> {

    /**
     * 根据条件分页查询识别模型参数数据
     *
     * @param idenModelParam 识别模型参数信息
     * @return 参数数据集合信息
     */
    List<IdenModelParam> selectParamList(IdenModelParam idenModelParam);

    /**
     * 编辑设别模型参数
     *
     * @param idenModelParam 识别模型参数信息
     * @return
     */
    void add(IdenModelParam idenModelParam);

    /**
     * 编辑设别模型参数
     *
     * @param idenModelParam 识别模型参数信息
     * @return
     */
    void update(IdenModelParam idenModelParam);

    /**
     * 删除设别模型参数
     *
     * @param id 识别模型参数信息
     * @return
     */
    void delete(Long id);


    /**
     * 根据识别模型删除设别模型参数
     *
     * @param idenModelId 识别模型id
     * @return
     */
    void deleteByIdenModelId(Long idenModelId);


    /**
     * 根据识别参数组id删除设别模型参数
     *
     * @param idenModelParamGroupId 识别参数组id
     * @return
     */
    void deleteByParamGroupId(Long idenModelParamGroupId);


    /**
     * 编辑设别模型参数状态
     *
     * @param idenModelParam 识别模型参数信息
     * @return
     */
    void updateParamStatus(IdenModelParam idenModelParam);

    /**
     * 根据识别配置获取识别参数，如果是索引类型的参数会返回多个
     * @param conf 识别配置
     * @return 识别参数
     */
    List<IdenModelParam> selectParamListByIdenConf(BizPointIdenConf conf);
}
