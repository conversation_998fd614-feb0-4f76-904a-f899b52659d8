package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.machine.domain.HistoryDto;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.model.dto.TaskInstanceNodeDto;
import com.honichang.point.model.vo.TaskInstanceNodeVo;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface TaskInstanceNodeService extends IService<TaskInstanceNode> {

    /**
     * 根据任务定义创建空的任务实例节点<br/>
     * 只创建，不保存DB<br/>
     * 主键的id也没有关联
     *
     * @param taskDefId 任务定义id
     * @param mapId     任务实例
     * @return 任务实例节点列表
     */
    List<TaskInstanceNode> createTaskNodesByDef(long taskDefId, long mapId);

    List<TaskInstanceNode> getHistory(HistoryDto historyDto);

    /**
     * 查询报警（历史报警+实时报警）
     * @param taskInstanceNodeDto
     * @return
     */
    List<TaskInstanceNodeVo> selectAlarmPageList(TaskInstanceNodeDto taskInstanceNodeDto);

    /**
     * 充血模型TaskInstanceNode
     * @param taskNode 任务点位
     * @param mapId    地图id
     * @return 任务点位校验失败是false，成功是true
     */
    boolean congestedAndCheck(@NonNull TaskInstanceNode taskNode, long mapId);

    /**
     * 根据任务实例获取任务实例节点
     * @param instance 任务实例
     * @return 任务实例节点列表（充血）
     */
    List<TaskInstanceNode> getListByTaskInstance(TaskInstance instance);

    /**
     * 合并属性，并保存：<p/>
     * 到达时间、离开时间、状态
     * @param taskNodes RobotContext.taskDirectiveChain中的缓存数据
     */
    void mergeAndReflush(@NonNull List<TaskInstanceNode> taskNodes);
}
