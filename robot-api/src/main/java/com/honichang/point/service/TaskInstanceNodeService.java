package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface TaskInstanceNodeService extends IService<TaskInstanceNode> {

    /**
     * 根据任务定义创建空的任务实例节点
     *
     * @param taskDefId 任务定义id
     * @param taskInstance 任务实例
     * @param taskDefNodes 任务节点定义
     * @return 任务实例节点列表
     */
    List<TaskInstanceNode> createTaskNodesByDef(long taskDefId, TaskInstance taskInstance, List<TaskDefNode> taskDefNodes);

    void congested(@NonNull TaskInstanceNode taskNode, @NonNull TaskInstance taskInstance);
}
