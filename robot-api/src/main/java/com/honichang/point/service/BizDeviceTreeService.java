package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.BizDeviceTree;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 *
 */
public interface BizDeviceTreeService extends IService<BizDeviceTree> {


    /**
     * 查询设备树管理数据
     *
     * @param bizDeviceTree 设备树信息
     * @return 设备树信息集合
     */
    List<BizDeviceTree> selectDeviceTreeList(BizDeviceTree bizDeviceTree);

    /**
     * 新增保存设备树信息
     *
     * @param bizDeviceTree 设备树信息
     * @return 结果
     */
    void insertDeviceTree(BizDeviceTree bizDeviceTree);

    /**
     * 修改保存设备树信息
     *
     * @param bizDeviceTree 设备树信息
     * @return 结果
     */
    void updateDeviceTree(BizDeviceTree bizDeviceTree);

    /**
     * 删除设备树管理信息
     *
     * @param id 设备树ID
     * @return 结果
     */
    void deleteDeviceTreeById(Long id);


    /**
     * 导入
     * @param file
     */
    void importFile(MultipartFile file);

}
