package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.RobotChargingPile;

import java.util.List;

/**
 *
 */
public interface RobotChargingPileService extends IService<RobotChargingPile> {

    /**
     * 根据条件分页查询充电桩数据
     *
     * @param robotChargingPile 充电桩信息
     * @return 充电桩数据集合信息
     */
    List<RobotChargingPile> selectPageList(RobotChargingPile robotChargingPile);

    /**
     * 编辑充电桩
     *
     * @param robotChargingPile 充电桩信息
     * @return
     */
    void add(RobotChargingPile robotChargingPile);

    /**
     * 编辑充电桩
     *
     * @param robotChargingPile 充电桩信息
     * @return
     */
    void update(RobotChargingPile robotChargingPile);

    /**
     * 删除充电桩
     *
     * @param id 充电桩信息
     * @return
     */
    void delete(Long id);

}
