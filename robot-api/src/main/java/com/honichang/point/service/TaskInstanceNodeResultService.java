package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.machine.domain.HistoryDto;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.domain.TaskInstanceNodeResult;

import java.util.List;

/**
 *
 */
public interface TaskInstanceNodeResultService extends IService<TaskInstanceNodeResult> {

    List<TaskInstanceNodeResult> getHistoryCurve(HistoryCurveDto historyCurveDto);



}
