package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.MapPath;

import java.util.List;

/**
 *
 */
public interface MapPathService extends IService<MapPath> {

    /**
     * 清空地图路径
     *
     * @param mapId 地图ID
     */
    void removeByMapId(Long mapId);

    /**
     * 根据地图ID获取路径信息
     *
     * @param mapId 地图ID
     * @return 路径信息
     */
    List<MapPath> getPathsByMapId(Long mapId);
}
