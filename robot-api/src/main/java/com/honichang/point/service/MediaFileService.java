package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.AlarmAi;
import com.honichang.point.domain.MediaFile;
import com.honichang.point.domain.RobotHardware;

/**
 *
 */
public interface MediaFileService extends IService<MediaFile> {

    /**
     * 保存AI实时报警图片
     *
     * @param entity   AI报警实体
     * @param picture  图片base64
     * @param hardware 机器人硬件信息
     */
    void saveMediaFile(AlarmAi entity, String picture, RobotHardware hardware);
}
