package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.AlarmAi;
import com.honichang.point.domain.dto.AbnormalReceptionParams;

import java.util.List;

/**
 *
 */
public interface AlarmAiService extends IService<AlarmAi> {


    /**
     * 根据条件分页查询智能报警
     *
     * @param alarmAi 智能报警参数信息
     * @return 智能报警数据集合信息
     */
    List<AlarmAi> selectPageList(AlarmAi alarmAi);

    /**
     * 确认
     * @param alarmAi
     */
    void confirm(AlarmAi alarmAi);

    /**
     * AI异常接收
     * @param abnormalReceptionParams 异常接收参数
     */
    void abnormalReception(AbnormalReceptionParams abnormalReceptionParams);
}
