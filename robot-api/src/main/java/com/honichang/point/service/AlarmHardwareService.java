package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.AlarmHardware;

import java.util.List;

/**
 *
 */
public interface AlarmHardwareService extends IService<AlarmHardware> {

    /**
     * 根据条件分页查询硬件报警
     *
     * @param alarmHardware 硬件报警参数信息
     * @return 硬件报警数据集合信息
     */
    List<AlarmHardware> selectPageList(AlarmHardware alarmHardware);
}
