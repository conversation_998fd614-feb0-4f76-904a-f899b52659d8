package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.IdenType;

import java.util.List;

/**
 *
 */
public interface IdenTypeService extends IService<IdenType> {


    /**
     * 查询识别类型树
     *
     * @param
     * @return 识别类型树
     */
    List<IdenType> getIdenTypeTree();


    /**
     * 新增设别类型
     *
     * @param idenType 识别类型信息
     * @return
     */
    void add(IdenType idenType);

    /**
     * 编辑设别类型
     *
     * @param idenType 识别类型信息
     * @return
     */
    void update(IdenType idenType);

    /**
     * 删除设别类型
     *
     * @param id 识别类型信息
     * @return
     */
    void delete(Long id);

}
