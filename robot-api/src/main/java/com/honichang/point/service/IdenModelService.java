package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.IdenModel;

import java.util.List;

/**
 *
 */
public interface IdenModelService extends IService<IdenModel> {

    /**
     * 查询识别模型树
     *
     * @param
     * @return 识别模型树
     */
    List<IdenModel> getIdenModelTree();

    /**
     * 新增识别模型
     *
     * @param idenModel 识别模型信息
     * @return
     */
    void add(IdenModel idenModel);

    /**
     * 编辑识别模型
     *
     * @param idenModel 识别模型信息
     * @return
     */
    void update(IdenModel idenModel);

    /**
     * 删除识别模型
     *
     * @param id 识别模型信息
     * @return
     */
    void delete(Long id);
}
