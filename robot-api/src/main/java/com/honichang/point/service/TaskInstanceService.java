package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface TaskInstanceService extends IService<TaskInstance> {

    boolean auditDataEdit(List<TaskInstanceNode> taskInstanceNode);

    /**
     * 根据任务定义创建任务实例
     *
     * @param taskDefId    任务定义id
     * @param taskDefNodes 任务节点定义
     * @param mapId        地图id
     */
    TaskInstance createTaskByDef(long taskDefId, @NonNull List<TaskDefNode> taskDefNodes, long mapId);
}
