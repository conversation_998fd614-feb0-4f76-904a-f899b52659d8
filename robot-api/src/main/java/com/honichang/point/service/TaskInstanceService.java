package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.dispactch.model.Task;
import com.honichang.dispactch.model.enums.TaskStatusEnum;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;

import java.util.List;

/**
 *
 */
public interface TaskInstanceService extends IService<TaskInstance> {

    boolean auditDataEdit(List<TaskInstanceNode> taskInstanceNode);

    /**
     * 根据任务定义创建任务实例<br/>
     * 只创建，不会保存DB<br/>
     * 初始化状态=暂停；开始时间=null
     *
     * @param taskDefId    任务定义id
     * @param mapId        地图id
     * @return 任务实例
     */
    TaskInstance createTaskByDef(long taskDefId, long mapId);

    List<TaskInstance> getList(TaskInstance taskInstance);

    /**
     * 更新任务执行状态
     *
     * @param taskInstance 任务实例
     * @param status       新状态
     * @param reason       暂停/终止原因
     */
    void updateTaskStatus(TaskInstance taskInstance, TaskStatusEnum status, String reason);

    /**
     * 获取机器人负责的未完任务
     * ①按计划类型排序，周期任务在前，普通任务在后；②按优先级排序，优先级高的在前
     * @param robotId 机器人ID
     * @return 任务列表
     */
    List<TaskInstance> getUnfinishedTasks(long robotId);

    /**
     * 终止任务
     * @param robotId 机器人ID
     * @param reason 终止原因
     */
    void abortTask(long robotId, String reason);

    /**
     * 暂停任务
     * @param robotId 机器人ID
     * @param reason 暂停原因
     */
    void pauseTask(long robotId, String reason);

    /**
     * 新保存，或恢复任务<p/>
     * 1.更新任务实例状态并保存（新任务会持久化）<br/>
     * 2.获取未完成节点<br/>
     * 3.规划路径<br/>
     * 4.推入RC.directiveChain<br/>
     * 5.写日志<br/>
     *
     * @param task    任务实例
     * @param robotId 机器人ID
     * @param reason  原因
     * @return true成功恢复或开始这个任务 | false任务恢复失败，可能已经完成了
     */
    boolean startOrResume(Task task, long robotId, String reason);

    /**
     * 检查周期任务是否已经触发过了
     * @param defId 任务定义ID
     * @param robotId 机器人ID
     * @return true-已经触发过，false-没有触发过
     */
    boolean periodTaskPassedToday(long defId, long robotId);

    /**
     * 检查周期任务已经触发了多少次
     * @param defId 任务定义ID
     * @return 次数
     */
    long periodTaskPassedNum(long defId);

    /**
     * 完成任务
     */
    void finishedTask(long robotId, Long taskInstanceId);
}
