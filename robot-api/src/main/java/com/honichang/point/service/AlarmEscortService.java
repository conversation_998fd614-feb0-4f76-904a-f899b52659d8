package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.AlarmEscort;

import java.util.List;

/**
 *
 */
public interface AlarmEscortService extends IService<AlarmEscort> {

    /**
     * 根据条件分页查询陪同报警
     *
     * @param alarmEscort 陪同报警参数信息
     * @return 陪同报警数据集合信息
     */
    List<AlarmEscort> selectPageList(AlarmEscort alarmEscort);

}
