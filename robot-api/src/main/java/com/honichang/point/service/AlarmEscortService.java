package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum;
import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.domain.TaskEscort;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface AlarmEscortService extends IService<AlarmEscort> {

    /**
     * 根据条件分页查询陪同报警
     *
     * @param alarmEscort 陪同报警参数信息
     * @return 陪同报警数据集合信息
     */
    List<AlarmEscort> selectPageList(AlarmEscort alarmEscort);

    /**
     * 陪同报警触发
     *
     * @param taskEscort   陪同任务
     * @param reason       报警原因
     * @param pushTemplate 推送消息模板
     */
    void add(@NonNull TaskEscort taskEscort, @NonNull String reason, TaskEscortPushTemplateEnum pushTemplate);
}
