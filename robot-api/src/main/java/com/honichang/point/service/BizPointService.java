package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.BizPoint;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 *
 */
public interface BizPointService extends IService<BizPoint> {

    /**
     * 根据xmapId和mapId获取点位信息<br/>
     * redisKey: bizPoint_xmap_{xmapId}_map_{mapId}
     *
     * @param xmapId xmapId REF[map_point.xmap_id]
     * @param mapId mapId REF[map_point.map_id]
     * @return 点位信息
     */
    @NonNull
    List<BizPoint> getByXmapId(String xmapId, Long mapId);

    String importBizPoint(List<BizPoint> bizPointList, boolean updateSupport, String operName);

    List<BizPoint> getBizPointList(BizPoint bizPoint);

    boolean add(BizPoint bizPoint);

    boolean edit(BizPoint bizPoint);

    /**
     * 根据任务定义获取任务所有点位的点位信息
     * @param taskDefId 任务定义id
     * @return 点位信息
     */
    List<BizPoint> getPointsByTaskDef(long taskDefId);
}
