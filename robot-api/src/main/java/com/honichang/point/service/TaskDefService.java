package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.TaskDef;

import java.util.List;

/**
 *
 */
public interface TaskDefService extends IService<TaskDef> {


    /**
     * 根据条件分页查询任务数据
     *
     * @param taskDef 任务信息
     * @return 任务数据集合信息
     */
    List<TaskDef> selectList(TaskDef taskDef);

    /**
     * 新增任务
     *
     * @param taskDef 任务信息
     * @return
     */
    void add(TaskDef taskDef);

    /**
     * 编辑任务
     *
     * @param taskDef 任务信息
     * @return
     */
    void update(TaskDef taskDef);

    /**
     * 删除任务
     *
     * @param id 任务id
     * @return
     */
    void delete(Long id);

    /**
     * 查询所有循环任务，按优先级排序
     * 必须是有效期内的
     *
     * @return 任务列表
     */
    List<TaskDef> selectAllCycleTask();

    /**
     * 查询所有周期任务，按优先级排序
     * 必须是有效期内的：0无限制|1本月内（即创建开始30天内）
     *
     * @return 任务列表
     */
    List<TaskDef> selectAllPeriodTask();

    /**
     * 查询指定机器人的所有周期任务，按优先级排序
     * @param robotId 机器人id
     * @return 任务列表
     */
    List<TaskDef> selectAllPeriodTask(long robotId);
}
