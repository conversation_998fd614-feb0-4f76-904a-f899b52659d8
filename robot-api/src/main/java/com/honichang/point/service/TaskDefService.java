package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.TaskDef;
import com.honichang.point.domain.TaskDef;
import com.honichang.point.domain.TaskDef;

import java.util.List;

/**
 *
 */
public interface TaskDefService extends IService<TaskDef> {


    /**
     * 根据条件分页查询任务数据
     *
     * @param taskDef 任务信息
     * @return 任务数据集合信息
     */
    List<TaskDef> selectList(TaskDef taskDef);

    /**
     * 新增任务
     *
     * @param taskDef 任务信息
     * @return
     */
    void add(TaskDef taskDef);

    /**
     * 编辑任务
     *
     * @param taskDef 任务信息
     * @return
     */
    void update(TaskDef taskDef);

    /**
     * 删除任务
     *
     * @param id 任务id
     * @return
     */
    void delete(Long id);

}
