package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.IdenModelParamGroup;
import com.honichang.point.domain.IdenModelParamGroup;

import java.util.List;

/**
 *
 */
public interface IdenModelParamGroupService extends IService<IdenModelParamGroup> {

    /**
     * 根据条件分页查询识别模型参数组数据
     *
     * @param idenModelParamGroup 识别模型参数组信息
     * @return 参数数据集合信息
     */
    List<IdenModelParamGroup> selectParamGroupList(IdenModelParamGroup idenModelParamGroup);

    /**
     * 编辑设别模型参数
     *
     * @param idenModelParamGroup 识别模型参数组信息
     * @return
     */
    void add(IdenModelParamGroup idenModelParamGroup);

    /**
     * 删除设别模型参数组
     *
     * @param id 识别模型参数信息
     * @return
     */
    void delete(Long id);


    /**
     * 根据识别模型删除设别模型参数组
     *
     * @param idenModelId 识别模型id
     * @return
     */
    void deleteByIdenModelId(Long idenModelId);
}
