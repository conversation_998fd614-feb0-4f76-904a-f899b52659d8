package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.MapPoint;

import java.util.List;

/**
 *
 */
public interface MapPointService extends IService<MapPoint> {

    /**
     * 清空地图点位
     *
     * @param mapId 地图ID
     */
    void removeByMapId(Long mapId);

    /**
     * 根据地图ID获取点位信息
     *
     * @param mapId 地图ID
     */
    List<MapPoint> getPointsByMapId(Long mapId);
}
