package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.Position;
import com.honichang.point.domain.MapPoint;

import java.util.List;

/**
 *
 */
public interface MapPointService extends IService<MapPoint> {

    /**
     * 清空地图点位
     *
     * @param mapId 地图ID
     */
    void removeByMapId(Long mapId);

    /**
     * 根据地图ID获取点位信息
     *
     * @param mapId 地图ID
     */
    List<MapPoint> getPointsByMapId(Long mapId);

    /**
     * 根据map_point.id从MapContext中获取Xmap点位
     * @param mapPointId map_point.id
     * @param mapId 地图id
     * @return Xmap点位
     * @throws ServiceException 地图点位不存在；地图不匹配，点位不存在
     */
    Position getXmapPosition(Long mapPointId, long mapId);
}
