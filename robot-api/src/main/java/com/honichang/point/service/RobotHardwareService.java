package com.honichang.point.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.honichang.point.domain.RobotHardware;
import org.springframework.lang.Nullable;

/**
 *
 */
public interface RobotHardwareService extends IService<RobotHardware> {

    /**
     * 根据摄像头ip和端口获取机器人id
     *
     * @return 机器人id，null没找到
     */
    @Nullable
    RobotHardware getRobotIdByIpAndPort(String ip, String port);
}
