package com.honichang.point.dto;

import com.ruoyi.robot.map.bo.ObstacleBo;
import com.ruoyi.robot.map.bo.Position;
import com.ruoyi.robot.map.bo.Route;
import com.ruoyi.robot.map.domain.MapMain;

import java.util.List;

public class MapInfoBase {

    /** 地图ID */
    private Long mapId;
    /** 地图名称 */
    private String mapName;
    /** CAD图层宽度，取自图层的svg文件定义width（单位是pt） */
    private Double cadWidth;
    /** CAD图层高度（单位是pt），CAD左上角永远是0,0，右下角是宽高的值 */
    private Double cadHeight;
    /** CAD图层svg文件url */
    private String cadUrl;
    /**
     * 地图比例尺
     */
    private Double scale;
    /**
     * XMAP图层宽度（米）
     */
    private Double xmapWidth;
    /**
     * XMAP图层高度（米）
     */
    private Double xmapHeight;
    /**
     * XMAP图层版本
     */
    private Double xmapVersion;
    private Double minPosX;
    private Double minPosY;
    private Double maxPosX;
    private Double maxPosY;
    /**
     * XMAP图层SVG URL
     */
    private String xmapSvgUrl;
    /** XMAP图层二次偏移X轴的单位pt（相对于CAD左上角，向右是正数） */
    private Double offsetX;
    /** XMAP图层二次偏移Y轴的单位pt（相对于CAD左上角，向下是正数） */
    private Double offsetY;
    /**
     * XMAP图层宽度（pt）
     */
    private Double xmapSvgWidth;
    /**
     * XMAP图层高度（pt）
     */
    private Double xmapSvgHeight;

    /**
     * 全景地图的路径点（偏移后的）
     */
    private List<Position> advancedPoints;
    /**
     * 全景地图的路径（偏移后的）
     */
    private List<Route> advancedRoutes;
    /**
     * 障碍点信息
     */
    private List<ObstacleBo> obstacles;
    /**
     * 合并后的SVG文件URL（仅用于制图平移预览）
     */
    private String mergeSvgUrl;

    public List<ObstacleBo> getObstacles() {
        return obstacles;
    }

    public void setObstacles(List<ObstacleBo> obstacles) {
        this.obstacles = obstacles;
    }

    public void setCadUrl(String cadUrl) {
        this.cadUrl = cadUrl;
    }

    public String getCadUrl() {
        return cadUrl;
    }

    public void setCadWidth(Double cadWidth) {
        this.cadWidth = cadWidth;
    }

    public Double getCadWidth() {
        return cadWidth;
    }

    public void setCadHeight(Double cadHeight) {
        this.cadHeight = cadHeight;
    }

    public Double getCadHeight() {
        return cadHeight;
    }

    public void setScale(Double scale) {
        this.scale = scale;
    }

    public Double getScale() {
        return scale;
    }

    public void setXmapSvgUrl(String xmapSvgUrl) {
        this.xmapSvgUrl = xmapSvgUrl;
    }

    public String getXmapSvgUrl() {
        return xmapSvgUrl;
    }

    public void setXmapWidth(Double xmapWidth) {
        this.xmapWidth = xmapWidth;
    }

    public Double getXmapWidth() {
        return xmapWidth;
    }

    public void setXmapHeight(Double xmapHeight) {
        this.xmapHeight = xmapHeight;
    }

    public Double getXmapHeight() {
        return xmapHeight;
    }

    public void setXmapVersion(Double xmapVersion) {
        this.xmapVersion = xmapVersion;
    }

    public Double getXmapVersion() {
        return xmapVersion;
    }

    public void setMinPosX(Double minPosX) {
        this.minPosX = minPosX;
    }

    public Double getMinPosX() {
        return minPosX;
    }

    public void setMinPosY(Double minPosY) {
        this.minPosY = minPosY;
    }

    public Double getMinPosY() {
        return minPosY;
    }

    public void setMaxPosX(Double maxPosX) {
        this.maxPosX = maxPosX;
    }

    public Double getMaxPosX() {
        return maxPosX;
    }

    public void setMaxPosY(Double maxPosY) {
        this.maxPosY = maxPosY;
    }

    public Double getMaxPosY() {
        return maxPosY;
    }

    public void setXmapSvgWidth(Double xmapSvgWidth) {
        this.xmapSvgWidth = xmapSvgWidth;
    }

    public Double getXmapSvgWidth() {
        return xmapSvgWidth;
    }

    public Double getXmapSvgHeight() {
        return xmapSvgHeight;
    }

    public void setXmapSvgHeight(Double xmapSvgHeight) {
        this.xmapSvgHeight = xmapSvgHeight;
    }

    public List<Position> getAdvancedPoints() {
        return advancedPoints;
    }

    public void setAdvancedPoints(List<Position> advancedPoints) {
        this.advancedPoints = advancedPoints;
    }

    public List<Route> getAdvancedRoutes() {
        return advancedRoutes;
    }

    public void setAdvancedRoutes(List<Route> advancedRoutes) {
        this.advancedRoutes = advancedRoutes;
    }

    public Long getMapId() {
        return mapId;
    }

    public void setMapId(Long mapId) {
        this.mapId = mapId;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public Double getOffsetX() {
        return offsetX;
    }

    public void setOffsetX(Double offsetX) {
        this.offsetX = offsetX;
    }

    public Double getOffsetY() {
        return offsetY;
    }

    public void setOffsetY(Double offsetY) {
        this.offsetY = offsetY;
    }

    public void convertFrom(MapMain mapMain) {
        this.mapId = mapMain.getMapId();
        this.mapName = mapMain.getMapName();
        this.cadWidth = mapMain.getCadWidth();
        this.cadHeight = mapMain.getCadHeight();
        this.cadUrl = mapMain.getCadUrl();
        this.scale = mapMain.getScale();
        this.xmapWidth = mapMain.getXmapWidth();
        this.xmapHeight = mapMain.getXmapHeight();
        this.xmapVersion = mapMain.getXmapVersion();
        this.minPosX = mapMain.getMinPosX();
        this.minPosY = mapMain.getMinPosY();
        this.maxPosX = mapMain.getMaxPosX();
        this.maxPosY = mapMain.getMaxPosY();
        this.xmapSvgUrl = mapMain.getXmapSvgUrl();
        this.offsetX = mapMain.getOffsetX();
        this.offsetY = mapMain.getOffsetY();
        this.xmapSvgWidth = mapMain.getXmapSvgWidth();
        this.xmapSvgHeight = mapMain.getXmapSvgHeight();
    }

    public void setMergeSvgUrl(String mergeSvgUrl) {
        this.mergeSvgUrl = mergeSvgUrl;
    }

    public String getMergeSvgUrl() {
        return mergeSvgUrl;
    }
}
