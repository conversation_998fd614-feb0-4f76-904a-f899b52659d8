package com.honichang.point.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.honichang.point.domain.MediaFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskInstanceNodeVo {

    private Long id;

    private String taskName;

    private String deptName;

    private String pointName;

    private String robotName;

    private String idenModelName;

    private String ruleResultDict;

    private String ruleResult;

    private Date idenTime;

    private String status;

    private String auditStatus;


    private Long deptId;
    private Long toolGroupId;
    private Long idenModelId;
    private Long bizPointId;
    private Long taskId;
    private Long taskInstanceId;
    private Long robotId;


    private List<MediaFile> mediaFileList;

}
