package com.honichang.point.model.vo;

import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.point.domain.MapMain;
import lombok.Data;

import java.util.List;

/**
 * 地图响应对象基类
 */
@Data
public class MapResponseBase {

    /** 地图ID */
    private Long mapId;
    /** 地图名称 */
    private String mapName;
    /** CAD图层宽度，取自图层的svg文件定义width（单位是pt） */
    private Double cadWidth;
    /** CAD图层高度（单位是pt），CAD左上角永远是0,0，右下角是宽高的值 */
    private Double cadHeight;
    /** CAD图层svg文件url */
    private String cadUrl;
    /**
     * 地图比例尺
     */
    private Double scale;
    /**
     * XMAP图层宽度（米）
     */
    private Double xmapWidth;
    /**
     * XMAP图层高度（米）
     */
    private Double xmapHeight;
    /**
     * XMAP图层版本
     */
    private Double xmapVersion;
    private Double minPosX;
    private Double minPosY;
    private Double maxPosX;
    private Double maxPosY;
    /** XMAP图层二次偏移X轴的单位pt（相对于CAD左上角，向右是正数） */
    private Double offsetX;
    /** XMAP图层二次偏移Y轴的单位pt（相对于CAD左上角，向下是正数） */
    private Double offsetY;
    /**
     * XMAP图层宽度（pt）
     */
    private Double xmapSvgWidth;
    /**
     * XMAP图层高度（pt）
     */
    private Double xmapSvgHeight;

    /**
     * 全景地图的路径点（偏移后的）
     */
    private List<Position> advancedPoints;
    /**
     * 全景地图的路径（偏移后的）
     */
    private List<Path> advancedPaths;
    /**
     * 障碍点信息
     */
    private List<Obstacle> obstacles;
    /**
     * 合并后的SVG文件URL（仅用于制图平移预览）
     */
    private String mergeSvgUrl;

    public static <T extends MapResponseBase> T convertFrom(Class<T> clazz, MapMain mapMain) {
        T t;
        try {
            t = clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException("创建制图响应对象失败", e);
        }
        t.setMapId(mapMain.getId());
        t.setMapName(mapMain.getMapName());
        t.setCadWidth(mapMain.getCadWidth());
        t.setCadHeight(mapMain.getCadHeight());
        t.setCadUrl(mapMain.getCadUrl());
        t.setScale(mapMain.getScale());
        t.setXmapWidth(mapMain.getXmapWidth());
        t.setXmapHeight(mapMain.getXmapHeight());
        t.setXmapVersion(mapMain.getXmapVersion());
        t.setMinPosX(mapMain.getMinPosX());
        t.setMinPosY(mapMain.getMinPosY());
        t.setMaxPosX(mapMain.getMaxPosX());
        t.setMaxPosY(mapMain.getMaxPosY());
        t.setOffsetX(mapMain.getOffsetX());
        t.setOffsetY(mapMain.getOffsetY());
        t.setXmapSvgWidth(mapMain.getXmapSvgWidth());
        t.setXmapSvgHeight(mapMain.getXmapSvgHeight());
        return t;
    }

}
