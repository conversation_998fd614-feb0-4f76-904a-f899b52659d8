package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName robot_dynamic_attr
 */
@TableName(value ="robot_dynamic_attr")
@Data
public class RobotDynamicAttr implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ⬇任务属性开始
     */
    @TableField(value = "pos_x")
    private Double posX;

    /**
     *
     */
    @TableField(value = "pos_y")
    private Double posY;

    /**
     * 最后的角度，单位rad
     */
    @TableField(value = "theta")
    private Double theta;

    /**
     * 范围 0~1
     */
    @TableField(value = "reliability")
    private Double reliability;

    /**
     * 当前所在路径点编号，0 表示未在路径点上
     */
    @TableField(value = "xmap_point_id")
    private String xmapPointId;

    /**
     * 当前所在路径编号，0 表示未在路径上
     */
    @TableField(value = "xmap_path_id")
    private String xmapPathId;

    /**
     *
     */
    @TableField(value = "longitude")
    private Double longitude;

    /**
     *
     */
    @TableField(value = "latitude")
    private Double latitude;

    /**
     * 1有效
     */
    @TableField(value = "compass_quality")
    private String compassQuality;

    /**
     *
     */
    @TableField(value = "compass")
    private Double compass;

    /**
     * 当前任务类型：0巡检任务 | 1陪同任务 | 2排查障碍任务
     */
    @TableField(value = "latest_task_class")
    private Integer latestTaskClass;

    /**
     * REF[task_instance.id]，NULL不在任务中
     */
    @TableField(value = "latest_task_id")
    private Long latestTaskId;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * 累计运行时间秒
     */
    @TableField(value = "total_running")
    private Long totalRunning;

    /**
     * 累计运行里程米
     */
    @TableField(value = "total_odom")
    private Double totalOdom;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
