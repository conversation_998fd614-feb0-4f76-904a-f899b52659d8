package com.honichang.point.domain;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 固定死：rule_result=1~3级报警，其他的不报警
 * @TableName iden_rule_expr
 */
@TableName(value ="iden_rule_expr")
@Data
public class IdenRuleExpr implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 同一维度下不允许重复
     */
    @TableField(value = "rule_result")
    private String ruleResult;

    /**
     *
     */
    @TableField(value = "expression_json")
    private String expressionJson;

    /**
     * 人类可读
     */
    @TableField(value = "expression_text")
    private String expressionText;

    /**
     * REF[iden_rule.id]
     */
    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
