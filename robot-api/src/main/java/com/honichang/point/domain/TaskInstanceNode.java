package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 实际的任务检查点信息,识别图片原图一张,带识别框一张
 * @TableName task_instance_node
 */
@TableName(value ="task_instance_node")
@Data
public class TaskInstanceNode implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 到达任务点的时间
     */
    @TableField(value = "arrive_time")
    private Date arriveTime;

    /**
     * 离开任务点前往下一个目标
     */
    @TableField(value = "leave_time")
    private Date leaveTime;

    /**
     * 识别时间
     */
    @TableField(value = "iden_time")
    private Date idenTime;

//    /**
//     * null说明当前节点是终点
//     */
//    @TableField(value = "plan_next_id")
//    private Long planNextId;
//
//    /**
//     * 因为障碍的出现可能会与规划不一致
//     */
//    @TableField(value = "actual_next_id")
//    private Long actualNextId;

    /**
     * ⬇冗余自biz_point
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * REF[biz_point.tool_group]
     */
    @TableField(value = "tool_group_id")
    private Long toolGroupId;

    /**
     * REF[biz_point.tool_id]
     */
    @TableField(value = "tool_id")
    private String toolId;



    /**
     * REF[biz_point.point_name]
     */
    @TableField(value = "point_name")
    private String pointName;

    /**
     * ⬇冗余自iden_model系列，REF[iden_model.id]
     */
    @TableField(value = "iden_model_id")
    private Long idenModelId;

    /**
     * REF[iden_model.iden_model_name]
     */
    @TableField(value = "iden_model_name")
    private String idenModelName;

    /**
     * REF[biz_point.id]
     */
    @TableField(value = "biz_point_id")
    private Long bizPointId;

    /**
     * REF[task_def.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * REF[task_instance.id]
     */
    @TableField(value = "task_instance_id")
    private Long taskInstanceId;

    /**
     * ⬇定性结果，REF[iden_rule_expr.rule_result]
     */
    @TableField(value = "rule_result")
    private String ruleResult;

    /**
     * REF[iden_rule_expr.expression_text]
     */
    @TableField(value = "rule_expression_text")
    private String ruleExpressionText;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * ⬇审核开始
     */
    @TableField(value = "auditor")
    private String auditor;

    /**
     * 0未审核 | 1审核通过 | 2审核不通过
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String imgUrl;

    @TableField(exist = false)
    private List<TaskInstanceNodeResult> taskInstanceNodeResultList;

    /**
     * 点位信息
     */
    @TableField(exist = false)
    private BizPoint bizPoint;

    /**
     * AI参数
     */
    @TableField(exist = false)
    private List<BizPointAiParam> aiParams;

    /**
     * AI识别配置
     */
    @TableField(exist = false)
    private List<BizPointIdenConf> idenConfs;

    /**
     * 识别结果图片
     */
    @TableField(exist = false)
    private List<MediaFile> mediaFileList;

}
