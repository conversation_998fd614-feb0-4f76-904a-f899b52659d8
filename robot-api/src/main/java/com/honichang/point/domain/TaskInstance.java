package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @TableName task_instance
 */
@TableName(value = "task_instance")
@Data
public class TaskInstance implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     *
     */
    @TableField(value = "finish_time")
    private Date finishTime;

    /**
     * 结束时的环境信息采集
     */
    @TableField(value = "env_report")
    private String envReport;

    /**
     * COUNT(task_instance_node.columns.count)
     */
    @TableField(value = "point_count")
    private Integer pointCount;

    /**
     * ⬇完成点位识别即刻写入
     * 智能报警数量
     */
    @TableField(value = "ai_alarm_num")
    private Integer aiAlarmNum;

    /**
     * 告警点位数
     */
    @TableField(value = "point_alarm_num")
    private Integer pointAlarmNum;

    /**
     * 正常点位数
     */
    @TableField(value = "point_health_num")
    private Integer pointHealthNum;

    /**
     * 不可达点位数
     */
    @TableField(value = "unreachable_num")
    private Integer unreachableNum;

    /**
     * 识别异常点位数
     */
    @TableField(value = "abnormal_num")
    private Integer abnormalNum;

    /**
     * REF[task_def.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * REF[map_main.id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     *
     */
    @TableField(value = "termination_reason")
    private String terminationReason;

    /**
     * 0未审核 | 1部分审核 | 2审核完成
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 1执行中 | 3完成 | 4暂停 | 8终止
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0否：完成或终止的任务，进入历史状态
     */
    @TableField(value = "his_flag")
    private String hisFlag;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String taskName;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private String duration;

    @TableField(exist = false)
    private List<AlarmAi> alarmAiList;

    @TableField(exist = false)
    private List<TaskInstanceNode> taskInstanceNodeList;

}
