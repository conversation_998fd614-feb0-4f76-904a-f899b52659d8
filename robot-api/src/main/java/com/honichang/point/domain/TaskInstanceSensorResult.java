package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName task_instance_sensor_result
 */
@TableName(value ="task_instance_sensor_result")
@Data
public class TaskInstanceSensorResult implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "collection_value")
    private Double collectionValue;

    /**
     * REF[task_def.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * REF[task_instance.id]
     */
    @TableField(value = "task_instance_id")
    private Long taskInstanceId;

    /**
     * REF[task_instance_node.id]
     */
    @TableField(value = "task_instance_node_id")
    private Long taskInstanceNodeId;

    /**
     * REF[conf_env_sensor.id]
     */
    @TableField(value = "conf_env_sensor_id")
    private Long confEnvSensorId;

    /**
     * REF[conf_env_sensor.sensor_name]
     */
    @TableField(value = "sensor_name")
    private String sensorName;

    /**
     * REF[iden_rule.id]
     */
    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     * REF[iden_rule.iden_rule_name]
     */
    @TableField(value = "iden_rule_name")
    private String idenRuleName;

    /**
     * REF[iden_rule_expr.rule_result]
     */
    @TableField(value = "iden_rule_result")
    private String idenRuleResult;

    /**
     * REF[iden_rule_expr.expression_text]
     */
    @TableField(value = "expression_text")
    private String expressionText;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 审核备注
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
