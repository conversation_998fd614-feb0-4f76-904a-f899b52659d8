package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName iden_type_param
 */
@TableName(value ="iden_type_param")
@Data
public class IdenTypeParam implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "param_name")
    private String paramName;

    /**
     * 1-Float | 2-枚举
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     * float: { "min": 0.0, "max": 180.0 }
enum: [ "开", "关" ]
     */
    @TableField(value = "value_scope_json")
    private String valueScopeJson;

    /**
     * REF[iden_type.id]
     */
    @TableField(value = "iden_type_id")
    private Long idenTypeId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private Double minValue;

    @TableField(exist = false)
    private Double maxValue;

    @TableField(exist = false)
    private String enumValue;
}
