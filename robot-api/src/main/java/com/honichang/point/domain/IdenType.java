package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName iden_type
 */
@TableName(value ="iden_type")
@Data
public class IdenType implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "iden_type_name")
    private String idenTypeName;

    /**
     *
     */
    @TableField(value = "sample_photo_url")
    private String samplePhotoUrl;

    /**
     *
     */
    @TableField(value = "sample_photo_physical")
    private String samplePhotoPhysical;

    /**
     * 单圈表双圈表需要标记手动起终点
     */
    @TableField(value = "have_min_max")
    private String haveMinMax;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



    @TableField(exist = false)
    private List<IdenType> children;
}
