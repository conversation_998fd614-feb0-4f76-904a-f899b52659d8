package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName robot
 */
@TableName(value ="robot")
@Data
public class Robot implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "robot_name")
    private String robotName;

    /**
     *
     */
    @TableField(value = "robot_model")
    private String robotModel;

    /**
     *
     */
    @TableField(value = "sn")
    private String sn;

    /**
     *
     */
    @TableField(value = "begin_use_time")
    private Date beginUseTime;

    /**
     *
     */
    @TableField(value = "ip")
    private String ip;

    /**
     *
     */
    @TableField(value = "port")
    private String port;

    /**
     *
     */
    @TableField(value = "version")
    private String version;

    /**
     * 与机器人底盘通讯用
     */
    @TableField(value = "app_code")
    private String appCode;

    /**
     * 越大越优先
     */
    @TableField(value = "priority")
    private Double priority;

    /**
     * REF[map_main.map_id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * 0待机 | 1工作 | 4暂停 | 6定位中 |
     * 701载入地图失败 | 702定位失败 | 703导航失败
     * 8离线 | 9故障
     */
    @TableField(value = "status")
    private String status;

    /**
     * 10100陪同任务 | 10300陪同返航<br/>
     * 30100充电 | 30200 升级<br/>
     * 60100周期任务 | 60180检查障碍 | 60200任务 | 60300任务返航<p/>
     *
     * 机器人路径优先级排序：where status='1' order by work_status, priority desc
     */
    @TableField(value = "work_status")
    private Integer workStatus;

    /**
     * 工作模式：101自动模式 | 102遥控模式(后端遥控) | 201手动模式(厂家遥控器)
     */
    @TableField(value = "work_mode")
    private Integer workMode;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private RobotDynamicAttr robotDynamicAttr;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<RobotHardware> robotHardwareList;
}
