package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName map_path
 */
@TableName(value ="map_path")
@Data
public class MapPath implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来自机器人扫图xmap文件
     */
    @TableField(value = "xmap_id")
    private String xmapId;

    /**
     * 默认空串，无用
     */
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * bezier_curve贝塞尔曲线|straight_line直线|convex凸弧线|concave_arc凹弧线
     */
    @TableField(value = "route_type")
    private String routeType;

    /**
     * 仅弧线类型需要，单位:rad
     */
    @TableField(value = "radian")
    private Double radian;

    /**
     * REF[map_point.xmap_id]
     */
    @TableField(value = "xmap_start_pos_id")
    private String xmapStartPosId;

    /**
     * REF[map_point.xmap_id]
     */
    @TableField(value = "xmap_end_pos_id")
    private String xmapEndPosId;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "start_pos_id")
    private Long startPosId;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "end_pos_id")
    private Long endPosId;

    /**
     * 单位：米
     */
    @TableField(value = "start_x")
    private Double startX;

    /**
     * 单位：米
     */
    @TableField(value = "start_y")
    private Double startY;

    /**
     * 单位：米
     */
    @TableField(value = "end_x")
    private Double endX;

    /**
     * 单位：米
     */
    @TableField(value = "end_y")
    private Double endY;

    /**
     * 单位：米
     */
    @TableField(value = "control_x_1")
    private Double controlX1;

    /**
     * 单位：米
     */
    @TableField(value = "control_y_1")
    private Double controlY1;

    /**
     * 单位：米
     */
    @TableField(value = "control_x_2")
    private Double controlX2;

    /**
     * 单位：米
     */
    @TableField(value = "control_y_2")
    private Double controlY2;

    /**
     * 暂无用
     */
    @TableField(value = "angle_compensation")
    private Double angleCompensation;

    /**
     * forward正走 | backward倒走<br/>
     * 取消both，如果是both则拆成两条路径
     */
    @TableField(value = "direction")
    private String direction;

    /**
     * start至end的长度，单位：米
     */
    @TableField(value = "real_length")
    private Double realLength;

    /**
     * REF[map_main.id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
