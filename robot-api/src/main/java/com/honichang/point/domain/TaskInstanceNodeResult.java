package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName task_instance_node_result
 */
@TableName(value ="task_instance_node_result")
@Data
public class TaskInstanceNodeResult implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * REF[iden_model_param.param_name]
     */
    @TableField(value = "iden_model_param_name")
    private String idenModelParamName;

    /**
     * 1-Float | 2-枚举
     */
    @TableField(value = "data_type")
    private Integer dataType;

    /**
     *
     */
    @TableField(value = "float_value")
    private Double floatValue;

    /**
     *
     */
    @TableField(value = "enum_key")
    private String enumKey;

    /**
     *
     */
    @TableField(value = "enum_value")
    private String enumValue;

    /**
     * 0不正确 | 1正确
     */
    @TableField(value = "correct_flag")
    private String correctFlag;

    /**
     * 不正确可人为修改float值
     */
    @TableField(value = "actual_float_value")
    private Double actualFloatValue;

    /**
     * REF[task_def.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * REF[task_instance.id]
     */
    @TableField(value = "task_instance_id")
    private Long taskInstanceId;

    /**
     * REF[task_instance_node.id]
     */
    @TableField(value = "task_instance_node_id")
    private Long taskInstanceNodeId;

    /**
     * REF[iden_model_param.id]
     */
    @TableField(value = "iden_model_param_id")
    private Long idenModelParamId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 审核备注
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
