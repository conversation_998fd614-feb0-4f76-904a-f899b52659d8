package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName task_robot
 */
@TableName(value ="task_robot")
@Data
public class TaskRobot implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * REF[task_main.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * 0否 | 1是；一个任务只能有一个主
     */
    @TableField(value = "master_flag")
    private String masterFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
