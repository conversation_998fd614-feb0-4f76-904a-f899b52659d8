package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName task_def
 */
@TableName(value ="task_def")
@Data
public class TaskDef implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 越大越优先
     */
    @TableField(value = "priorty")
    private Double priorty;

    /**
     * ⬇计划通用：0循环计划 | 1周期计划
     */
    @TableField(value = "plan_type")
    private String planType;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * ⬇计划类型=周期有效：0每天 | 1每周 | 2每月 | 9间隔
     */
    @TableField(value = "cycle_type")
    private String cycleType;

    /**
     * pattern：HH:mm
     */
    @TableField(value = "cycle_trigger")
    private String cycleTrigger;

    /**
     * 0无限制 | 1本月
     */
    @TableField(value = "cycle_scope")
    private String cycleScope;

    /**
     * （单位hour）
     */
    @TableField(value = "cycle_interval")
    private Integer cycleInterval;

    /**
     * REF[sys_dept.id]
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 暂不用
     */
    @TableField(value = "latest_exec_time")
    private Date latestExecTime;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private Integer robotNum;


    @TableField(exist = false)
    private List<Long> bizPointIds;

    @TableField(exist = false)
    private List<TaskRobot> taskRobotList;
}
