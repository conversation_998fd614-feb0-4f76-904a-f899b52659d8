package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName map_point
 */
@TableName(value ="map_point")
@Data
public class MapPoint implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 为了兼容与扩展，存varchar
     */
    @TableField(value = "xmap_id")
    private String xmapId;

    /**
     * 来自地图文件解析结果
     * class_name：路径点类型
     * - LandMark（路径点）
     * - ChargePoint（充电点）
     * - HomePoint（充电房）
     * - ReturnPoint（返航点）
     * - JobPoint（工作点）
     */
    @TableField(value = "class_name")
    private String className;

    /**
     *
     */
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * 机器人只认这个坐标
     */
    @TableField(value = "pos_x")
    private Double posX;

    /**
     *
     */
    @TableField(value = "pos_y")
    private Double posY;

    /**
     * 0 不允许 | 1 允许
     */
    @TableField(value = "allow_revolve")
    private String allowRevolve;

    /**
     * 朝向角
     */
    @TableField(value = "angle")
    private Double angle;

    /**
     * REF[map_main.id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;


    // region 👇瞬态
    /**
     * 是否是十字路口
     */
    @TableField(exist = false)
    private Boolean isCrossroads;

    /**
     * REF[biz_point.class_name]<br/>
     * dict-key: point_class_name<p/>
     * 点位类型: <br/>
     * LandMark(标记点) | ChargePoint(充电点) | IdenPoint(识别) | InfraredDetection(红外测温) | Crossroads(十字路口) | Exit(出口) | Elevator(电梯) | EyeWashStation(紧急洗眼器) | Parking(停车位)
     */
    @TableField(exist = false)
    private String bizClassName;

    @TableField(exist = false)
    private Long bizPointId;

    @TableField(exist = false)
    private String bizInstanceName;
    // endregion

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
