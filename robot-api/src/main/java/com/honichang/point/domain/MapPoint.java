package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName map_point
 */
@TableName(value ="map_point")
@Data
public class MapPoint implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 为了兼容与扩展，存varchar
     */
    @TableField(value = "xmap_id")
    private String xmapId;

    /**
     * 来自地图文件解析结果
     */
    @TableField(value = "class_name")
    private String className;

    /**
     *
     */
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * 机器人只认这个坐标
     */
    @TableField(value = "pos_x")
    private Double posX;

    /**
     *
     */
    @TableField(value = "pos_y")
    private Double posY;

    /**
     * 0 不允许 | 1 允许
     */
    @TableField(value = "allow_revolve")
    private String allowRevolve;

    /**
     * 朝向角
     */
    @TableField(value = "angle")
    private Double angle;

    /**
     * REF[map_main.id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
