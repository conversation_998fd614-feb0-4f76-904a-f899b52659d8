package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName conf_robot_voice
 */
@TableName(value ="conf_robot_voice")
@Data
public class ConfRobotVoice implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * dict-key: voice-scene
     */
    @TableField(value = "scene")
    private String scene;

    /**
     *
     */
    @TableField(value = "display")
    private String display;

    /**
     *
     */
    @TableField(value = "voice_file_path")
    private String voiceFilePath;

    /**
     *
     */
    @TableField(value = "voice_url")
    private String voiceUrl;

    /**
     * 自增
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 0未下发 | 1部分下发 | 2全部下发
     */
    @TableField(value = "publish_status")
    private String publishStatus;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<Long> robotIds;

    @TableField(exist = false)
    private List<String> robotNames;
}
