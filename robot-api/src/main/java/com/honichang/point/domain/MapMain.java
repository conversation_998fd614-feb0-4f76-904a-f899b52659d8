package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName map_main
 */
@TableName(value ="map_main")
@Data
public class MapMain implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "map_name")
    private String mapName;

    /**
     *
     */
    @TableField(value = "cad_width")
    private Double cadWidth;

    /**
     *
     */
    @TableField(value = "cad_height")
    private Double cadHeight;

    /**
     *
     */
    @TableField(value = "cad_url")
    private String cadUrl;

    /**
     *
     */
    @TableField(value = "cad_path")
    private String cadPath;

    /**
     * 新建地图必须固定好比例尺
     */
    @TableField(value = "scale")
    private Double scale;

    /**
     *
     */
    @TableField(value = "xmap_width")
    private Double xmapWidth;

    /**
     *
     */
    @TableField(value = "xmap_height")
    private Double xmapHeight;

    /**
     *
     */
    @TableField(value = "xmap_svg_width")
    private Double xmapSvgWidth;

    /**
     *
     */
    @TableField(value = "xmap_svg_height")
    private Double xmapSvgHeight;

    /**
     *
     */
    @TableField(value = "xmap_version")
    private Double xmapVersion;

    /**
     *
     */
    @TableField(value = "min_pos_x")
    private Double minPosX;

    /**
     *
     */
    @TableField(value = "min_pos_y")
    private Double minPosY;

    /**
     *
     */
    @TableField(value = "max_pos_x")
    private Double maxPosX;

    /**
     *
     */
    @TableField(value = "max_pos_y")
    private Double maxPosY;

    /**
     *
     */
    @TableField(value = "xmap_init_path")
    private String xmapInitPath;

    /**
     *
     */
    @TableField(value = "xmap_svg_path")
    private String xmapSvgPath;

    /**
     *
     */
    @TableField(value = "offset_x")
    private Double offsetX;

    /**
     *
     */
    @TableField(value = "offset_y")
    private Double offsetY;

    /**
     * 用于记录是哪个扫图文件
     */
    @TableField(value = "xmap_filename")
    private String xmapFilename;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
