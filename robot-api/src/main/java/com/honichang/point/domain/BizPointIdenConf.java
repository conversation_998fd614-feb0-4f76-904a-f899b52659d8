package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 拍照机配置
 * @TableName biz_point_iden_conf
 */
@TableName(value ="biz_point_iden_conf")
@Data
public class BizPointIdenConf implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 框选的title
     */
    @TableField(value = "iden_name")
    private String idenName;

    /**
     *
     */
    @TableField(value = "min_pos_x")
    private Double minPosX;

    /**
     *
     */
    @TableField(value = "min_pos_y")
    private Double minPosY;

    /**
     *
     */
    @TableField(value = "max_pos_x")
    private Double maxPosX;

    /**
     *
     */
    @TableField(value = "max_pos_y")
    private Double maxPosY;

    /**
     * 0没有 | 1有
     */
    @TableField(value = "adjust_line_flag")
    private String adjustLineFlag;

    /**
     *
     */
    @TableField(value = "line_start_x")
    private Double lineStartX;

    /**
     *
     */
    @TableField(value = "line_start_y")
    private Double lineStartY;

    /**
     *
     */
    @TableField(value = "line_end_x")
    private Double lineEndX;

    /**
     *
     */
    @TableField(value = "line_end_y")
    private Double lineEndY;

    /**
     *
     */
    @TableField(value = "begin_scalar_x")
    private Double beginScalarX;

    /**
     *
     */
    @TableField(value = "begin_scalar_y")
    private Double beginScalarY;

    /**
     *
     */
    @TableField(value = "end_scalar_x")
    private Double endScalarX;

    /**
     *
     */
    @TableField(value = "end_scalar_y")
    private Double endScalarY;

    /**
     *
     */
    @TableField(value = "min_limit")
    private Double minLimit;

    /**
     *
     */
    @TableField(value = "max_limit")
    private Double maxLimit;

    /**
     * REF[biz_point.id]
     */
    @TableField(value = "point_id")
    private Long pointId;

    /**
     * REF[iden_model_param.id]
     */
    @TableField(value = "model_param_id")
    private Long modelParamId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
