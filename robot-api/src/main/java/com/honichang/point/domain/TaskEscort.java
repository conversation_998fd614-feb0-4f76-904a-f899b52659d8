package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName task_escort
 */
@TableName(value ="task_escort")
@Data
public class TaskEscort implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 1-立即执行 | 2-预约执行
     */
    @TableField(value = "exec_type")
    private Integer execType;

    /**
     * 立即执行，就是当前时间
     */
    @TableField(value = "exec_time")
    private Date execTime;

    /**
     * 允许等待的时间（分钟）
     */
    @TableField(value = "timeout_min")
    private Integer timeoutMin;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "wait_point_id")
    private Long waitPointId;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "arrive_point_id")
    private Long arrivePointId;

    /**
     * REF[uwb_card.id]
     */
    @TableField(value = "uwb_card_id")
    private Long uwbCardId;

    /**
     * REF[sys_user.id]
     */
    @TableField(value = "superintendent")
    private Long superintendent;

    /**
     * 分钟
     */
    @TableField(value = "pause_min")
    private Integer pauseMin;

    /**
     *
     */
    @TableField(value = "recovery_time")
    private Date recoveryTime;

    /**
     * 0未开始 | 1陪同中 | 3已完成 | 4暂停 | 6抵达等待点 | 7抵达目的地 | 8终止 | 9报警
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
