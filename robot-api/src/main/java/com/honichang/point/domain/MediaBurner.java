package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName media_burner
 */
@TableName(value ="media_burner")
@Data
public class MediaBurner implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "burner_name")
    private String burnerName;

    /**
     *
     */
    @TableField(value = "burner_model")
    private String burnerModel;

    /**
     *
     */
    @TableField(value = "storage_root_path")
    private String storageRootPath;

    /**
     *
     */
    @TableField(value = "total_capacity")
    private Long totalCapacity;

    /**
     *
     */
    @TableField(value = "used_capacity")
    private Long usedCapacity;

    /**
     *
     */
    @TableField(value = "last_maintenance")
    private Date lastMaintenance;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
