package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.honichang.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName log_industrial_control
 */
@TableName(value ="log_industrial_control")
@Data
public class LogIndustrialControl implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @Excel(name = "记录时间", type = Excel.Type.EXPORT, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;

    @Excel(name = "机器人", type = Excel.Type.EXPORT)
    @TableField(exist = false)
    private String robotName;

    /**
     *
     */
    @Excel(name = "类型", type = Excel.Type.EXPORT)
    @TableField(value = "log_type")
    private String logType;

    /**
     *
     */
    @Excel(name = "内容", type = Excel.Type.EXPORT)
    @TableField(value = "content")
    private String content;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * 0正常 | 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
