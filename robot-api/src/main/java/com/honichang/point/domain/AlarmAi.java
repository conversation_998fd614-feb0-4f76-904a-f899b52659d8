package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName alarm_ai
 */
@TableName(value ="alarm_ai")
@Data
public class AlarmAi implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "alarm_name")
    private String alarmName;

    /**
     * 1-传感器 | 2-AI
     */
    @TableField(value = "iden_type")
    private Integer idenType;

    /**
     * 1-发现点 | 2-结束点
     */
    @TableField(value = "sign_type")
    private Integer signType;

    /**
     * REF[biz_point.id]
     */
    @TableField(value = "near_point_id")
    private Long nearPointId;

    /**
     *
     */
    @TableField(value = "pos_x")
    private Double posX;

    /**
     *
     */
    @TableField(value = "pos_y")
    private Double posY;

    /**
     * 冗余触发报警的那条规则的表达式
     */
    @TableField(value = "expression_text")
    private String expressionText;

    /**
     *
     */
    @TableField(value = "iden_result")
    private String idenResult;

    /**
     * 同iden_rule_expr.rule_result
     */
    @TableField(value = "alarm_level")
    private String alarmLevel;

    /**
     *
     */
    @TableField(value = "trigger_time")
    private Date triggerTime;

    /**
     *
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * REF[iden_rule.id]
     */
    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     * REF[sys_dept.id]
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * REF[task_instance.id]，不在任务中为空
     */
    @TableField(value = "task_instance_id")
    private Long taskInstanceId;

    /**
     * 0未确认 | 1已确认
     */
    @TableField(value = "confirm_status")
    private String confirmStatus;

    /**
     * 确认人
     */
    @TableField(value = "confirm_person")
    private Long confirmPerson;

    /**
     * 确认时间
     */
    @TableField(value = "confirm_time")
    private Date confirmTime;

    /**
     * 确认内容
     */
    @TableField(value = "confirm_content")
    private String confirmContent;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String imgUrl;

    @TableField(exist = false)
    private String robotName;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private List<MediaFile> mediaFileList;
}
