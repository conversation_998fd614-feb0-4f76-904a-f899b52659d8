package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName robot_charging_pile
 */
@TableName(value ="robot_charging_pile")
@Data
public class RobotChargingPile implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    @TableField(value = "biz_point_id")
    private Long bizPointId;

    /**
     *
     */
    @TableField(value = "ip")
    private String ip;

    /**
     *
     */
    @TableField(value = "port")
    private String port;

    /**
     * 文字描述的位置
     */
    @TableField(value = "location")
    private String location;

    /**
     *
     */
    @TableField(value = "xmap_id")
    private String xmapId;


    /**
     * REF[map_main.map_id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * 0空闲 | 1充电中 | 4维护中 | 7预约中 | 8离线 | 9故障
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
