package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName log_robot_upgrade
 */
@TableName(value ="log_robot_upgrade")
@Data
public class LogRobotUpgrade implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "curr_version")
    private String currVersion;

    /**
     *
     */
    @TableField(value = "new_version")
    private String newVersion;

    /**
     *
     */
    @TableField(value = "execute_time")
    private Date executeTime;

    /**
     *
     */
    @TableField(value = "completed_time")
    private Date completedTime;

    /**
     *
     */
    @TableField(value = "spend_mills")
    private Long spendMills;

    /**
     * 0待更新 | 1更新中 | 3更新完成 | 9更新失败
     */
    @TableField(value = "upgrade_status")
    private String upgradeStatus;

    /**
     * 失败原因等
     */
    @TableField(value = "content")
    private String content;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
