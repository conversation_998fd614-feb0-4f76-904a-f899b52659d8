package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.honichang.common.annotation.Excel;
import com.honichang.dispactch.model.Position;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName biz_point
 */
@TableName(value ="biz_point")
@Data
public class BizPoint implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * dict-key: point_class_name
     */
    @Excel(name = "点位类型", prompt = "标记点,充电点,识别,红外测温,十字路口,出口,电梯,紧急洗眼器,停车位", dictType = "point_class_name")
    @TableField(value = "class_name")
    private String className;

    /**
     *
     */
    @Excel(name = "点位名称")
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * ⬇业务属性开始
     */
    @TableField(value = "dept_id")
    private Long deptId;

    @TableField(exist = false)
    @Excel(name = "所属部门")
    private String deptName;

    @TableField(exist = false)
    @Excel(name = "toolGroup")
    private String toolGroupName;

    @TableField(value = "tool_group_id")
    private Long toolGroupId;

    /**
     *
     */
    @TableField(value = "tool_id")
    @Excel(name = "toolId")
    private String toolId;

    /**
     * ⬇云台属性开始
     */
    @TableField(value = "ptz_angle_tb")
    private Double ptzAngleTb;

    /**
     *
     */
    @TableField(value = "ptz_angle_lr")
    private Double ptzAngleLr;

    /**
     *
     */
    @TableField(value = "camera_focus")
    private Double cameraFocus;

    /**
     *
     */
    @TableField(value = "camera_zoom")
    private Double cameraZoom;

    /**
     *
     */
    @TableField(value = "telescopic_rod_h")
    private Double telescopicRodH;

    /**
     * REF[iden_rule.id]
     */
    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     * dict-key: point_device_class
     */
    @TableField(value = "device_class")
    private String deviceClass;

    /**
     *
     */
    @TableField(value = "on_site_photo_url")
    private String onSitePhotoUrl;

    /**
     * （物理路径）
     */
    @TableField(value = "on_site_photo_path")
    private String onSitePhotoPath;

    /**
     *
     */
    @TableField(value = "marked_photo_url")
    private String markedPhotoUrl;

    /**
     * （物理路径）
     */
    @TableField(value = "marked_photo_path")
    private String markedPhotoPath;

    /**
     * REF[iden_model.id]
     */
    @TableField(value = "iden_model_id")
    private Long idenModelId;

    /**
     * REF[map_point.xmap_id]，手工录入，以此关联
     */
    @TableField(value = "xmap_id")
    @Excel(name = "点位ID")
    private String xmapId;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "map_point_id")
    private Long mapPointId;

    /**
     * 0导入后未编辑 | 1完成编辑
     */
    @TableField(value = "edit_status")
    private String editStatus;

    /**
     * 0正常| 1停用（未完成编辑的点位是不生效的）
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;
    @TableField(exist = false)
    private String taskInstanceNodeId;
    @TableField(exist = false)
    private String taskInstanceNodeStatus;
    @TableField(exist = false)
    private String taskInstanceNodeIdenTime;
    @TableField(exist = false)
    private String taskInstanceNodeAuditStatus;
    @TableField(exist = false)
    private String taskInstanceNodeRuleExpressionText;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<BizPointAiParam> bizPointAiParamList;

    /**
     * 对应的地图点位
     */
    @TableField(exist = false)
    private Position mapPoint;

}
