package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.honichang.common.annotation.Excel;
import com.honichang.common.core.domain.DataScopeEntity;
import com.honichang.dispactch.model.Position;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @TableName biz_point
 */
@TableName(value = "biz_point")
@Data
public class BizPoint extends DataScopeEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(exist = false)
    @Excel(name = "所属部门")
    private String deptName;

    @TableField(exist = false)
    @Excel(name = "toolGroup")
    private String toolGroupName;

    /**
     *
     */
    @TableField(value = "tool_id")
    @Excel(name = "toolId")
    private String toolId;

    /**
     *
     */
    @Excel(name = "点位名称")
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * dict-key: point_class_name<p/>
     * 点位类型: <br/>
     * LandMark(标记点) | ChargePoint(充电点) | IdenPoint(识别) | InfraredDetection(红外测温) | Crossroads(十字路口) | Exit(出口) | Elevator(电梯) | EyeWashStation(紧急洗眼器) | Parking(停车位)
     */
    @Excel(name = "点位类型", combo = {}, comboReadDict = true, dictType = "point_class_name")
    @TableField(value = "class_name")
    private String className;

    /**
     * REF[map_point.xmap_id]，手工录入，以此关联
     */
    @TableField(value = "xmap_id")
    @Excel(name = "点位ID")
    private String xmapId;


    /**
     * REF[iden_model.id]
     */
    @TableField(value = "iden_model_id")
    private Long idenModelId;

    @Excel(name = "点位模型", prompt = "类型为识别时必填")
    @TableField(exist = false)
    private String idenModelName;

    @Excel(name = "规则状态", prompt = "类型为识别或红外测温时必填")
    @TableField(exist = false)
    private String idenRuleName;

    /**
     * dict-key: point_device_class
     */
    @Excel(name = "设备类型", combo = {}, comboReadDict = true, dictType = "machine_type")
    @TableField(value = "device_class")
    private String deviceClass;

    /**
     * ⬇业务属性开始
     */
    @TableField(value = "dept_id")
    private Long deptId;

    @TableField(value = "tool_group_id")
    private Long toolGroupId;


    /**
     * 云台上下角度
     */
    @TableField(value = "ptz_angle_tb")
    private Double ptzAngleTb;

    /**
     * 云台左右角度
     */
    @TableField(value = "ptz_angle_lr")
    private Double ptzAngleLr;

    /**
     * 摄像头聚焦
     */
    @TableField(value = "camera_focus")
    private Double cameraFocus;

    /**
     * 摄像头变倍
     */
    @TableField(value = "camera_zoom")
    private Double cameraZoom;

    /**
     * 伸缩杆高度（毫米）
     */
    @TableField(value = "telescopic_rod_h")
    private Double telescopicRodH;

    /**
     * REF[iden_rule.id]
     */

    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     *
     */
    @TableField(value = "on_site_photo_url")
    private String onSitePhotoUrl;

    /**
     * （物理路径）
     */
    @TableField(value = "on_site_photo_path")
    private String onSitePhotoPath;

    /**
     *
     */
    @TableField(value = "marked_photo_url")
    private String markedPhotoUrl;

    /**
     * （物理路径）
     */
    @TableField(value = "marked_photo_path")
    private String markedPhotoPath;

    /**
     * REF[map_point.id]
     */
    @TableField(value = "map_point_id")
    private Long mapPointId;

    /**
     * 0导入后未编辑 | 1完成编辑
     */
    @TableField(value = "edit_status")
    private String editStatus;

    /**
     * 0正常| 1停用（未完成编辑的点位是不生效的）
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;
    @TableField(exist = false)
    private String taskInstanceNodeId;
    @TableField(exist = false)
    private String taskInstanceNodeStatus;
    @TableField(exist = false)
    private String taskInstanceNodeIdenTime;
    @TableField(exist = false)
    private String taskInstanceNodeAuditStatus;
    @TableField(exist = false)
    private String taskInstanceNodeRuleExpressionText;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<BizPointAiParam> bizPointAiParamList;

    /**
     * 对应的地图点位
     */
    @TableField(exist = false)
    private Position mapPoint;

    @TableField(exist = false)
    private List<BizPointIdenConf> bizPointIdenConfList;

    @TableField(exist = false)
    private BizPointAiParam bizPointAiParam;

    @TableField(exist = false)
    private String lastIdenConfInfo;

}
