package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName media_burner_camera
 */
@TableName(value ="media_burner_camera")
@Data
public class MediaBurnerCamera implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * REF[media_burner.id]
     */
    @TableField(value = "burner_id")
    private Long burnerId;

    /**
     * REF[robot_hardware.id]
     */
    @TableField(value = "robot_hardware_id")
    private Long robotHardwareId;

    /**
     * REF[robot_id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
