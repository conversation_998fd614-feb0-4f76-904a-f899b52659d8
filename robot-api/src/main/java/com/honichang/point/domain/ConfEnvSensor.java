package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName conf_env_sensor
 */
@TableName(value ="conf_env_sensor")
@Data
public class ConfEnvSensor implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "sensor_name")
    private String sensorName;

    /**
     * 气体|温度
     */
    @TableField(value = "sensor_type")
    private String sensorType;

    /**
     *
     */
    @TableField(value = "protocol")
    private String protocol;

    /**
     *
     */
    @TableField(value = "request_interface")
    private String requestInterface;

    /**
     * 1-Float
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     *
     */
    @TableField(value = "unit")
    private String unit;

    /**
     *
     */
    @TableField(value = "collection_mills")
    private Integer collectionMills;

    /**
     * REF[iden_rule.id]
     */
    @TableField(value = "iden_rule_id")
    private Long idenRuleId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
