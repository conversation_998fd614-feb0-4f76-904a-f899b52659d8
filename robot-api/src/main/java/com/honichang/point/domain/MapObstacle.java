package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName map_obstacle
 */
@TableName(value ="map_obstacle")
@Data
public class MapObstacle implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     *
     */
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * 单位：米
     */
    @TableField(value = "pos_x")
    private Double posX;

    /**
     * 单位：米
     */
    @TableField(value = "pos_y")
    private Double posY;

    /**
     * REF[map_main.id]
     */
    @TableField(value = "map_id")
    private Long mapId;

    /**
     * REF[map_path.id]
     */
    @TableField(value = "path_id")
    private Long pathId;

    /**
     * REF[map_path.xmap_id]
     */
    @TableField(value = "xmap_path_id")
    private String xmapPathId;

    /**
     * REF[robot.id]，除发现者外都自动避障，谁发现谁负责清障
     */
    @TableField(value = "discoverer")
    private Long discoverer;

    /**
     * escort陪同时发现 | task任务中发现 | task-out任务外发现
     */
    @TableField(value = "scene_type")
    private String sceneType;

    /**
     * REF[task_escort/task_instance.id]
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
