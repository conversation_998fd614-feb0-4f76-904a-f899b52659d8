package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName iden_model_param
 */
@TableName(value ="iden_model_param")
@Data
public class IdenModelParam implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 默认取iden_type_param，可修改
     */
    @TableField(value = "param_name")
    private String paramName;

    /**
     * 默认取iden_type的识别类型名称，可修改
     */
    @TableField(value = "iden_type_name")
    private String idenTypeName;

    /**
     * 1-Float | 2-枚举
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 不为空则为索引类的参数
     */
    @TableField(value = "seq")
    private Integer seq;

    /**
     * 手录
     */
    @TableField(value = "unit")
    private String unit;

    /**
     *
     */
    @TableField(value = "offset_value")
    private Double offsetValue;

    /**
     *
     */
    @TableField(value = "precision_num")
    private Integer precisionNum;

    /**
     *
     */
    @TableField(value = "scaling")
    private Double scaling;

    /**
     *
     */
    @TableField(value = "min_limit")
    private Double minLimit;

    /**
     *
     */
    @TableField(value = "max_limit")
    private Double maxLimit;

    /**
     * REF[iden_type.id]
     */
    @TableField(value = "iden_type_id")
    private Long idenTypeId;

    /**
     * REF[iden_model.id]
     */
    @TableField(value = "iden_model_id")
    private Long idenModelId;

    /**
     * REF[iden_model_param_group.id]
     */
    @TableField(value = "param_group_id")
    private Long paramGroupId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;


    @TableField(value = "iden_type_param_id")
    private String idenTypeParamId;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String idenName;

    @TableField(exist = false)
    private IdenTypeParam idenTypeParam;

}
