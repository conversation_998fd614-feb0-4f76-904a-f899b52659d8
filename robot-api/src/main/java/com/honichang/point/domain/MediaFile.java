package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体文件统一管理
 * @TableName media_file
 */
@TableName(value ="media_file")
@Data
public class MediaFile implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * vidio | photo
     */
    @TableField(value = "media_type")
    private String mediaType;

    /**
     * burner刻录机 | local本地存储
     */
    @TableField(value = "storage_type")
    private String storageType;

    /**
     * REF[media_burner.id]
     */
    @TableField(value = "burner_id")
    private Long burnerId;

    /**
     * REF[robot_hardware.id]
     */
    @TableField(value = "robot_hardware_id")
    private Long robotHardwareId;

    /**
     *
     */
    @TableField(value = "url")
    private String url;

    /**
     * ⬇文件基础属性
     */
    @TableField(value = "filename")
    private String filename;

    /**
     * 文件在刻录机/本地中的路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 分辨率(1920x1080)
     */
    @TableField(value = "resolution")
    private String resolution;

    /**
     *
     */
    @TableField(value = "capture_time")
    private Date captureTime;

    /**
     * ⬇视频专用属性，（秒）
     */
    @TableField(value = "video_duration")
    private Integer videoDuration;

    /**
     * H264,H265,MP4,AVI
     */
    @TableField(value = "video_format")
    private String videoFormat;

    /**
     * ⬇照片专用属性，'JPEG','PNG','BMP'
     */
    @TableField(value = "photo_format")
    private String photoFormat;

    /**
     *
     */
    @TableField(value = "thumbnail_url")
    private String thumbnailUrl;

    /**
     *
     */
    @TableField(value = "thumbnail_path")
    private String thumbnailPath;

    /**
     * ⬇业务相关属性；业务类型即表名
     */
    @TableField(value = "biz_type")
    private String bizType;

    /**
     * 表的主键
     */
    @TableField(value = "biz_id")
    private Long bizId;

    /**
     * 业务下有多个图片
     */
    @TableField(value = "seq")
    private Integer seq;

    /**
     * 0否 | 1是
     */
    @TableField(value = "multi_flag")
    private String multiFlag;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
