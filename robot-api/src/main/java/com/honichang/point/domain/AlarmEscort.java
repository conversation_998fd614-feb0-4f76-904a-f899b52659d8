package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName alarm_escort
 */
@TableName(value ="alarm_escort")
@Data
public class AlarmEscort implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1未到超时 | 2提前离开 | 9其他
     */
    @TableField(value = "alarm_type")
    private Integer alarmType;

    /**
     *
     */
    @TableField(value = "alarm_content")
    private String alarmContent;

    /**
     * REF[robot.id]
     */
    @TableField(value = "robot_id")
    private Long robotId;

    /**
     * REF[task_escort.id]
     */
    @TableField(value = "task_escort_id")
    private Long taskEscortId;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private String taskName;

    @TableField(exist = false)
    private String robotName;

    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;
}
