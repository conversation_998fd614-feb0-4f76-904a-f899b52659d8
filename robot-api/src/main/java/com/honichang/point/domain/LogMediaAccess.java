package com.honichang.point.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName log_media_access
 */
@TableName(value ="log_media_access")
@Data
public class LogMediaAccess implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * REF[media_file.id]
     */
    @TableField(value = "media_file_id")
    private Long mediaFileId;

    /**
     *
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * play | download | delete | preview
     */
    @TableField(value = "operation_type")
    private String operationType;

    /**
     *
     */
    @TableField(value = "client_ip")
    private String clientIp;

    /**
     * （秒）
     */
    @TableField(value = "access_duration")
    private Integer accessDuration;

    /**
     * 0正常| 1停用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 0代表存在 | 2代表删除
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
