package com.honichang.dispactch.model.enums;

import lombok.Getter;

@Getter
public enum RouteTypeEnum {

    BEZIER_CURVE("bezier_curve", "贝塞尔曲线"),
    STRAIGHT_LINE("straight_line", "直线"),
    CONVEX("convex", "凸弧线"),
    CONCAVE_ARC("concave_arc", "凹弧线");

    private final String code;
    private final String description;

    RouteTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static RouteTypeEnum fromCode(String code) {
        for (RouteTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
