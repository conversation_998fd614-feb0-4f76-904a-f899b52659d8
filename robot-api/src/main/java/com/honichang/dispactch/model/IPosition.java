package com.honichang.dispactch.model;

/**
 * 地图位置
 * <AUTHOR>
 */
public interface IPosition extends IPoint {

    /**
     * 地图点位id
     */
    String getXmapId();

    /**
     * 点位类型: LandMark(标记点) | ChargePoint(充电点) | IdenPoint(识别) | InfraredDetection(红外测温) | Crossroads(十字路口) | Exit(出口) | Elevator(电梯) | EyeWashStation(紧急洗眼器) | Parking(停车位)
     */
    String getClassName();

    /**
     * 是否是十字路口
     */
    boolean isCrossroads();

    Long getBizPointId();

    void setCrossroads(boolean b);
}
