package com.honichang.dispactch.model.enums;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import lombok.Getter;
import org.springframework.lang.NonNull;

/**
 * 工作模式：101自动模式 | 102遥控模式(后端遥控) | 103避障模式  | 201手动模式(厂家遥控器)
 */
@Getter
public enum RobotWorkModeEnum {

    AUTO_MODE(101, "自动模式"),

    REMOTE_CONTROL_MODE(102, "遥控模式"),

    AVOIDANCE_MODE(103, "避障模式"),

    MANUAL_MODE(201, "手动模式");

    private final int code;

    private final String description;

    RobotWorkModeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @NonNull
    public static RobotWorkModeEnum fromCode(int code) {
        for (RobotWorkModeEnum mode : values()) {
            if (mode.code == code) {
                return mode;
            }
        }
        throw new ServiceException("不支持的工作模式：" + code, HttpStatus.ERROR);
    }
}
