package com.honichang.dispactch.model.enums;

import lombok.Getter;

/**
 * 点位类型枚举
 * 点位类型: LandMark(标记点) | ChargePoint(充电点) | IdenPoint(识别) | InfraredDetection(红外测温) | Crossroads(十字路口) | Exit(出口) | Elevator(电梯) | EyeWashStation(紧急洗眼器) | Parking(停车位)
 *
 * <AUTHOR>
 */
@Getter
public enum PointClassNameEnum {

    LAND_MARK("LandMark", "标记点"),

    CHARGE_POINT("ChargePoint", "充电点"),

    IDEN_POINT("IdenPoint", "识别"),

    INFRARED_DETECTION("InfraredDetection", "红外测温"),

    CROSSROADS("Crossroads", "十字路口"),

    EXIT("Exit", "出口"),

    ELEVATOR("Elevator", "电梯"),

    EYE_WASH_STATION("EyeWashStation", "紧急洗眼器"),

    PARKING("Parking", "停车位");

    private final String code;

    private final String description;

    PointClassNameEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PointClassNameEnum fromCode(String code) {
        for (PointClassNameEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isCrossroads(String className) {
        return CROSSROADS.getCode().equals(className);
    }

}
