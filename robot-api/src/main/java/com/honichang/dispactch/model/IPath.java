package com.honichang.dispactch.model;

import java.io.Serializable;

public interface IPath extends Serializable {

    /**
     * 路径ID
     */
    String getXmapId();

    /**
     * 路径名称
     */
    String getInstanceName();

    /**
     * 路径类型
     * bezier_curve - 贝塞尔曲线
     * straight_line - 直线
     * convex - 凸弧线
     * concave_arc - 凹弧线
     */
    String getRouteType();

    /**
     * 弧形路径的弧度值(仅弧线类型需要),单位:rad
     */
    double getRadian();

    /**
     * 起点坐标
     */
    IPosition getStartPos();

    /**
     * 终点坐标
     */
    IPosition getEndPos();

    /**
     * 路径控制点1坐标
     */
    IPosition getCtrlPos1();

    /**
     * 路径控制点2坐标
     */
    IPosition getCtrlPos2();

    /**
     * 角度补偿
     */
    double getAngleCompensation();

    /**
     * 方向: forward 表示正走，backward 表示倒走
     * 往返可走的路径是两条线，一条正走，一条倒走，不存在both类型
     */
    String getDirection();

    /**
     * 路径长度，单位同坐标单位（m）
     */
    double getLength();


    // 👇默认方法开始

    /**
     * 判断点是否是路径的起点
     * @param point 点
     * @return 是否是起点
     */
    default boolean isStartPoint(IPosition point) {
        if (point == null) return false;
        return point.equalCo(getStartPos());
    }

    /**
     * 判断点是否是路径的终点
     * @param point 点
     * @return 是否是终点
     */
    default boolean isEndPoint(IPosition point) {
        if (point == null) return false;
        return point.equalCo(getEndPos());
    }

    /**
     * 判断路径是否完全相等: xmapId, startPos, endPos
     * @param other 另一个路径
     * @return 是否相等
     */
    default boolean equals(IPath other) {
        if (this == other) return true;
        if (other == null) return false;
        return getXmapId().equals(other.getXmapId()) &&
                getStartPos().equalCo(other.getStartPos()) &&
                getEndPos().equalCo(other.getEndPos());
    }

    /**
     * 判断是否是反向路径
     * @param other 另一个路径
     * @return 是否是反向路径
     */
    default boolean isReversePath(IPath other) {
        if (other == null) return false;
        return getXmapId().equals(other.getXmapId()) &&
                getStartPos().equalCo(other.getEndPos()) &&
                getEndPos().equalCo(other.getStartPos());
    }

    /**
     * 判断是否是相等或反向路径
     * @param other 另一个路径
     * @return 是否是相等或反向路径
     */
    default boolean equalsOrReverse(IPath other) {
        return getXmapId().equals(other.getXmapId());
    }
}
