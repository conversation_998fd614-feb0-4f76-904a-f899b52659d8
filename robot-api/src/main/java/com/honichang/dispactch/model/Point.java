package com.honichang.dispactch.model;

import lombok.Data;

@Data
public class Point implements IPoint {

    protected double x;

    protected double y;

    /**
     * 角度（单位弧度）
     */
    protected double angle;

    public Point() {
    }

    public Point(double x, double y) {
        this.x = x;
        this.y = y;
        this.angle = 0.0;
    }

    public Point(double x, double y, double angle) {
        this.x = x;
        this.y = y;
        this.angle = angle;
    }

}
