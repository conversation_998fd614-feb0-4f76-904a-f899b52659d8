package com.honichang.dispactch.model;

import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class Task implements Serializable {

    private volatile TaskInstance taskInstance;

    private volatile List<TaskNode> taskNodes;

    public Task() {}

    public Task(TaskInstance taskInstance) {
        this.taskInstance = taskInstance;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 未完成的节点（排除已完成和不可达的）
     */
    public List<TaskInstanceNode> getUnfinishedNodes() {
        return taskNodes.stream()
                .filter(n -> n.isPending() || n.isExecuting())
                .map(TaskNode::getTaskInstanceNode).collect(Collectors.toList());
    }
}
