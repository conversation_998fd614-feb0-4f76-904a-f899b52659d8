package com.honichang.dispactch.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.honichang.dispactch.common.ChainSerializer;
import com.honichang.dispactch.model.base.ChainBase;
import com.honichang.point.domain.TaskInstance;
import lombok.Getter;
import lombok.Setter;

/**
 * 任务链
 * @param <E>
 * <AUTHOR>
 */
@Getter
@Setter
@JsonSerialize(using = ChainSerializer.class)
public final class TaskChain<E extends TaskNode> extends ChainBase<E> {

    private TaskInstance taskInstance;

    @Override
    public TaskChain<E> clone() {
        TaskChain<E> pc = new TaskChain<>();
        super._clone(pc);
        pc.setTaskInstance(taskInstance);
        return pc;
    }

    private static final long serialVersionUID = 1L;
}
