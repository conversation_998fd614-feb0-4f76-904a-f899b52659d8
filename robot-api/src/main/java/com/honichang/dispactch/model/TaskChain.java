package com.honichang.dispactch.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.honichang.dispactch.common.ChainSerializer;
import com.honichang.dispactch.model.base.ChainBase;

/**
 * 任务链
 * @param <E>
 * <AUTHOR>
 */
@JsonSerialize(using = ChainSerializer.class)
public final class TaskChain<E extends TaskNode> extends ChainBase<E> {

    @Override
    public TaskChain<E> clone() {
        TaskChain<E> pc = new TaskChain<>();
        super._clone(pc);
        return pc;
    }

    private static final long serialVersionUID = 1L;
}
