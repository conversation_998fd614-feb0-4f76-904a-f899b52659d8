package com.honichang.dispactch.model;

/**
 * 坐标点接口
 * <AUTHOR>
 */
public interface IPoint extends java.io.Serializable {

    double getX();

    double getY();

    /**
     * 判断2D坐标是否相等
     * @param other 另一个坐标
     * @return 是否相等
     */
    default boolean equalCo(IPoint other) {
        if (this == other) return true;
        if (other == null) return false;
        return Double.compare(this.getX(), other.getX()) == 0 && Double.compare(this.getY(), other.getY()) == 0;
    }
}
