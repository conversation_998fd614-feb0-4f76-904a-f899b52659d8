package com.honichang.dispactch.model;

import com.honichang.common.constant.Constants;

/**
 * 坐标点接口
 *
 * <AUTHOR>
 */
public interface IPoint extends java.io.Serializable {

    double getX();

    double getY();

    /**
     * 朝向角rad
     */
    double getAngle();

    /**
     * 判断2D坐标是否相等
     *
     * @param other 另一个坐标
     * @return 是否相等
     */
    default boolean equalCo(IPoint other) {
        if (this == other) return true;
        if (other == null) return false;
        return Math.abs(getX() - other.getX()) < Constants.EPSILON &&
                Math.abs(getY() - other.getY()) < Constants.EPSILON &&
                Math.abs(getAngle() - other.getAngle()) < Constants.EPSILON;
    }
}
