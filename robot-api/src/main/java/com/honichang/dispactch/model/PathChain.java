package com.honichang.dispactch.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.honichang.common.constant.Constants;
import com.honichang.dispactch.common.ChainSerializer;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.base.ChainBase;
import com.honichang.dispactch.model.enums.DirectionEnum;

/**
 * 路径链
 *
 * @param <E>
 * <AUTHOR>
 */
@JsonSerialize(using = ChainSerializer.class)
public final class PathChain<E extends IPath> extends ChainBase<E> {

    /**
     * 路径链的总长度，单位同坐标系
     */
    public double getLength() {
        double pathLength = 0.0;
        for (Entry e = head.get(); e != null; e = e.next) {
            pathLength += e.data.getLength();
        }
        return pathLength;
    }

    /**
     * 计算路径链总长度（带系数），用于比较路径优先级
     * 1.倒走系数加成
     * 2.路径重叠系数加成（与其他机器人的预分配路径重叠）
     *
     * @param currentRobotId 当前机器人ID
     */
    public double getLengthWithFactor(long currentRobotId) {
        double pathLength = 0.0;

        for (Entry e = head.get(); e != null; e = e.next) {
            // 倒走系数
            double l = e.data.getLength() * (
                    DirectionEnum.BACKWARD.getCode().equals(e.data.getDirection()) ?
                            Constants.BACKWARD_LENGTH_FACTOR :
                            1.0);

            // 重叠系数
            for (RobotContext rc : RobotContext.values()) {
                if (rc.getRobotId() == currentRobotId) continue;
                if (rc.getPreAllocatedPaths().existsIgnoreReverse(e.data)) {
                    l *= Constants.PATH_OVERLAP_FACTOR;
                }
            }

            pathLength += l;
        }
        return pathLength;
    }

    /**
     * 计算路径链总长度（带系数），用于比较路径优先级
     * 1.倒走系数加成
     */
    public double getLengthWithFactor() {
        double pathLength = 0.0;

        for (Entry e = head.get(); e != null; e = e.next) {
            // 倒走系数
            pathLength += e.data.getLength() * (
                    DirectionEnum.BACKWARD.getCode().equals(e.data.getDirection()) ?
                            Constants.BACKWARD_LENGTH_FACTOR :
                            1.0);
        }
        return pathLength;
    }

    /**
     * 判断路径r是否在当前任务链中（严格匹配xmapId、start、end）
     *
     * @param r 路径
     * @return 是否存在
     */
    @Override
    public boolean exists(E r) {
        if (r == null) return false;
        for (Entry e = head.get(); e != null; e = e.next) {
            if (e.data.equals(r)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断路径r是否在当前任务链中（宽松匹配xmapId、start、end，即允许反向路径）
     *
     * @param r 路径
     * @return 是否存在
     */
    public boolean existsIgnoreReverse(E r) {
        if (r == null) return false;
        for (Entry e = head.get(); e != null; e = e.next) {
            if (e.data.equalsOrReverse(r)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public PathChain<E> clone() {
        PathChain<E> pc = new PathChain<>();
        super._clone(pc);
        return pc;
    }

    private static final long serialVersionUID = 1L;
}
