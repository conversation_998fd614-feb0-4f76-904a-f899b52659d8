package com.honichang.dispactch.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.honichang.dispactch.common.ChainSerializer;
import com.honichang.dispactch.model.base.ChainBase;

import java.util.concurrent.atomic.AtomicReference;

/**
 * 路径链
 * @param <E>
 * <AUTHOR>
 */
@JsonSerialize(using = ChainSerializer.class)
public final class PathChain<E extends IPath> extends ChainBase<E> {

    /**
     * 路径链的总长度
     */
    private final transient AtomicReference<Double> pathLength = new AtomicReference<>(0.0);

    /**
     * 路径链的总长度，单位同坐标系
     */
    public double getPathLength() {
        return pathLength.get();
    }

    @Override
    public void append(ChainBase<E> other) {
        if (!(other instanceof PathChain)) {
            return;
        }
        PathChain<E> pc = (PathChain<E>) other;
        super.append(pc);
        pathLength.set(pathLength.get() + pc.getPathLength());
    }

    @Override
    public void prepend(ChainBase<E> other) {
        if (!(other instanceof PathChain)) {
            return;
        }
        PathChain<E> pc = (PathChain<E>) other;
        super.prepend(pc);
        pathLength.set(pc.getPathLength() + pathLength.get());
    }

    @Override
    public void add(E r) {
        if (r == null) return;
        super.add(r);
        pathLength.set(pathLength.get() + r.getLength());
    }

    @Override
    public E popTail() {
        E e = super.popTail();
        if (e != null) {
            pathLength.set(pathLength.get() - e.getLength());
        }
        return e;
    }

    @Override
    public E popHead() {
        E e = super.popHead();
        if (e != null) {
            pathLength.set(pathLength.get() - e.getLength());
        }
        return e;
    }

    @Override
    public PathChain<E> clone() {
        PathChain<E> pc = new PathChain<>();
        super._clone(pc);
        pc.pathLength.set(this.pathLength.get());
        return pc;
    }

    @Override
    public void clear() {
        super.clear();
        pathLength.set(0.0);
    }

    /**
     * 避免内存泄漏与溢出，这里必须采用手动序列化形式实现链表的序列化
     */
    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();
        // Write out size
        s.writeDouble(pathLength.get());
    }

    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();
        pathLength.set(s.readDouble());
    }
}
