package com.honichang.dispactch.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class Position extends Point implements IPosition {

    /**
     * XMAP地图点位ID
     */
    private String xmapId;

    /**
     * 点位类型
     */
    private String className;

    /**
     * 点位名称
     */
    private String instanceName;

    public Position() {
    }

    /**
     * @param x 机器人坐标
     * @param y 机器人坐标
     */
    public Position(double x, double y) {
        super(x, y);
    }

    /**
     * @param xmapId       机器人地图点位ID
     * @param x            机器人坐标
     * @param y            机器人坐标
     * @param angle        点位角度
     * @param className    点位类型
     * @param instanceName 点位名称
     */
    public Position(String xmapId, double x, double y, double angle, String className, String instanceName) {
        super(x, y, angle);
        this.xmapId = xmapId;
        this.className = className;
        this.instanceName = instanceName == null ? "" : instanceName;
    }
}
