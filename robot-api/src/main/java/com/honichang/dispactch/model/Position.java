package com.honichang.dispactch.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class Position extends Point implements IPosition {

    /**
     * 机器人点位ID
     */
    private String xmapId;

    /**
     * 点位名称
     */
    private String instanceName;

    /**
     * 角度（单位弧度）
     */
    private Double angle;

    public Position() {
    }

    /**
     * @param x    机器人坐标
     * @param y    机器人坐标
     */
    public Position(double x, double y) {
        super(x, y);
    }

    /**
     * @param xmapId           机器人地图点位ID
     * @param x            机器人坐标
     * @param y            机器人坐标
     * @param angle        点位角度
     * @param instanceName 点位名称
     */
    public Position(String xmapId, double x, double y, Double angle, String instanceName) {
        this(x, y);
        this.xmapId = xmapId;
        this.angle = angle;
        this.instanceName = instanceName == null ? "" : instanceName;
    }
}
