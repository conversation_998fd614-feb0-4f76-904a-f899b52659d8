package com.honichang.dispactch.model;

import lombok.Data;

import java.util.Objects;

@Data
public class Position extends Point implements IPosition {

    private int index;

    /**
     * XMAP地图点位ID
     */
    private String xmapId;

    /**
     * 业务点位ID
     */
    private Long bizPointId;

    /**
     * 点位类型
     */
    private String className;

    /**
     * 点位名称
     */
    private String instanceName;

    /**
     * 是否是十字路口
     */
    private boolean isCrossroads;

    public Position() {
    }

    /**
     * @param x 机器人坐标
     * @param y 机器人坐标
     */
    public Position(double x, double y) {
        super(x, y);
    }

    public Position(double x, double y, double angle) {
        super(x, y, angle);
    }

    /**
     * @param xmapId       机器人地图点位ID
     * @param x            机器人坐标
     * @param y            机器人坐标
     * @param angle        点位角度
     * @param className    点位类型
     * @param instanceName 点位名称
     */
    public Position(String xmapId, double x, double y, double angle, String className, String instanceName, boolean isCrossroads, Long bizPointId) {
        this(x, y, angle);
        this.xmapId = xmapId;
        this.className = className;
        this.instanceName = instanceName == null ? "" : instanceName;
        this.isCrossroads = isCrossroads;
        this.bizPointId = bizPointId;
    }

    @Override
    public boolean equals(IPoint other) {
        if (!super.equals(other)) {
            return false;
        }
        return Objects.equals(xmapId, ((Position) other).xmapId);
    }

    @Override
    public int hashCode() {
        // 增加xmapId的hashcode
        if (xmapId != null) {
            return Objects.hash(xmapId, super.hashCode());
        } else {
            return super.hashCode();
        }
    }

    @Override
    public Position clone() {
        return (Position) super.clone();
    }
}
