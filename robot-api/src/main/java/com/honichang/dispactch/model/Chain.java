package com.honichang.dispatch.model.base;

import java.io.Serializable;
import java.util.List;

/**
 * 链结构接口
 * @param <E>
 *
 * <AUTHOR>
 */
public interface Chain<E> extends Serializable {

    /**
     * 链中包含了多少成员
     */
    int size();

    /**
     * 获取路径链的头，null 表示空链
     */
    E getHead();

    /**
     * 获取路径链的尾，null 表示空链
     */
    E getTail();

    /**
     * 获取链中指定索引的成员
     * @param i 索引
     * @throws IndexOutOfBoundsException 如果索引越界
     */
    E get(int i);

    /**
     * 查找是否已经存在
     */
    boolean exists(E r);

    /**
     * 获取链中所有成员
     */
    List<E> getAll();

    /**
     * Appends the specified Route to the list
     */
    void add(E r);

    /**
     * pop head
     */
    E popHead();

    /**
     * pop tail
     */
    E popTail();

    /**
     * 避免链被污染，克隆一份新的
     */
    void cloneTo(Chain<E> target);

    /**
     * 清空链
     */
    void clear();
}
