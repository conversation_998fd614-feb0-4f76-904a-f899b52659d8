package com.honichang.dispactch.model;

import com.honichang.dispactch.util.MapCalcUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NewPoint extends Point implements INewPoint {

    private double newX;

    private double newY;

    public NewPoint() {}

    public NewPoint(double x, double y, double minX, double maxY, double scale, double translationX, double translationY) {
        super(x, y);
        this.newX = MapCalcUtil.calcNewCoordinateXToPt(x, minX, scale, translationX);
        this.newY = MapCalcUtil.calcNewCoordinateYToPt(y, maxY, scale, translationY);
    }
}
