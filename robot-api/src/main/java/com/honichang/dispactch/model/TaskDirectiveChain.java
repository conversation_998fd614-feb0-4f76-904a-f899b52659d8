package com.honichang.dispactch.model;

import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.base.ChainBase;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务指令链（最终的）
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TaskDirectiveChain extends ChainBase<TaskDirective> {

    private volatile transient TaskInstance taskInstance;

    @Override
    public void clear() {
        super.clear();
        this.taskInstance = null;
    }

    @Override
    public Chain<TaskDirective> clone() {
        TaskDirectiveChain tdc = new TaskDirectiveChain();
        super._clone(tdc);
        return tdc;
    }

    /**
     * 避免内存泄漏与溢出，这里必须采用手动序列化形式实现链表的序列化
     */
    private void writeObject(java.io.ObjectOutputStream s)
            throws java.io.IOException {
        // Write out any hidden serialization magic
        s.defaultWriteObject();
        s.writeObject(taskInstance);
    }

    private void readObject(java.io.ObjectInputStream s)
            throws java.io.IOException, ClassNotFoundException {
        // Read in any hidden serialization magic
        s.defaultReadObject();
        taskInstance = (TaskInstance) s.readObject();
    }

    private static final long serialVersionUID = 1L;

    /**
     * 1执行中 | 3完成 | 4暂停 | 8终止
     */
    public String getStatus() {
        return taskInstance.getStatus();
    }

    public boolean exists(TaskInstanceNode pn) {
        AtomicBoolean b = new AtomicBoolean(false);
        this.forEach(tdc -> {
            if (b.get()) return;
            TaskInstanceNode node = tdc.getTaskNode().getTaskInstanceNode();
            if (node.getId() == null ? node.equals(pn) : node.getId().equals(pn.getId())) {
                b.set(true);
            }
        });
        return b.get();
    }
}
