package com.honichang.dispactch.model.enums;

import lombok.Getter;

/**
 * 陪同任务推送模板枚举<p/>
 * 无法开始陪同任务 | 等待访客超时 | 陪同任务异常终止 | 访客中途离开 | 陪同任务完成
 */
@Getter
public enum TaskEscortPushTemplateEnum {

    /**
     * 等待访客超时
     */
    ESCORT_TEMP_WAIT_TIMEOUT(4, "等待访客超时"),

    /**
     * 陪同任务异常终止，比如突然切换到手动模式
     */
    ESCORT_TEMP_ABORT(5, "陪同任务异常终止"),

    /**
     * 访客中途离开
     */
    ESCORT_TEMP_LEAVE(6, "访客中途离开"),

    /**
     * 陪同任务完成
     */
    ESCORT_TEMP_COMPLETED(7, "陪同任务完成"),

    /**
     * 无法开始陪同任务
     */
    ESCORT_TEMP_CANNOT_START(8, "无法开始陪同任务");

    private final int code;

    private final String description;

    TaskEscortPushTemplateEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
