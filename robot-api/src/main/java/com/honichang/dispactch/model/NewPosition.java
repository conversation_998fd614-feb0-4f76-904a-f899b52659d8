package com.honichang.dispactch.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NewPosition extends NewPoint implements INewPosition {

    /**
     * 机器人点位ID
     */
    private String xmapId;

    /**
     * 点位名称
     */
    private String instanceName;

    /**
     * 角度（单位弧度）
     */
    private Double angle;

    public NewPosition() {}

    public NewPosition(double x, double y, double minX, double maxY, double scale, double translationX, double translationY) {
        super(x, y, minX, maxY, scale, translationX, translationY);
    }

    public NewPosition(double x, double y, double minX, double maxY, double scale, double translationX, double translationY, String xmapId, String instanceName, Double angle) {
        super(x, y, minX, maxY, scale, translationX, translationY);
        this.xmapId = xmapId;
        this.instanceName = instanceName;
        this.angle = angle;
    }
}
