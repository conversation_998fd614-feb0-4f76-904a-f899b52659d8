package com.honichang.dispactch.model;

import com.honichang.point.domain.MapObstacle;
import lombok.Data;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * 障碍
 *
 * <AUTHOR>
 */
@Data
public class Obstacle {

    /**
     * 点位ID
     */
    private Long id;

    /**
     * xmap路径ID
     */
    private String xmapPathId;
//
//    /** 起点ID */
//    private String startPosXmapId;
//
//    /** 终点ID */
//    private String endPosXmapId;

    /**
     * 障碍点名称
     */
    private String instanceName;

    /**
     * 原始点位坐标X
     */
    private Double posX;

    /**
     * 原始点位坐标Y
     */
    private Double posY;

    private Long mapId;

    /**
     * 发现者（机器人）
     */
    private Long discoverer;

    /**
     * 场景类型: escort陪同 | task任务 | task-out任务外
     */
    private String sceneType;

    /**
     * 发现障碍的任务ID（sceneType=task）
     */
    private Long taskId;

    public static Obstacle convertFrom(@NonNull MapObstacle mapObstacle) {
        Obstacle obstacle = new Obstacle();
        obstacle.id = mapObstacle.getId();
        obstacle.xmapPathId = mapObstacle.getXmapPathId();
        obstacle.instanceName = mapObstacle.getInstanceName();
        obstacle.posX = mapObstacle.getPosX();
        obstacle.posY = mapObstacle.getPosY();
        obstacle.mapId = mapObstacle.getMapId();
        obstacle.discoverer = mapObstacle.getDiscoverer();
        obstacle.sceneType = mapObstacle.getSceneType();
        obstacle.taskId = mapObstacle.getTaskId();
        return obstacle;
    }

    public static List<Obstacle> convertFrom(@NonNull List<MapObstacle> mapObstacles) {
        List<Obstacle> obstacles = new ArrayList<>(mapObstacles.size());
        for (MapObstacle obstacle : mapObstacles) {
            obstacles.add(Obstacle.convertFrom(obstacle));
        }
        return obstacles;
    }

}
