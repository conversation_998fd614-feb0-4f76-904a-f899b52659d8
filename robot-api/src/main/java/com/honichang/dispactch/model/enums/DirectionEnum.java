package com.honichang.dispactch.model.enums;

import lombok.Getter;

@Getter
public enum DirectionEnum {
    FORWARD("forward", "正走"),
    BACKWARD("backward", "倒走");

    private final String code;
    private final String description;

    DirectionEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static DirectionEnum fromCode(String code) {
        for (DirectionEnum direction : values()) {
            if (direction.code.equals(code)) {
                return direction;
            }
        }
        return null;
    }
}
