package com.honichang.dispactch.model;

import com.honichang.point.domain.MapPath;
import lombok.Data;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

@Data
public class Path implements IPath {

    /** 地图ID **/
    private long mapId;
    /** 路径ID，REF[map_path.id] **/
    private Long pathId;
    /** XMAP路径ID **/
    private String xmapId;
    /** XMAP路径名称 **/
    private String instanceName;
    /** 路径类型: bezier_curve贝塞尔曲线|straight_line直线|convex凸弧线|concave_arc凹弧线 **/
    private String routeType;
    /** 弧形路径的弧度值(仅弧线类型需要),单位:rad **/
    private double radian;
    /** XMAP起始 **/
    private IPosition startPos;
    private IPosition endPos;
    private IPosition ctrlPos1;
    private IPosition ctrlPos2;
    /** 角度补偿 **/
    private double angleCompensation;
    /** 方向: forward正走 | backward倒走 **/
    private String direction;
    /** start至end的长度，单位：米 **/
    private double length;

    /**
     * 机器人是否倒着走，非路径的方向
     */
    private boolean isBackward;

    public static Path convertFrom(@NonNull MapPath path) {
        Path p = new Path();
        p.pathId = path.getId();
        p.mapId = path.getMapId();
        p.xmapId = path.getXmapId();
        p.instanceName = path.getInstanceName();
        p.routeType = path.getRouteType();
        p.radian = path.getRadian();
        p.startPos = new Position(path.getStartX(), path.getStartY());
        p.endPos = new Position(path.getEndX(), path.getEndY());
        p.ctrlPos1 = new Position(path.getControlX1(), path.getControlY1());
        p.ctrlPos2 = new Position(path.getControlX2(), path.getControlY2());
        p.angleCompensation = path.getAngleCompensation();
        p.direction = path.getDirection();
        p.length = path.getRealLength();
        return p;
    }

    public static List<Path> convertFrom(@NonNull List<MapPath> paths) {
        List<Path> list = new ArrayList<>(paths.size());
        for (MapPath path : paths) {
            list.add(convertFrom(path));
        }
        return list;
    }

}
