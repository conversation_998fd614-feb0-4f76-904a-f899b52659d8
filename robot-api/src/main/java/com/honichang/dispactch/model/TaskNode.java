package com.honichang.dispactch.model;

import com.honichang.point.domain.TaskInstanceNode;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 任务节点
 *
 * <AUTHOR>
 */
@Data
public class TaskNode implements Serializable {

    /**
     * 任务实例：<br/>
     * REF[task_instance.id]<p/>
     * <p>
     * 状态：0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警<br/>
     * 任务开始执行并规划好后，就一次性创建好并提交DB
     */
    private volatile TaskInstanceNode taskInstanceNode;

    // ↓状态判断
    /**
     *  3巡检正常 | 7识别异常 | 9告警
     */
    public static final String COMPLETED_STATUS_ARRAY = "|3|7|9|";

    public TaskNode() {}

    public TaskNode(TaskInstanceNode n) {
        this.taskInstanceNode = n;
    }

    public String getXmapPointId() {
        return taskInstanceNode.getBizPoint().getXmapId();
    }

    public Position getMapPoint() {
        return taskInstanceNode.getBizPoint().getMapPoint();
    }

    /**
     * 判断节点是否已完成
     */
    public boolean isCompleted() {
        String s = taskInstanceNode.getStatus();
        return COMPLETED_STATUS_ARRAY.contains('|' + s + '|');
    }

    /**
     * 判断节点是否正在执行
     */
    public boolean isExecuting() {
        return "1".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否待执行
     */
    public boolean isPending() {
        return "0".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否不可达
     */
    public boolean isSkipped() {
        return "8".equals(taskInstanceNode.getStatus());
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        if (taskInstanceNode == null) return false;
        TaskNode taskNode = (TaskNode) o;
        if (taskInstanceNode.getId() == null) {
            return Objects.equals(taskInstanceNode, taskNode.taskInstanceNode);
        } else {
            return Objects.equals(taskInstanceNode.getId(), taskNode.taskInstanceNode.getId());
        }
    }

    @Override
    public int hashCode() {
        if (taskInstanceNode == null) return 0;
        if (taskInstanceNode.getId() == null) {
            return Objects.hashCode(taskInstanceNode);
        } else {
            return Objects.hashCode(taskInstanceNode.getId());
        }
    }

    private static final long serialVersionUID = 1L;
}
