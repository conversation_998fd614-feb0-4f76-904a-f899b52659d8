package com.honichang.dispactch.model;

import com.honichang.point.domain.TaskInstanceNode;
import lombok.Data;

import java.io.Serializable;

/**
 * 任务节点
 *
 * <AUTHOR>
 */
@Data
public class TaskNode implements Serializable {

    /**
     * 任务实例：<br/>
     * REF[task_instance.id]<p/>
     * <p>
     * 状态：0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警<br/>
     * 任务开始执行并规划好后，就一次性创建好并提交DB
     */
    private TaskInstanceNode taskInstanceNode;

    // ↓状态判断
    /**
     *  3巡检正常 | 7识别异常 | 9告警
     */
    public static final String COMPLETED_STATUS_ARRAY = "|3|7|9|";

    /**
     * 判断节点是否已完成
     */
    public boolean isCompleted() {
        String s = taskInstanceNode.getStatus();
        return COMPLETED_STATUS_ARRAY.contains('|' + s + '|');
    }

    /**
     * 判断节点是否正在执行
     */
    public boolean isExecuting() {
        return "1".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否待执行
     */
    public boolean isPending() {
        return "0".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否不可达
     */
    public boolean isSkipped() {
        return "8".equals(taskInstanceNode.getStatus());
    }

    private static final long serialVersionUID = 1L;
}
