package com.honichang.dispactch.model;

import com.honichang.point.domain.TaskInstanceNode;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点
 *
 * <AUTHOR>
 */
@Data
public class TaskNode implements Serializable {
    /**
     * 任务定义节点ID
     **/
    private long taskDefNodeId;
    /**
     * 点位类型
     **/
    private String className;
    /**
     * 点位名称
     **/
    private String instanceName;
    /**
     * 地图点位ID
     **/
    private long mapPointId;
    /**
     * XMAP地图点位ID
     **/
    private String xmapId;
    /**
     * 所属部门ID
     **/
    private long deptId;
    /**
     * toolgroupId
     **/
    private long toolGroupId;
    /**
     * toolid
     **/
    private String toolId;

    // ↓云台配置
    /**
     * 云台角度TB
     **/
    private double ptzAngleTb;
    /**
     * 云台角度LR
     **/
    private double ptzAngleLr;
    /**
     * 摄像头焦距
     **/
    private double cameraFocus;
    /**
     * 摄像头变倍
     **/
    private double cameraZoom;
    /**
     * 云台升降杆高度
     **/
    private double telescopicRodH;
    /**
     * 识别规则ID
     **/
    private long idenRuleId;

    // ↓任务实例
    /**
     * 任务实例：<br/>
     * REF[task_instance.id]<p/>
     * <p>
     * 状态：0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警<br/>
     * 任务开始执行并规划好后，就一次性创建好并提交DB
     */
    private TaskInstanceNode taskInstanceNode;


    /**
     * 执行顺序：用于排序<br/>
     * 不可达为：99999
     **/
    private int sequence;

    /**
     * 预计执行时间（分钟）
     **/
    private Integer estimatedTime;

    /**
     * 实际开始时间
     **/
    private Date actualStartTime;
    /**
     * 实际结束时间
     **/
    private Date actualEndTime;

    /**
     * 执行结果
     **/
    private String result;
    /**
     * 失败原因
     **/
    private String failureReason;
    /**
     * 节点参数（JSON格式）
     **/
    private String parameters;


    // ↓状态判断
    public static final String COMPLETED_STATUS_ARRAY = "|3|7|9|";

    /**
     * 判断节点是否已完成
     */
    public boolean isCompleted() {
        String s = taskInstanceNode.getStatus();
        return COMPLETED_STATUS_ARRAY.contains('|' + s + '|');
    }

    /**
     * 判断节点是否正在执行
     */
    public boolean isExecuting() {
        return "1".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否待执行
     */
    public boolean isPending() {
        return "0".equals(taskInstanceNode.getStatus());
    }

    /**
     * 判断节点是否不可达
     */
    public boolean isSkipped() {
        return "8".equals(taskInstanceNode.getStatus());
    }

    private static final long serialVersionUID = 1L;
}
