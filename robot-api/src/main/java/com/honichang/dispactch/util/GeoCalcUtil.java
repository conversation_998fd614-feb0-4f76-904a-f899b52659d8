package com.honichang.dispactch.util;

import com.honichang.dispactch.model.IPoint;

/**
 * 几何计算工具类
 *
 * <AUTHOR>
 */
public class GeoCalcUtil {

    // region 弧线

    /**
     * 计算两点之间的弧线长度
     *
     * @param p0       起点坐标
     * @param p1       终点坐标
     * @param thetaRad 弧度值（rad）
     * @return 弧线长度
     * @throws IllegalArgumentException 如果thetaRad导致除零错误（例如两点重合且theta是2π的倍数）
     */
    public static double calculateArcLength(IPoint p0, IPoint p1, double thetaRad) {
        // 计算两点之间的弦长（欧几里得距离）
        double dx = p1.getX() - p0.getX();
        double dy = p1.getY() - p0.getY();
        double chordLength = Math.sqrt(dx * dx + dy * dy);

        // 处理theta为0或非常接近0的情况（弧长近似弦长）
        if (Math.abs(thetaRad) < 1e-10) {
            return chordLength;
        }

        // 检查sin(theta/2)是否接近0（防止除零错误）
        double halfTheta = thetaRad / 2;
        double sinHalfTheta = Math.sin(halfTheta);
        if (Math.abs(sinHalfTheta) < 1e-10) {
            throw new IllegalArgumentException(
                    "Theta results in division by zero (points coincide and theta is multiple of 2π)"
            );
        }

        // 计算半径
        double radius = chordLength / (2 * sinHalfTheta);

        // 计算弧长
        return radius * thetaRad;
    }

    // endregion

    // region 贝塞尔曲线

    /**
     * 计算贝塞尔曲线长度
     *
     * @param p0 起点坐标
     * @param p1 控制点1坐标
     * @param p2 控制点2坐标
     * @param p3 终点坐标
     * @return 曲线长度
     */
    public static double calculateBezierCurveLength2(IPoint p0, IPoint p1, IPoint p2, IPoint p3) {
        // 使用线段系列模拟贝塞尔曲线
        int numPoints = 100;
        double totalLength = 0;

        for (int i = 0; i < numPoints - 1; i++) {
            double t = (double) i / numPoints;
            double tNext = (double) (i + 1) / numPoints;

            double x = Math.pow(1 - t, 3) * p0.getX() + 3 * Math.pow(1 - t, 2) * t * p1.getX() + 3 * (1 - t) * Math.pow(t, 2) * p2.getX() + Math.pow(t, 3) * p3.getX();
            double y = Math.pow(1 - t, 3) * p0.getY() + 3 * Math.pow(1 - t, 2) * t * p1.getY() + 3 * (1 - t) * Math.pow(t, 2) * p2.getY() + Math.pow(t, 3) * p3.getY();

            double xNext = Math.pow(1 - tNext, 3) * p0.getX() + 3 * Math.pow(1 - tNext, 2) * tNext * p1.getX() + 3 * (1 - tNext) * Math.pow(tNext, 2) * p2.getX() + Math.pow(tNext, 3) * p3.getX();
            double yNext = Math.pow(1 - tNext, 3) * p0.getY() + 3 * Math.pow(1 - tNext, 2) * tNext * p1.getY() + 3 * (1 - tNext) * Math.pow(tNext, 2) * p2.getY() + Math.pow(tNext, 3) * p3.getY();

            totalLength += Math.sqrt(Math.pow(xNext - x, 2) + Math.pow(yNext - y, 2));
        }

        return totalLength;
    }

    /**
     * 计算贝塞尔曲线长度
     *
     * @param p0 起点坐标
     * @param p1 控制点1坐标
     * @param p2 控制点2坐标
     * @param p3 终点坐标
     * @return 曲线长度
     */
    public static double calculateBezierCurveLength(IPoint p0, IPoint p1, IPoint p2, IPoint p3) {
        int n = 1000;   // 分段数越大越精确
        double length = 0.0;

        // 复合辛普森法计算
        double h = 1.0 / n;
        double sumEven = 0.0;
        double sumOdd = 0.0;

        // 计算首尾项
        double v0 = __velocityMagnitude(0.0, p0, p1, p2, p3);
        double vn = __velocityMagnitude(1.0, p0, p1, p2, p3);

        // 计算中间项
        for (int i = 1; i < n; i++) {
            double t = i * h;
            double v = __velocityMagnitude(t, p0, p1, p2, p3);
            if (i % 2 == 0) {
                sumEven += v;
            } else {
                sumOdd += v;
            }
        }

        // 辛普森公式
        length = h / 3.0 * (v0 + 4.0 * sumOdd + 2.0 * sumEven + vn);
        return length;
    }

    private static double __velocityMagnitude(double t, IPoint p0, IPoint p1, IPoint p2, IPoint p3) {
        // 计算导数 dx/dt 和 dy/dt
        double dxdt = -3 * Math.pow(1 - t, 2) * p0.getX()
                + 3 * (1 - t) * (1 - 3 * t) * p1.getX()
                + 3 * t * (2 - 3 * t) * p2.getX()
                + 3 * Math.pow(t, 2) * p3.getX();
        double dydt = -3 * Math.pow(1 - t, 2) * p0.getY()
                + 3 * (1 - t) * (1 - 3 * t) * p1.getY()
                + 3 * t * (2 - 3 * t) * p2.getY()
                + 3 * Math.pow(t, 2) * p3.getY();

        // 计算速度的模
        return Math.sqrt(dxdt * dxdt + dydt * dydt);
    }

    // endregion

    // region 直线

    /**
     * 两点间直线距离
     *
     * @param p0 起点坐标
     * @param p1 终点坐标
     * @return 距离
     */
    public static double calculateLineLength(IPoint p0, IPoint p1) {
        double dx = p1.getX() - p0.getX();
        double dy = p1.getY() - p0.getY();
        return Math.sqrt(dx * dx + dy * dy);
    }

    // endregion


    /**
     * 计算两点间长度
     *
     * @param type     路径类型
     *                 bezier_curve - 贝塞尔曲线
     *                 straight_line - 直线
     *                 convex - 凸弧线
     *                 concave_arc - 凹弧线
     * @param startPos 起点坐标
     * @param endPos   终点坐标
     * @param control1 控制点1坐标
     * @param control2 控制点2坐标
     * @param rad      弧度值(仅弧线类型需要),单位:rad
     * @return 长度
     */
    public static double calculateLength(String type, IPoint startPos, IPoint control1, IPoint control2, IPoint endPos, double rad) {
        switch (type) {
            case "bezier_curve":
                return calculateBezierCurveLength(startPos, control1, control2, endPos);
            case "straight_line":
                return calculateLineLength(startPos, endPos);
            case "convex":
            case "concave_arc":
                return calculateArcLength(startPos, endPos, rad);
            default:
                throw new IllegalArgumentException("Unknown route type: " + type);
        }
    }
}
