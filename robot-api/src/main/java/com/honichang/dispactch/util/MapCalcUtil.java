package com.honichang.dispactch.util;

import com.honichang.dispactch.model.Coordinate;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.Position;

/**
 * 地图计算工具类
 *
 * <AUTHOR>
 */
public class MapCalcUtil {

    /**
     * 根据地图的单位（pt）与比例尺，计算出地图当前的米数
     * <br/>
     * 公式：地图尺寸（米）= 地图尺寸（pt）/ 72 * 25.4 / 1000
     *
     * @param ptValue 地图尺寸（pt）
     * @return 地图尺寸（米）
     */
    public static double calcMapMeterByPt(double ptValue) {
        return ptValue / 72 * 25.4 / 1000;
    }

    /**
     * 根据物理真实尺寸和比例尺，计算出pt单位的值
     *
     * @param realMeter 物理真实尺寸（米）
     * @param scale     地图的比例尺
     * @return pt单位的值
     */
    public static double calcPtByRealMeter(double realMeter, double scale) {
        return realMeter * scale * 72000 / 25.4;
    }

    /**
     * 根据地图尺寸和比例尺，计算出地图的米数
     * <br/>
     * 公式：地图物理尺寸（米）= 地图尺寸（米） / 比例尺
     *
     * @param value 地图尺寸（米）
     * @param scale 地图的比例尺
     * @return 地图物理尺寸（米）
     */
    public static double calcMapMeterByPt(double value, double scale) {
        return value / scale;
    }

    /**
     * 根据地图尺寸（pt）与比例尺，计算scale(pt/m)
     * <br/>
     * 公式：<br/>
     * scale(pt/m) = 比例尺 * (72000 / 25.4)
     *
     * @param scale 地图的比例尺
     * @return scale(pt / m)
     */
    public static double calcPtPerMeterScale(double scale) {
        return scale * (72000 / 25.4);
    }

    /**
     * 机器人给出一个坐标(rx,ry)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     *
     * @param rPos             机器人给出的坐标值
     * @param offsetCoordinate 坐标偏移值（基于左上角0,0）
     * @param scale            地图缩放比
     * @return 新坐标
     */
    public static Position calcNewCoordinate(IPosition rPos, Coordinate offsetCoordinate, double scale) {
        return new Coordinate(
                calcNewCoordinateX(rPos.getX(), offsetCoordinate.getX(), scale),
                calcNewCoordinateY(rPos.getY(), offsetCoordinate.getY(), scale));
    }

    /**
     * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     * 3.最后在平移
     *
     * @param x            机器人给出的X坐标值
     * @param minX         笛卡尔坐标系左下角坐标值，不一定是0
     * @param scale        地图缩放比
     * @param translationX X轴平移值
     * @return 新X坐标
     */
    public static double calcNewCoordinateXToPt(double x, double minX, double scale, double translationX) {
        return MapCalcUtil.calcNewCoordinateX(x, minX * -1, scale) + translationX;
    }

    /**
     * 机器人给出一个坐标(rx)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx)
     *
     * @param x       机器人给出的坐标值
     * @param offsetX 坐标偏移值（基于左上角0,0）
     * @param scale   地图缩放比
     * @return 新坐标
     */
    private static double calcNewCoordinateX(double x, double offsetX, double scale) {
        // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
        double nrx = getNewX(x, offsetX);
        // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
        return calcPtByRealMeter(nrx, scale);
    }

    /**
     * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     * 3.最后在平移
     *
     * @param y            机器人给出的Y坐标值
     * @param maxY         笛卡尔坐标系右上角Y坐标值
     * @param scale        地图缩放比
     * @param translationY Y轴平移值
     * @return 新Y坐标
     */
    public static double calcNewCoordinateYToPt(double y, double maxY, double scale, double translationY) {
        return MapCalcUtil.calcNewCoordinateY(y, maxY * -1, scale) + translationY;
    }

    /**
     * 机器人给出一个坐标(ry)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(pty)
     *
     * @param y       机器人给出的坐标值
     * @param offsetY 坐标偏移值（基于左上角0,0）
     * @param scale   地图缩放比
     * @return 新坐标
     */
    private static double calcNewCoordinateY(double y, double offsetY, double scale) {
        // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
        double nry = getNewY(y, offsetY);
        // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
        return calcPtByRealMeter(nry, scale);
    }

    /**
     * 计算Y轴偏移后的值
     */
    private static double getNewY(double y, double offsetY) {
        return -1 * (y + offsetY);
    }

    /**
     * 计算X轴偏移后的值
     */
    public static double getNewX(double x, double offsetX) {
        return x + offsetX;
    }

}
