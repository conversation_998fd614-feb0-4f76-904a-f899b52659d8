package com.honichang.dispactch.util;

import com.honichang.dispactch.model.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 地图计算工具类
 *
 * <AUTHOR>
 */
public class MapCalcUtil {

    /**
     * 根据地图的单位（pt）与比例尺，计算出地图当前的米数
     * <br/>
     * 公式：地图尺寸（米）= 地图尺寸（pt）/ 72 * 25.4 / 1000
     *
     * @param ptValue 地图尺寸（pt）
     * @return 地图尺寸（米）
     */
    public static double calcMapMeterByPt(double ptValue) {
        return ptValue / 72 * 25.4 / 1000;
    }

    /**
     * 根据物理真实尺寸和比例尺，计算出pt单位的值
     *
     * @param realMeter 物理真实尺寸（米）
     * @param scale     地图的比例尺
     * @return pt单位的值
     */
    public static double calcPtByRealMeter(double realMeter, double scale) {
        return realMeter * scale * 72000 / 25.4;
    }

    /**
     * 根据地图尺寸和比例尺，计算出地图的米数
     * <br/>
     * 公式：地图物理尺寸（米）= 地图尺寸（米） / 比例尺
     *
     * @param value 地图尺寸（米）
     * @param scale 地图的比例尺
     * @return 地图物理尺寸（米）
     */
    public static double calcMapMeterByPt(double value, double scale) {
        return value / scale;
    }

    /**
     * 根据地图尺寸（pt）与比例尺，计算scale(pt/m)
     * <br/>
     * 公式：<br/>
     * scale(pt/m) = 比例尺 * (72000 / 25.4)
     *
     * @param scale 地图的比例尺
     * @return scale(pt / m)
     */
    public static double calcPtPerMeterScale(double scale) {
        return scale * (72000 / 25.4);
    }

    /**
     * 机器人给出一个坐标(rx,ry)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     *
     * @param rPos             机器人给出的坐标值
     * @param offsetCoordinate 坐标偏移值（基于左上角0,0）
     * @param scale            地图缩放比
     * @return 新坐标
     */
    public static Position calcNewCoordinate(IPosition rPos, Coordinate offsetCoordinate, double scale) {
        return new Coordinate(
                calcNewCoordinateX(rPos.getX(), offsetCoordinate.getX(), scale),
                calcNewCoordinateY(rPos.getY(), offsetCoordinate.getY(), scale));
    }

    /**
     * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     * 3.最后在平移
     *
     * @param x            机器人给出的X坐标值
     * @param minX         笛卡尔坐标系左下角坐标值，不一定是0
     * @param scale        地图缩放比
     * @param translationX X轴平移值
     * @return 新X坐标
     */
    public static double calcNewCoordinateXToPt(double x, double minX, double scale, double translationX) {
        return MapCalcUtil.calcNewCoordinateX(x, minX * -1, scale) + translationX;
    }

    /**
     * 机器人给出一个坐标(rx)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx)
     *
     * @param x       机器人给出的坐标值
     * @param offsetX 坐标偏移值（基于左上角0,0）
     * @param scale   地图缩放比
     * @return 新坐标
     */
    private static double calcNewCoordinateX(double x, double offsetX, double scale) {
        // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
        double nrx = getNewX(x, offsetX);
        // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
        return calcPtByRealMeter(nrx, scale);
    }

    /**
     * 将机器人坐标，依据当前xmap的坐标系及scale、offset转换成为基于左上角原点的pt坐标
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
     * 3.最后在平移
     *
     * @param y            机器人给出的Y坐标值
     * @param maxY         笛卡尔坐标系右上角Y坐标值
     * @param scale        地图缩放比
     * @param translationY Y轴平移值
     * @return 新Y坐标
     */
    public static double calcNewCoordinateYToPt(double y, double maxY, double scale, double translationY) {
        return MapCalcUtil.calcNewCoordinateY(y, maxY * -1, scale) + translationY;
    }

    /**
     * 机器人给出一个坐标(ry)
     * 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nry)
     * 2.再将新坐标转换为当前地图pt单位的坐标(pty)
     *
     * @param y       机器人给出的坐标值
     * @param offsetY 坐标偏移值（基于左上角0,0）
     * @param scale   地图缩放比
     * @return 新坐标
     */
    private static double calcNewCoordinateY(double y, double offsetY, double scale) {
        // 1.先将此坐标转换为左上角(0,0)坐标系的新坐标(nrx,nry)
        double nry = getNewY(y, offsetY);
        // 2.再将新坐标转换为当前地图pt单位的坐标(ptx,pty)
        return calcPtByRealMeter(nry, scale);
    }

    /**
     * 计算Y轴偏移后的值
     */
    private static double getNewY(double y, double offsetY) {
        return -1 * (y + offsetY);
    }

    /**
     * 计算X轴偏移后的值
     */
    public static double getNewX(double x, double offsetX) {
        return x + offsetX;
    }


    // region 路径计算

    /**
     * 给定一组路径，和起点、终点、障碍点（包含了所属路径id），返回优先级排序的可达路径链
     */
    public static List<PathChain<IPath>> calcRouteChain(List<? extends IPath> paths, IPosition start, IPosition end, List<Obstacle> obstacles) {
        List<PathChain<IPath>> pathChains = new ArrayList<>();
        // 如果路径为空，直接返回空结果
        if (paths == null || paths.isEmpty()) {
            return pathChains;
        }

        // 不允许起点和终点重合
        if (start.equalCo(end)) {
            return pathChains;
        }

        // 获取包含起始点的，且可以往下走的路径
        List<IPath> containStartPosPaths = paths.stream()
                .filter(r ->
                        ((r.getStartPos().equalCo(start) && r.getDirection().equals("forward")) ||
                                (r.getEndPos().equalCo(start) && r.getDirection().equals("backward")))
                )
                .collect(Collectors.toList());
        // 没有包含起始点的路径，说明起始点错误，返回空结果
        if (containStartPosPaths.isEmpty()) {
            return pathChains;
        }

        for (IPath path : containStartPosPaths) {
            // 基于start点为head，预备一个路径链
            PathChain<IPath> root = new PathChain<>();
//            root.add(path.cloneMe());
//            __tryAddIfArrive(pathChains, paths, root, start, start, end, obstacles);
        }

        // 根据路径链的长度排序，越短优先
        pathChains.sort(Comparator.comparingDouble(PathChain::getPathLength));

        return pathChains;
    }

    /**
     * 推算end目标是否可达，如果可达则添加到结果数组中，不可达则什么都不做
     *
     * @param routeChainList 结果数组
     * @param paths          所有路径
     * @param parent         父路径链（null表示是根路径链），用于处理分叉路口的递归计算
     * @param beginning      最初的起始点
     * @param start          parent的起始点（因为机器人路线可以是both，所以不能单纯的判断end，start也要判断！）
     * @param finishing      目标点
     * @param obstacles      障碍点
     */
    private static void __tryAddIfArrive(List<PathChain<IPath>> routeChainList, List<? extends IPath> paths, PathChain<IPath> parent, IPosition beginning, IPosition start, IPosition finishing, List<Obstacle> obstacles) {
        // 如果本路径包含了障碍点，则说明路不通，跳过
        if (obstacles.stream().anyMatch(o -> Objects.equals(o.getXmapPathId(), parent.getTail().getXmapId()))) {
            return;
        }

        // 获取parent的尾部路径的起始点和结束点
        IPosition startPos = parent.getTail().getStartPos();
        IPosition endPos = parent.getTail().getEndPos();
        // 因为路径方向未知，所以要计算出真正的本次实际的另一端
        IPosition realEndPos = startPos.equalCo(start) ? endPos : startPos;
//        IPosition realStartPos = startPos.equalCo(start) ? startPos : endPos;

        // 如果是both，且realStart与start不符，则反转last的start与end
        // 没有both了！
//        if (parent.getTail().getDirection().equals("both") && realStartPos.equalCo(endPos)) {
//            parent.getTail().reverse();
//        }

        // 成功抵达：则加入结果数组并返回！！
        if (realEndPos.equalCo(finishing)) {
            // 这里必须建立全新的副本Entry链，否则会被后面的路径链污染
            routeChainList.add(parent.clone());
            return;
        }

        // 闭环回到原点，也没抵达，说明这个环路上没有目标点，跳过，不跳过就会进入无限递归
        if (realEndPos.equalCo(beginning)) {
            return;
        }

        // 未抵达，则获取除尾链路径外与realEndPos相连的路径
        List<IPath> containEndPosPaths = paths.stream()
                .filter(r -> !r.getXmapId().equals(parent.getTail().getXmapId()) &&
                        ((r.getStartPos().equalCo(realEndPos) && r.getDirection().equals("forward")) ||
                                (r.getEndPos().equalCo(realEndPos) && r.getDirection().equals("backward")))
                )
                .collect(Collectors.toList());
        // 如果没有包含realEndPos的其他路径，说明是死胡同，跳过
        if (containEndPosPaths.isEmpty()) {
            return;
        }

        for (IPath path : containEndPosPaths) {
            // 查找这个路径是否已经存在在parent链中，如果已经存在说明绕回来了，自动跳过即可
            if (parent.exists(path)) {
                continue;
            }
            // 基于parentTailEndPos点为head，预备一个路径链
            PathChain<IPath> child = new PathChain<>();
            // TODO: 路径是否需要克隆？
            child.add(path);
            child.prepend(parent);
            // 递归调用，如果可达则添加到结果数组中
            __tryAddIfArrive(routeChainList, paths, child, beginning, realEndPos, finishing, obstacles);
        }
    }

    // endregion
}
