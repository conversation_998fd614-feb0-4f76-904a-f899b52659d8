package com.honichang.dispactch.service;

import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.TaskDirectiveChain;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.TaskDef;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * 普通任务服务接口
 * 
 * <AUTHOR>
 */
public interface TaskService {

    /**
     * 判断机器人是否可以执行陪同任务
     * @param rc 机器人
     * @return 空串-可以执行陪同任务，非空-不可以执行陪同任务，返回值为原因
     */
    @NonNull
    String canWorkForEscort(RobotContext rc);

    /**
     * 判断机器人是否可以执行普通任务
     * @param rc 机器人
     * @return 空串-可以执行任务，非空-不可以执行任务，返回值为原因
     */
    @NonNull
    String canWorkForTask(RobotContext rc);

    /**
     * 重新安排所有机器人的任务
     * 遍历所有的任务定义（非陪同和周期任务）
     * 优先主机器人排满，主机器人数量不足（非工作状态）
     * 则随机调度备用机器人（准工作状态且3小时内无陪同任务）
     * 只放入RobotContext中，不访问数据库
     */
    void distributeTasksForRobots();

    /**
     * 说明：继续当前任务<p>
     * 由调度任务每8秒检查一次，如果是待机状态（在线等）<br/>
     * 从任务队列中获取最顶层任务<br/>
     * 1.更新任务实例状态并保存（新任务会持久化）<br/>
     * 2.获取未完成节点<br/>
     * 3.规划路径<br/>
     * 4.推入RC.directiveChain<br/>
     * 5.写日志<br/>
     * 6.导航批任务下发<br/>
     *
     * @param rc 机器人上下文
     * @return 有任务恢复或开始导航返回true，没有返回false
     */
    boolean runTaskInQueue(RobotContext rc);

    /**
     * 规划任务节点以及路径
     * 不可达的排除在结果之外！
     *
     * @param rc            机器人
     * @param taskNodes     任务节点列表（副作用：将会刷新节点的不可达状态）
     * @return 任务指令链
     * @throws NoReachablePathException 无可达路径异常
     */
    @NonNull
    TaskDirectiveChain planTaskNodeOrders(RobotContext rc, @NonNull List<TaskInstanceNode> taskNodes) throws NoReachablePathException;

}
