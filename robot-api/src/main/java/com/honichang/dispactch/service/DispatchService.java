package com.honichang.dispactch.service;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.bo.ChargingPilePathPlan;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.BizPoint;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 调度核心服务接口
 *
 * <AUTHOR>
 */
public interface DispatchService {

    // ========== 组合控制（遥控模式）相关方法 ==========

    /**
     * 想要进行组合控制，必须先切换机器人工作模式到102=遥控模式
     * 需要特殊权限<p/>
     * 1.智能机器人页面锁头打开进入遥控模式，无操作下倒计时N分钟自动切回自动模式<br/>
     * 2.点位配置进入遥控模式，不会自动切换回来<br/>
     *
     * @param robotId     机器人id
     * @param workMode    工作模式：101自动模式 | 102遥控模式(后端遥控)
     * @param autoTimeout 是否启用超时回到自动模式，当处于点位配置场景中是没有超时判定的
     *                    true: 启用超时回到自动模式（智能机器人场景）
     *                    false: 不启用超时回到自动模式（点位配置场景）
     * @return 成功返回true，失败返回false
     */
    boolean switchWorkMode(long robotId, RobotWorkModeEnum workMode, boolean autoTimeout);

    /**
     * 让当前机器人复位到初始状态
     * 注意：这个点位是xmap点位，不是biz点位
     *
     * @param robotId 机器人id
     * @param xmapId  复位的点位xmapID
     * @return 成功返回true，失败返回false
     */
    boolean resetRobot(long robotId, String xmapId);

    /**
     * 让当前机器人走入前方最近点位，如果当前在十字路口，则根据theta确定哪里是前方，没有前方则放弃
     * 注意：这个点位是xmap点位，不是biz点位
     *
     * @param robotId 机器人id
     * @return 成功返回前进点位，失败返回null
     */
    String forwardStepPoint(long robotId);

    /**
     * 让当前机器人退到后方最近点位，如果当前在十字路口，则根据theta确定哪里是后方，没有后方则放弃
     * 注意：这个点位是xmap点位，不是biz点位
     *
     * @param robotId 机器人id
     * @return 成功返回后退点位，失败返回null
     */
    String backwardStepPoint(long robotId);

    // ========== 调度（非手动模式）相关方法 ==========

    /**
     * 走一步
     * 应用场景：遥控模式单一路径指令；自动模式所有调度都要用到这个函数
     *
     * @param robotId      机器人id
     * @param pathId       要走的路径id
     * @param startPointId 起始点位id
     * @param endPointId   走向终止点位id
     * @param direction    1正走 | 2倒走
     * @return 成功true，失败返回false
     */
    boolean runStepPoint(long robotId, String pathId, String startPointId, String endPointId, int direction);

    /**
     * 让当前机器人走临近路径的百分比长度
     * 期间遇障碍不汇报，障碍超过1分钟原地暂停
     * 函数体内部分多步+实时监控完成
     *
     * @param robotId      机器人id
     * @param pathId       要走的路径id
     * @param startPointId 起始点位id
     * @param endPointId   走向终止点位id
     * @param percent      百分比，范围0.1-1.0，step=0.1
     * @param direction    1正走 | 2倒走
     * @return 成功返回true，失败返回false
     */
    boolean runStepPercent(long robotId, String pathId, String startPointId, String endPointId, double percent, int direction);

    /**
     * 让当前机器人运行到指定点位
     * 必须处于遥控模式下
     *
     * @param robotId 机器人id
     * @param pointId runTo的点位id
     * @return 成功返回true，失败返回false，如果失败见机器人日志
     */
    boolean runToPoint(long robotId, String pointId);

    // ========== 路径规划相关方法 ==========

    /**
     * 根据地图上的所有路径、障碍点<br/>
     * 返回从起点到终点的路径链，中间不允许跳路径，必须连续<br/>
     * 最优算法包含了倒走系数、路径重叠系数、节点数量系数
     *
     * @param robotId 机器人id
     * @param start   起始点
     * @param end     目标点
     * @param alwaysForward 是否必须正走
     * @param tempObstacleXmapPathId 临时障碍路径id（如果传入了临时障碍路径id，则会启用临时距离矩阵排除这些路径）
     * @return 按路径长度排序后的路径链结果数组，返回NULL说明还在原地不需要动
     * @throws NoReachablePathException 无可达路径异常
     */
    Optional<PathChain<IPath>> planPath(
            long robotId,
            @NonNull Position start,
            @NonNull Position end,
            boolean alwaysForward,
            String... tempObstacleXmapPathId) throws NoReachablePathException;

    /**
     * 找最近的无人预约的充电桩
     *
     * @param robotId 机器人id
     * @return 成功返回充电桩id，失败没找到返回null
     */
    Long findNearestAvailableCharger(long robotId);

    /**
     * 找充电排队最少的充电桩，同样排队数量的找最近的
     *
     * @param robotId 机器人id
     * @return 成功返回充电桩id，失败没找到返回null
     */
    Long findLeastChargerQueue(long robotId);

    /**
     * 找某点附件最近的空闲停车位
     *
     * @param robotId 机器人id
     * @param pointId 目标点位id
     * @return 成功返回停车位id，失败没找到返回null
     */
    Long findNearestAvailableParking(long robotId, String pointId);

    // ========== 交通管制相关方法 ==========

    /**
     * 预测未来N步是否与其他机器人预分配路径冲突
     *
     * @param robotId 机器人id
     * @param steps   预测步数
     * @return 成功返回true，失败返回false
     */
    boolean predictFutureOccupancy(long robotId, int steps);

    /**
     * 处理机器人冲突，控制优先级低的机器人退到走过的最近十字路口再让出十字路口
     *
     * @param robotId 机器人id
     * @return 成功返回true，失败返回false
     */
    boolean resolveConflict(long robotId);

    // ========== 任务管理相关方法 ==========

    /**
     * 给任务排列节点，并规划路径
     *
     * @param robotId          机器人id
     * @param taskDefId        任务定义ID
     * @param taskChain        引用参数返回规划后的任务队列
     * @param unreachableNodes 引用参数返回不可达节点
     * @param paths            引用参数返回规划后的路径
     * @return 成功返回true，失败返回false
     */
    boolean planTaskPath(long robotId, long taskDefId, TaskChain taskChain, List<Long> unreachableNodes, PathChain<Path> paths);

    // ========== 机器人上下文管理 ==========

    /**
     * 获取机器人上下文
     */
    RobotContext getRobotContext(long robotId);

    /**
     * 更新机器人上下文
     */
    void updateRobotContext(RobotContext robotContext);

    /**
     * 获取所有机器人上下文
     */
    Collection<RobotContext> getAllRobotContexts();

    /**
     * 导航路径
     *
     * @param robotId   机器人id
     * @param bizId     业务id
     * @param pathChain 路径链
     */
    void navigate(long robotId, @NonNull Long bizId, @NonNull PathChain<IPath> pathChain);

    /**
     * 取消当前导航
     */
    String abortNavigation(long robotId);

    /**
     * 驱动机器人前往排队最少且最近的充电桩
     *
     * @throws ServiceException 充电桩不存在、充电桩无空闲停车位、找不到规划路径
     */
    @NonNull
    ChargingPilePathPlan navigateToCharge(long robotId);

    /**
     * 驱动机器人前往目标点附件的停车位
     *
     * @return true成功，false失败
     * @throws ServiceException 无空闲停车位、找不到规划路径; 目标点位在地图中不存在
     */
    boolean navigateToParking(long robotId, @NonNull String xmapPointId);

    /**
     * 导航到最近的十字路口（无方向，到了就行）
     * @return true成功，false就在脚下
     * @throws ServiceException 找不到十字路口；找不到规划路径
     */
    boolean navigateToNearestCrossPoint(long robotId);

    /**
     * 导航任务指令
     *
     * @param directiveChain 任务指令链
     */
    void navigateTaskDistribution(long robotId, TaskDirectiveChain directiveChain);

    /**
     * 获取机器人所在位置最近的业务点位
     * @param robotId 机器人id
     */
    @Nullable
    BizPoint findNearestBizPoint(Long robotId);
}
