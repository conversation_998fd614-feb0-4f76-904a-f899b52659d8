package com.honichang.dispactch.service;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.PathChain;
import com.honichang.dispactch.model.TaskDirectiveChain;
import org.springframework.lang.NonNull;

public interface DispatchCommandService {

    /**
     * 暂停导航
     *
     * @param robotId 机器人id
     */
    void pauseNavigation(@NonNull Long robotId);

    /**
     * 继续导航
     *
     * @param robotId 机器人id
     * @throws ServiceException 继续导航失败
     */
    void resumeNavigation(@NonNull Long robotId);

    /**
     * 取消导航
     *
     * @param robotId 机器人id
     */
    @NonNull
    String abortNavigation(@NonNull Long robotId);


    /**
     * 复位机器人到指定点位
     * @param robotId 机器人id
     * @param xmapId 复位的点位xmapID
     */
    void resetRobot(long robotId, String xmapId);

    /**
     * 导航路径
     *
     * @param robotId   机器人id
     * @param bizId 业务id
     * @param pathChain 路径链
     */
    void navigate(long robotId, @NonNull Long bizId, @NonNull PathChain<IPath> pathChain);

    /**
     * 打开或关闭充电口
     * @param robotId 机器人id
     * @param i 1打开 | 2关闭
     * @throws ServiceException 打开或关闭充电口失败
     */
    void openOrCloseChargingPort(long robotId, int i);

    /**
     * 开始充电
     * @param pileId 充电桩id
     * @throws ServiceException 开始充电失败
     */
    void startPileCharging(Long pileId);

    /**
     * 停止充电
     * @param pileId 充电桩id
     * @throws ServiceException 停止充电失败
     */
    void stopPileCharging(Long pileId);

    /**
     * 播放语音
     * @param voiceId 语音id
     * @param robotId 机器人id
     */
    void playVoice(Long robotId, int voiceId);

    /**
     * 暂停语音
     * @param robotId 机器人id
     */
    void pauseVoice(Long robotId);

    /**
     * UWB刷卡签到
     * @param robotId 机器人
     * @param success 签到成功否
     * @throws ServiceException 工控指令发送失败
     */
    void uwbCheckIn(Long robotId, boolean success);

    /**
     * 机器人执行固件升级
     * @throws ServiceException 升级失败
     */
    void firmwareUpdateConfirm(Long robotId);

    /**
     * 下发任务指令
     *
     * @param directiveChain 任务指令链
     */
    void navigateTaskDistribution(long robotId, @NonNull TaskDirectiveChain directiveChain);
}
