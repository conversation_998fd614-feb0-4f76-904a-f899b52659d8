package com.honichang.dispactch.context;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.Position;
import com.honichang.point.domain.TaskEscort;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 陪同任务上下文
 *
 * <AUTHOR>
 */
public class TaskEscortContext {

    /**
     * 缓存静态数组
     */
    private static final Map<Long, TaskEscortContext> cache = new ConcurrentHashMap<>();

    @Getter
    private volatile Position waitPosition;

    @Getter
    private volatile Position arrivePosition;
    /**
     * 机器人到达等待点时间，用于计算等待超时
     **/
    @Getter
    @Setter
    private volatile Date waitArriveTime;
    /**
     * 访客超出范围时间
     **/
    @Getter
    @Setter
    private volatile Date outOfRangeTime;

    private final transient AtomicReference<TaskEscort> taskEscort = new AtomicReference<>();

    @NonNull
    public TaskEscort getTaskEscort() {
        return taskEscort.get();
    }

    /**
     * 仅在修改陪同任务时使用
     */
    public void setTaskEscort(TaskEscort taskEscort) {
        this.taskEscort.set(taskEscort);
    }

    private TaskEscortContext(TaskEscort taskEscort) {
        this.taskEscort.set(taskEscort);
    }


    /**
     * 获取陪同任务上下文
     */
    @NonNull
    public static TaskEscortContext get(long taskId) {
        TaskEscortContext tc = cache.get(taskId);
        if (tc == null) {
            throw new ServiceException("陪同任务[" + taskId + "]上下文不存在", HttpStatus.ERROR);
        }
        return tc;
    }

    public static boolean exists(long taskId) {
        return cache.containsKey(taskId);
    }

    @Nullable
    public static TaskEscortContext getByRobot(long robotId) {
        return cache.values().stream()
                .filter(t -> Objects.equals(t.getTaskEscort().getRobotId(), robotId))
                .findFirst().orElse(null);
    }

//    @NonNull
//    public static List<TaskEscortContext> getByRobot(long robotId) {
//        // TODO: 为什么一个机器人会有多个陪同在线的任务？
//        return cache.values().stream()
//                .filter(t -> Objects.equals(t.getTaskEscort().getRobotId(), robotId))
//                .collect(Collectors.toList());
//    }

    /**
     * 缓存陪同任务上下文
     */
    public static TaskEscortContext put(@NonNull TaskEscort taskEscort, @NonNull Position waitPosition, @NonNull Position arrivePosition) {
        if (!exists(taskEscort.getId())) {
            TaskEscortContext tec = new TaskEscortContext(taskEscort);
            tec.waitPosition = waitPosition;
            tec.arrivePosition = arrivePosition;
            cache.put(taskEscort.getId(), tec);
            return tec;
        }
        return TaskEscortContext.get(taskEscort.getId());
    }

    @NonNull
    public static Collection<TaskEscortContext> values() {
        return cache.values();
    }

    /**
     * 移除陪同任务上下文
     */
    public static void remove(long taskId) {
        cache.remove(taskId);
    }

    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }

    public static boolean exists(Long taskEscortId) {
        return cache.containsKey(taskEscortId);
    }
}
