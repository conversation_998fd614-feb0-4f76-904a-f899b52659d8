package com.honichang.dispactch.context.helper;

import com.honichang.dispactch.model.Path;

import java.util.List;

public interface IDistMatrix {

    /**
     * 获取两点间距离
     *
     * @param fromXmapId 起点xmapId
     * @param toXmapId   终点xmapId
     * @return 距离米（单位毫米转换会有误差）；
     * 如果不可达则返回 UNREACHABLE_MM (Integer.MAX_VALUE / 2)
     */
    double getDist(String fromXmapId, String toXmapId);

    /**
     * 获取障碍数量
     *
     * @return 障碍数量
     */
    int obstacleSize();

    /**
     * 判断是否包含障碍
     *
     * @param xmapPathId 路径xmapId
     * @return true包含，false不包含
     */
    boolean containsObstacle(String xmapPathId);

    /**
     * 获取障碍路径xmapId
     *
     * @param idx 障碍索引
     * @return 障碍路径xmapId
     */
    String getObstacleXmapPathId(int idx);

    /**
     * 获得地图的所有路径
     *
     * @return 路径列表
     */
    List<Path> getPaths();
}
