package com.honichang.dispactch.context.helper;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.Path;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.IntStream;

/**
 * 距离矩阵
 *
 * <AUTHOR>
 */
public class DistMatrix {

    /**
     * 不可达值（毫米）
     */
    public static final int UNREACHABLE_MM = Integer.MAX_VALUE / 2;

    /**
     * 预存全图距离矩阵（mapPoints.size * mapPoints.size）
     * double米到这里需要*1000转换为毫米
     * int的最大值是21亿毫米，也就是21万米，足够了
     * 之所以用int是为了减少内存开销
     */
    @Getter
    private volatile int[][] distMatrix;

    /**
     * 预热距离矩阵
     */
    public void preloadMatrix() {
        if (points == null) throw new ServiceException("地图点位不存在", HttpStatus.ERROR);

        // 防止运行期访问，这里整批替换
        int[][] distMatrix = new int[points.size()][points.size()];

        // 初始化最大值（表示不可达）
        for (int i = 0; i < points.size(); i++) {
            Arrays.fill(distMatrix[i], UNREACHABLE_MM); // 防止溢出
            distMatrix[i][i] = 0; // 自身距离是0
        }

        // 判断所有点位的互相可达最优距离
        for (Path path : paths) {
            // 判断路径中是否有障碍点
            if (__isPathBlocked(path)) {
                continue;
            }
            int from = xmapPointIdToMatrixId.get(path.getStartPos().getXmapId());
            int to = xmapPointIdToMatrixId.get(path.getEndPos().getXmapId());
            int dist = (int) (path.getLength() * 1000); // 转换为毫米
            // 因为不存在双向，所以这里不需要写to到from的距离
            distMatrix[from][to] = dist;
        }

        long startTime = System.currentTimeMillis();
        // Floyd-Warshall算法计算所有点位间最短距离
        // 多线程并发,并行化中间层循环
        // 优势：避免频繁加锁，适合 CPU 密集型任务。
        IntStream.range(0, points.size()).parallel().forEach(k -> {
            for (int i = 0; i < points.size(); i++) {
                for (int j = 0; j < points.size(); j++) {
                    int nd = distMatrix[i][k] + distMatrix[k][j];
                    if (nd < distMatrix[i][j]) {
                        distMatrix[i][j] = nd;
                    }
                }
            }
        });
        log.debug("计算最短距离耗时：{}ms", System.currentTimeMillis() - startTime);

        this.distMatrix = distMatrix;
        log.debug("地图[{}]距离矩阵预加载完成，点位数量：{}，消耗内存：{}MB", name, points.size(), points.size() * points.size() * 4 / 1024 / 1024);
    }

}
