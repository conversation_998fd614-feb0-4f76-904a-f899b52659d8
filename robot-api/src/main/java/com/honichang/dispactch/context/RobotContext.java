package com.honichang.dispactch.context;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.NumberUtil;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkModeEnum;
import com.honichang.dispactch.model.enums.RobotWorkStatusEnum;
import com.honichang.dispactch.model.enums.StatusTransResultEnum;
import com.honichang.point.domain.Robot;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.*;

/**
 * 机器人调度上下文
 *
 * <AUTHOR>
 */
public class RobotContext {

    /**
     * 缓存静态数组
     */
    private static final Map<Long, RobotContext> cache = new ConcurrentHashMap<>();

    //↓--------不常变属性
    /**
     * 机器人id
     **/
    @Getter
    @Setter
    private volatile long robotId;
    /**
     * 地图id
     **/
    @Getter
    private volatile long mapId;
    /**
     * 机器人名称
     **/
    @Getter
    private volatile String robotName;
    /**
     * 机器人序列号
     **/
    @Getter
    private volatile String sn;
    /**
     * 机器人ip
     **/
    @Getter
    private volatile String ip;
    /**
     * 机器人端口
     **/
    @Getter
    private volatile int port;
    /**
     * 机器人appCode
     **/
    @Getter
    private volatile String appCode;
    /**
     * 机器人优先级
     **/
    @Getter
    private volatile double priority;


    /**
     * 通用锁，用于IP端口等的改变
     */
    private final Object commonLock = new Object();

    /**
     * 初始化通用属性，只初始化一次
     *
     * @param entity DB中的机器人信息
     */
    public void initCommonProperties(@NonNull Robot entity) {
        if (robotId != entity.getId()) {
            throw new IllegalArgumentException("机器人id不一致");
        }
        synchronized (commonLock) {
            mapId = entity.getMapId();
            robotName = entity.getRobotName();
            sn = entity.getSn();
            ip = entity.getIp();
            try {
                port = Integer.parseInt(entity.getPort());
            } catch (Exception e) {
                throw new ServiceException("机器人端口格式错误：" + entity.getPort(), HttpStatus.ERROR);
            }
            appCode = entity.getAppCode();
            priority = NumberUtil.defaultDouble(entity.getPriority());

            // 位置信息
            if (entity.getRobotDynamicAttr() != null) {
                // region 👇机器人所在点位
                String xmapPointId = entity.getRobotDynamicAttr().getXmapPointId();
                if (StringUtils.isEmpty(xmapPointId) || xmapPointId.equals("0")) {
                    // 不在点位上
                    position.set(new Position(
                            NumberUtil.defaultDouble(entity.getRobotDynamicAttr().getPosX()),
                            NumberUtil.defaultDouble(entity.getRobotDynamicAttr().getPosY()),
                            NumberUtil.defaultDouble(entity.getRobotDynamicAttr().getTheta())));
                    theta = NumberUtil.defaultDouble(entity.getRobotDynamicAttr().getTheta());
                } else {
                    // 在点位上
                    position.set(MapContext.getXmapPoint(xmapPointId, mapId));
                    theta = position.get().getAngle();
                }
                // endregion

                // region 👇机器人所在路径
                xmapPathId.set(entity.getRobotDynamicAttr().getXmapPathId());
                // endregion

                // region 👇机器人任务
                latestTaskClass = entity.getRobotDynamicAttr().getLatestTaskClass();
                latestTaskId = entity.getRobotDynamicAttr().getLatestTaskId();
                // endregion

                // region 👇机器人状态
                totalRunning = entity.getRobotDynamicAttr().getTotalRunning();
                totalOdom = entity.getRobotDynamicAttr().getTotalOdom();
                battery = entity.getRobotDynamicAttr().getBattery();
                // 状态由实际访问机器人控制器获得
//                status.set(RobotStatusEnum.fromCode(entity.getStatus()));

                workStatus.set(entity.getWorkStatus() == null ? RobotWorkStatusEnum.STANDBY : RobotWorkStatusEnum.fromCode(entity.getWorkStatus()));
                workMode.set(RobotWorkModeEnum.fromCode(entity.getWorkMode()));
                // endregion
            }
        }
    }

    /**
     * 更新通用属性
     *
     * @param robot DB中的机器人信息
     */
    public void updateCommonProperties(@NonNull Robot robot) {
        if (robotId != robot.getId()) {
            throw new IllegalArgumentException("机器人id不一致");
        }
        synchronized (commonLock) {
            mapId = robot.getMapId();
            robotName = robot.getRobotName();
            sn = robot.getSn();
            ip = robot.getIp();
            port = Integer.parseInt(robot.getPort());
            appCode = robot.getAppCode();
            priority = robot.getPriority();
        }
    }

    //↓--------常变属性
    /**
     * 机器人当前坐标（米）
     * 如果当前在地图的点位上则position.xmapId有值
     **/
    private final AtomicReference<Position> position = new AtomicReference<>(null);

    public Position getPosition() {
        return position.get();
    }

    public void setPosition(Position position) {
        this.position.set(position);
    }

    /**
     * 机器人当前朝向（弧度rad）
     **/
    @Getter
    private volatile double theta;
    /**
     * 机器人当前所在路径
     **/
    private final AtomicReference<String> xmapPathId = new AtomicReference<>(null);

    /**
     * 机器人当前所在路径
     */
    public String getXmapPathId() {
        return xmapPathId.get();
    }

    /**
     * 机器人当前所在路径
     */
    public void setXmapPathId(String xmapPathId) {
        this.xmapPathId.set(xmapPathId);
    }

    /**
     * 机器人当前任务类型：0巡检任务 | 1陪同任务 | 2排查障碍任务
     **/
    @Getter
    private volatile Integer latestTaskClass;
    /**
     * 机器人当前任务id
     **/
    @Getter
    private volatile Long latestTaskId;
    /**
     * 机器人当前速度（米/秒）
     **/
    @Getter
    private volatile Double velocity;
    /**
     * 机器人当前定位置信度（0.0-1.0）
     **/
    @Getter
    private volatile Double reliability = 1.0;

    /**
     * 机器人累计运行时间（分钟）
     **/
    @Getter
    @Setter
    private volatile Long totalRunning = 0L;
    /**
     * 机器人累计里程数（米）
     **/
    @Getter
    @Setter
    private volatile Double totalOdom = 0.0;
    /**
     * 机器人当前电量
     * 0~1 对应 0~100%
     **/
    @Getter
    @Setter
    private volatile Double battery = 0.0;

    public String getBatteryPercent() {
        return ((int) (battery * 100)) + "%";
    }

    /**
     * 是否正在充电
     */
    @Getter
    @Setter
    private volatile boolean charging;
    /**
     * 是否正在升级
     */
    @Getter
    @Setter
    private volatile boolean upgrading;


    // region 👇机器人状态

    // region 👇机器人离线状态

    private final AtomicBoolean isOnline = new AtomicBoolean(false);

    /**
     * 机器人离线时间，用于计算离线超时
     **/
    @Getter
    @Setter
    private volatile Date offlineTime;

    public boolean isOnline() {
        return isOnline.get();
    }

    public void setOnline(boolean online) {
        isOnline.set(online);
    }

    // endregion


    private final AtomicReference<RobotStatusEnum> status = new AtomicReference<>(RobotStatusEnum.INIT);

    /**
     * 机器人状态:<p/>
     * -1初始化状态<br/>
     * 0待机 | 1工作<br/>
     * 301去充电（充电由isCharging代替） | 302升级<br/>
     * 4暂停<br/>
     * 7无法导航（可以重试）<br/>
     * 901手工故障 | 902硬件故障（报警）<br/>
     *
     * <s>6定位中</s><br/>
     * <s>701载入地图失败 | 702定位失败 | 703导航失败 | 704置信度不足75%</s><br/>
     **/
    public RobotStatusEnum getStatus() {
        return status.get();
    }

    /**
     * 原子性地比较并设置机器人状态（CAS操作）
     *
     * @param expected  预期的当前状态（如果当前状态不是这个值，则更新失败）
     * @param newStatus 要设置的新状态
     * @return {@link StatusTransResultEnum}<br/>
     * - NO_NEED: 当前状态已经是目标状态，无需修改<br/>
     * - SUCCESS: 状态更新成功<br/>
     * - FAILED: 当前状态与预期不符，更新失败<br/>
     */
    public StatusTransResultEnum compareAndSetStatus(RobotStatusEnum expected, RobotStatusEnum newStatus) {
        // 先检查是否已经是目标状态（避免不必要的CAS操作）
        if (status.get() == newStatus) {
            return StatusTransResultEnum.NO_NEED;
        }
        if (status.compareAndSet(expected, newStatus)) {
            return StatusTransResultEnum.SUCCESS;
        }
        return StatusTransResultEnum.FAILED;
    }

    /**
     * 只要当前不是新状态就强制设置机器人状态
     *
     * @param newStatus 要设置的新状态
     * @return {@link StatusTransResultEnum}<br/>
     * - NO_NEED: 当前状态已经是目标状态，无需修改<br/>
     * - SUCCESS: 状态更新成功<br/>
     */
    public StatusTransResultEnum setStatus(RobotStatusEnum newStatus) {
        // 先检查是否已经是目标状态（避免不必要的CAS操作）
        if (status.get() == newStatus) {
            return StatusTransResultEnum.NO_NEED;
        }
        status.set(newStatus);
        return StatusTransResultEnum.SUCCESS;
    }

//    /**
//     * 状态锁，用于状态的改变
//     */
//    private final ReadWriteLock statusLock = new ReentrantReadWriteLock();
//
//    /** 保护status不被外部修改 */
//    public RobotStatusEnum getStatus() {
//        statusLock.readLock().lock();
//        try {
//            return status;
//        } finally {
//            statusLock.readLock().unlock();
//        }
//    }
//
//    public void setStatus(RobotStatusEnum status) {
//        statusLock.writeLock().lock();
//        try {
//            this.status = status;
//        } finally {
//            statusLock.writeLock().unlock();
//        }
//    }

    // endregion


    // region 👇当前工作状态

    /**
     * 机器人当前工作状态（主优先级）<p/>
     * 0待机（初始状态）
     * 10100陪同任务 | 10200为陪同充电 | 10300陪同返航
     * 30100充电 | 30200 升级 | 30900:runTo
     * 60100周期任务 | 60180检查障碍 | 60200任务 | 60300任务返航
     */
    private final AtomicReference<RobotWorkStatusEnum> workStatus = new AtomicReference<>(RobotWorkStatusEnum.STANDBY);

    /**
     * 机器人当前工作状态（主优先级）<p/>
     * 0待机（初始状态）
     * 10100陪同任务 | 10200为陪同充电 | 10300陪同返航
     * 30100充电 | 30200 升级 | 30900:runTo
     * 60100周期任务 | 60180检查障碍 | 60200任务 | 60300任务返航
     */
    public RobotWorkStatusEnum getWorkStatus() {
        return workStatus.get();
    }

    /**
     * 机器人当前工作状态（主优先级）<p/>
     * 0待机（初始状态）
     * 10100陪同任务 | 10200为陪同充电 | 10300陪同返航
     * 30100充电 | 30200 升级 | 30900:runTo
     * 60100周期任务 | 60180检查障碍 | 60200任务 | 60300任务返航
     */
    public StatusTransResultEnum setWorkStatus(RobotWorkStatusEnum newStatus) {
        // 先检查是否已经是目标状态（避免不必要的CAS操作）
        if (workStatus.get() == newStatus) {
            return StatusTransResultEnum.NO_NEED;
        }
        workStatus.set(newStatus);
        return StatusTransResultEnum.SUCCESS;
    }

    // endregion


    // region 👇工作模式

    /**
     * 工作模式：101自动模式 | 102遥控模式(后端遥控) | 103避障模式  | 201手动模式(厂家遥控器)
     */
    private final AtomicReference<RobotWorkModeEnum> workMode = new AtomicReference<>(RobotWorkModeEnum.AUTO_MODE);

    /**
     * 工作模式：101自动模式 | 102遥控模式(后端遥控) | 103避障模式  | 201手动模式(厂家遥控器)
     */
    public RobotWorkModeEnum getWorkMode() {
        return workMode.get();
    }

    /**
     * 工作模式：101自动模式 | 102遥控模式(后端遥控) | 103避障模式  | 201手动模式(厂家遥控器)
     */
    public void setWorkMode(RobotWorkModeEnum workMode) {
        this.workMode.set(workMode);
    }

    // endregion


    /**
     * 遥控模式最后操作时间，用于超时切回自动模式
     **/
    @Getter
    @Setter
    private volatile Date lastRemoteControlTime;

    /**
     * 当前是否被阻挡
     **/
    @Getter
    @Setter
    private volatile boolean isBlocked;
    /**
     * 阻挡开始时间，用于计算超时阈值
     **/
    @Getter
    @Setter
    private volatile Date blockStartTime;

    /**
     * 设定runStep完成百分比（0.0-1.0）；0或1走完
     **/
    @Getter
    private volatile double runStepPercent;

    /**
     * 机器人任务指令队列
     * 包含正在进行的和等待执行的任务
     */
    @Getter
    private final transient TaskDirectiveChain taskDirectiveChain = new TaskDirectiveChain();
    /**
     * 预分配路径资源
     **/
    @Getter
    private final transient PathChain<IPath> preAllocatedPaths = new PathChain<>();
    /**
     * 已完成路径资源
     **/
    @Getter
    private final transient PathChain<IPath> completedPaths = new PathChain<>();
    /**
     * 最近一次已完成路径资源，每次取消导航或新导航时从已完成克隆进来
     **/
    @Getter
    private final transient PathChain<IPath> hisCompletedPaths = new PathChain<>();

    /**
     * 清空路径资源，同时将已完成路径资源克隆到历史已完成路径资源中
     */
    public void clearPathChain() {
        if (completedPaths.isNotEmpty()) {
            hisCompletedPaths.clear();
            hisCompletedPaths.addAll(completedPaths.getAll());
            completedPaths.clear();
        }
        preAllocatedPaths.clear();
    }

    /**
     * 机器人任务队列
     * 不包括正在执行的TaskDirectiveChain
     * 且这里的数据都是尚未Insert到DB中的
     */
    @Getter
    private final transient SimpleChain<Task> taskQueue = new SimpleChain<>();

    /**
     * 获取机器人上下文，没有找到抛出异常
     */
    @NonNull
    public static RobotContext get(long robotId) {
        RobotContext robotContext = cache.get(robotId);
        if (robotContext == null) {
            throw new ServiceException("机器人[" + robotId + "]上下文不存在");
        }
        return robotContext;
    }

    public static boolean exists(long robotId) {
        return cache.containsKey(robotId);
    }

    /**
     * 缓存机器人上下文
     */
    public static void put(RobotContext robotContext) {
        cache.put(robotContext.getRobotId(), robotContext);
    }

    /**
     * 移除机器人上下文
     */
    public static void remove(long robotId) {
        cache.remove(robotId);
    }

    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }

    /**
     * 获取所有机器人上下文
     */
    @NonNull
    public static Collection<RobotContext> values() {
        return cache.values();
    }

    /**
     * 判断机器人是否处于准工作状态，即待机、工作或暂停状态<br/>
     * 0待机 | 1工作 | 4暂停
     *
     * @return true:处于工作或准工作状态，false:不是
     */
    public boolean isStandByOrWorkingStatus() {
        return !RobotStatusEnum.STANDBY.equals(status.get())
                && !RobotStatusEnum.WORKING.equals(status.get())
                && !RobotStatusEnum.PAUSED.equals(status.get());
    }

    /**
     * 判断机器人是否处于待机状态
     * 0待机
     *
     * @return true:处于待机状态，false:不是
     */
    public boolean isStandBy() {
        return RobotStatusEnum.STANDBY.equals(status.get());
    }

    public boolean isAutoMode() {
        return workMode.get() == RobotWorkModeEnum.AUTO_MODE;
    }

    /**
     * 可以监听变动的状态
     * 在线、自动模式、不在充电中、不在升级中、（待机or工作or暂停）
     *
     * @return true:可以监听，false:不可以
     */
    public boolean isListeningStatus() {
        RobotStatusEnum s = getStatus();
        return isOnline() && isAutoMode()
                && !isUpgrading() && !isCharging()
                && (
                s == RobotStatusEnum.STANDBY
                        || s == RobotStatusEnum.WORKING
                        || s == RobotStatusEnum.PAUSED
        );
    }

    /**
     * 判断机器人是否处于陪同任务中
     *
     * @return true:处于陪同任务中，false:不是
     */
    public boolean isEscorting() {
        RobotWorkStatusEnum ws = workStatus.get();
        return ws == ESCORT_TASK
                || ws == ESCORT_CHARGE
                || ws == ESCORT_RETURN;
    }

    /**
     * 判断当前机器人是否处于一般任务中（普通任务和周期任务）
     *
     * @return true:处于普通任务中，false:不处于普通任务中
     */
    public boolean isTaskWorking() {
        RobotWorkStatusEnum ws = workStatus.get();
        return ws == PERIOD_TASK
                || ws == CYCLE_TASK;
    }

    /**
     * 判断机器人是否处于是准备充电或正在充电的状态
     *
     * @return true:处于充电或正赶往充电桩的状态，false:不是
     */
    public boolean isWorkForCharging() {
        RobotWorkStatusEnum ws = workStatus.get();
        return ws == ESCORT_CHARGE
                || ws == CHARGE;
    }
}
