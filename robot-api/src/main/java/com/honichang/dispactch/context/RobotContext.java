package com.honichang.dispactch.context;

import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.model.*;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 机器人调度上下文
 * <AUTHOR>
 */
@Data
public class RobotContext {

    /**
     * 缓存静态数组
     */
    private static final Map<Long, RobotContext> cache = new ConcurrentHashMap<>();

    //↓--------不常变属性
    /** 机器人id **/
    private volatile long robotId;
    /** 地图id **/
    private volatile long mapId;
    /** 机器人名称 **/
    private volatile String robotName;
    /** 机器人序列号 **/
    private volatile String sn;
    /** 机器人ip **/
    private volatile String ip;
    /** 机器人端口 **/
    private volatile int port;
    /** 机器人appCode **/
    private volatile String appCode;
    /** 机器人优先级 **/
    private volatile double priority;

    //↓--------常变属性
    /**
     * 机器人当前坐标（米）
     * 如果当前在地图的点位上则position.xmapId有值
     **/
    private volatile Position position;
    /** 机器人当前朝向（弧度rad） **/
    private volatile double theta;
    /** 机器人当前所在路径 **/
    private volatile Path currentPath;

    /** 机器人当前任务类型：0巡检任务 | 1陪同任务 | 2排查障碍任务 **/
    private volatile Integer latestTaskClass;
    /** 机器人当前任务id **/
    private volatile Long latestTaskId;

    /** 机器人累计运行时间（分钟） **/
    private volatile Long totalRunning;
    /** 机器人累计里程数（米） **/
    private volatile Double totalOdom;
    /** 机器人当前电量 **/
    private volatile Double battery;

    /**
     * 机器人状态:<br/>
     * 0待机 | 1工作 | 4暂停 | 6定位中 |
     * 701载入地图失败 | 702定位失败 | 703导航失败 | 704置信度不足75%
     * 8离线 | 9故障
     **/
    private volatile String status;

    /** 机器人当前工作状态（主优先级） **/
    private volatile Integer workStatus;

    /** 工作模式：101自动模式 | 102遥控模式(后端遥控) | 201手动模式(厂家遥控器) **/
    private volatile int workMode;
    /** 遥控模式最后操作时间，用于超时切回自动模式 **/
    private volatile Date lastRemoteControlTime;

    /** 当前是否被阻挡 **/
    private volatile boolean isBlocked;
    /** 阻挡开始时间，用于计算超时阈值 **/
    private volatile Date blockStartTime;

    /** 设定runStep完成百分比（0.0-1.0）；0或1走完 **/
    private volatile double runStepPercent;

    /**
     * 机器人任务队列
     * 包含正在进行的和等待执行的任务
     **/
    private final transient TaskChain<?> taskQueue = new TaskChain<>();
    /** 预分配路径资源 **/
    private final transient PathChain<IPath> preAllocatedPaths = new PathChain<>();
    /** 已完成路径资源 **/
    private final transient PathChain<IPath> completedPaths = new PathChain<>();

    /**
     * 获取机器人上下文，没有找到抛出异常
     */
    public static RobotContext get(long robotId) {
        RobotContext robotContext = cache.get(robotId);
        if (robotContext == null) {
            throw new ServiceException("机器人上下文不存在！");
        }
        return robotContext;
    }

    public static boolean exists(long robotId) {
        return cache.containsKey(robotId);
    }

    /**
     * 缓存机器人上下文
     */
    public static void put(RobotContext robotContext) {
        cache.put(robotContext.getRobotId(), robotContext);
    }

    /**
     * 移除机器人上下文
     */
    public static void remove(long robotId) {
        cache.remove(robotId);
    }

    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }

    /**
     * 获取所有机器人上下文
     */
    public static Collection<RobotContext> values() {
        return cache.values();
    }
}
