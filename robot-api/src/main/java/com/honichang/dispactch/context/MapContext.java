package com.honichang.dispactch.context;

import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地图上下文
 * 需要缓存
 * 
 * <AUTHOR>
 */
@Data
public class MapContext {
    
    /**
     * 缓存静态数组
     */
    private static final Map<Long, MapContext> cache = new ConcurrentHashMap<>();
    
    /** 地图id **/
    private long mapId;
    /** 地图名称 **/
    private String name;
    /** 地图最小坐标 **/
    private volatile Position minPos;
    /** 地图最大坐标 **/
    private volatile Position maxPos;
    /** 地图宽度 **/
    private volatile double width;
    /** 地图高度 **/
    private volatile double height;
    /**
     * 地图路径, 取消both，如果是both则拆成两条路径
     * path的start、end包含了完整的业务点位类型信息
     **/
    private volatile List<Path> paths;
    /** 地图点位 **/
    private volatile List<Position> points;
    /** 地图障碍 **/
    private volatile List<Obstacle> obstacles;
    
    /**
     * 获取地图上下文
     */
    public static MapContext get(long mapId) {
        return cache.get(mapId);
    }
    
    /**
     * 缓存地图上下文
     */
    public static void put(MapContext mapContext) {
        cache.put(mapContext.getMapId(), mapContext);
    }
    
    /**
     * 移除地图上下文
     */
    public static void remove(long mapId) {
        cache.remove(mapId);
    }
    
    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }

    public static int size() {
        return cache.size();
    }

    /**
     * 获取地图点位
     * @param xmapPointId xmap地图点位id
     * @param mapId 地图id
     * @return 点位
     */
    public static Position getXmapPoint(String xmapPointId, long mapId) {
        MapContext mapContext = get(mapId);
        if (mapContext == null || mapContext.getPoints() == null) {
            throw new RuntimeException("地图上下文不存在！");
        }

        return mapContext.getPoints().stream()
                .filter(p -> xmapPointId.equals(p.getXmapId()))
                .findAny().orElse(null);
    }

    /**
     * 获取地图路径
     * @param pathId 路径id
     * @param mapId 地图id
     * @return 路径
     */
    public static Path getPath(Long pathId, long mapId) {
        MapContext mapContext = get(mapId);
        if (mapContext == null || mapContext.getPaths() == null) {
            return null;
        }
        for (Path path : mapContext.getPaths()) {
            if (pathId.equals(path.getPathId())) {
                return path;
            }
        }
        return null;
    }
}
