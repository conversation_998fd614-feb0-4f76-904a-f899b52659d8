package com.honichang.dispactch.context;

import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.dispactch.context.helper.IDistMatrix;
import com.honichang.dispactch.context.helper.TempDistMatrix;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.bo.ParkPositionBo;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 地图上下文
 * 需要缓存
 *
 * <AUTHOR>
 */
@Slf4j
public class MapContext implements IDistMatrix {

    // region 👇距离矩阵和前驱矩阵

    /**
     * 不可达值（毫米）
     */
    public static final int UNREACHABLE_MM = Integer.MAX_VALUE / 2;

    /**
     * 判断距离是否不可达
     *
     * @return true不可达，false可达
     */
    public static boolean isUnreachable(long mapId, String fromXmapId, String toXmapId) {
        double dist = get(mapId).getDist(fromXmapId, toXmapId);
        return (int) dist == UNREACHABLE_MM;
    }

    /**
     * 预存全图距离矩阵（mapPoints.size * mapPoints.size）
     * double米到这里需要*1000转换为毫米
     * int的最大值是21亿毫米，也就是21万米，足够了
     * 之所以用int是为了减少内存开销
     */
    @Getter
    private volatile int[][] distMatrix;

    /**
     * 缓存xmapPointId与距离矩阵id的关联关系
     */
    private final Map<String, Integer> xmapPointIdToMatrixId = new ConcurrentHashMap<>();

    /**
     * 预加载地图上下文，分析地图点位间距离矩阵和前驱矩阵
     * TODO: 当地图更新、遭遇障碍或障碍取消时，需要刷新距离矩阵
     */
    public void preloadMatrix() {
        if (points == null) throw new ServiceException("地图点位不存在", HttpStatus.ERROR);

        // 防止运行期访问，这里整批替换
        int[][] distMatrix = new int[points.size()][points.size()];

        // 初始化最大值（表示不可达）
        for (int i = 0; i < points.size(); i++) {
            Arrays.fill(distMatrix[i], UNREACHABLE_MM); // 防止溢出
            distMatrix[i][i] = 0; // 自身距离是0
        }

        // 判断所有点位的互相可达最优距离
        for (Path path : paths) {
            // 判断路径中是否有障碍点
            if (__isPathBlocked(path)) {
                continue;
            }
            int from = xmapPointIdToMatrixId.get(path.getStartPos().getXmapId());
            int to = xmapPointIdToMatrixId.get(path.getEndPos().getXmapId());
            int dist = (int) (path.getLength() * 1000); // 转换为毫米
            // 因为不存在双向，所以这里不需要写to到from的距离
            distMatrix[from][to] = dist;
        }

        long startTime = System.currentTimeMillis();
        // Floyd-Warshall算法计算所有点位间最短距离
        // 多线程并发,并行化中间层循环
        // 优势：避免频繁加锁，适合 CPU 密集型任务。
        IntStream.range(0, points.size()).parallel().forEach(k -> {
            for (int i = 0; i < points.size(); i++) {
                for (int j = 0; j < points.size(); j++) {
                    int nd = distMatrix[i][k] + distMatrix[k][j];
                    if (nd < distMatrix[i][j]) {
                        distMatrix[i][j] = nd;
                    }
                }
            }
        });
        log.debug("计算最短距离耗时：{}ms", System.currentTimeMillis() - startTime);

        this.distMatrix = distMatrix;
        log.debug("地图[{}]距离矩阵预加载完成，点位数量：{}，消耗内存：{}MB", name, points.size(), points.size() * points.size() * 4 / 1024 / 1024);
    }

    /**
     * 加载所有点位和路径后，可以通过这个方法自动确认哪些是十字路口
     */
    public void calcCrossroads() {
        // 最后计算是否是十字路口
        IntStream.range(0, paths.size()).parallel().forEach(i -> {
            Path p = paths.get(i);
            List<IPath> nextPaths = this.getNextPaths(p.getStartPos());
            if (nextPaths.size() > 2) {
                p.getStartPos().setCrossroads(true);
            }
        });
    }

    /**
     * 获取两点间距离
     *
     * @param fromXmapId 起点xmapId
     * @param toXmapId   终点xmapId
     * @return 距离米（单位毫米转换会有误差）；
     * 如果不可达则返回 UNREACHABLE_MM (Integer.MAX_VALUE / 2)
     */
    public double getDist(String fromXmapId, String toXmapId) {
        try {
            return distMatrix[xmapPointIdToMatrixId.get(fromXmapId)][xmapPointIdToMatrixId.get(toXmapId)] / 1000.0;
        } catch (Exception e) {
            return UNREACHABLE_MM;
        }
    }


    /**
     * 克隆并返回一份新的临时距离矩阵，供临时性增加不希望走的路径（障碍）使用
     */
    public TempDistMatrix newTempDistMatrix(@NonNull String... addObstacleXmapPathId) {
        return new TempDistMatrix(this, obstacles, xmapPointIdToMatrixId, addObstacleXmapPathId);
    }

    // endregion


    // region 👇地图基础属性

    /**
     * 地图id
     **/
    @Getter
    @Setter
    private long mapId;
    /**
     * 地图名称
     **/
    @Getter
    @Setter
    private String name;
    /**
     * 地图最小坐标
     **/
    @Getter
    @Setter
    private volatile Position minPos;
    /**
     * 地图最大坐标
     **/
    @Getter
    @Setter
    private volatile Position maxPos;
    /**
     * 地图宽度
     **/
    @Getter
    @Setter
    private volatile double width;
    /**
     * 地图高度
     **/
    @Getter
    @Setter
    private volatile double height;
    /**
     * 地图路径, 取消both，如果是both则拆成两条路径
     * path的start、end包含了完整的业务点位类型信息
     **/
    @Getter
    @Setter
    private volatile List<Path> paths = new ArrayList<>();

    /**
     * 地图点位
     **/
    @Getter
    private volatile List<Position> points = new ArrayList<>();

    /**
     * Setter
     *
     * @param refreshMatrix 是否刷新距离矩阵
     * @param points        点位列表
     */
    public void setPoints(List<Position> points, boolean refreshMatrix) {
        if (points == null) return;

        xmapPointIdToMatrixId.clear();

        for (int i = 0; i < points.size(); i++) {
            points.get(i).setIndex(i);
            xmapPointIdToMatrixId.put(points.get(i).getXmapId(), i);
        }

        this.points = points;

        if (refreshMatrix) {
            preloadMatrix();
        }
    }

    // endregion


    // region 👇障碍

    /**
     * 地图障碍
     **/
    private final List<Obstacle> obstacles = new Vector<>();

    /**
     * 添加障碍
     *
     * @param obstacle 障碍
     */
    public void addObstacle(Obstacle obstacle) {
        obstacles.add(obstacle);
        // 必须重新加载距离矩阵
        preloadMatrix();
    }

    /**
     * 添加障碍
     *
     * @param obstacles 障碍列表
     */
    public void addObstacles(List<Obstacle> obstacles) {
        if (!obstacles.isEmpty())
            this.obstacles.addAll(obstacles);
        // 必须重新加载距离矩阵
        preloadMatrix();
    }

    /**
     * 移除障碍
     *
     * @param obstacle 障碍
     */
    public void removeObstacle(Obstacle obstacle) {
        obstacles.remove(obstacle);
        // 必须重新加载距离矩阵
        preloadMatrix();
    }

    public int obstacleSize() {
        return obstacles.size();
    }

    public Obstacle getObstacle(int idx) {
        return obstacles.get(idx);
    }

    public boolean containsObstacle(String xmapPathId) {
        return obstacles.stream().anyMatch(o -> o.getXmapPathId().equals(xmapPathId));
    }

    @Override
    public String getObstacleXmapPathId(int idx) {
        if (idx < 0 || idx >= obstacles.size()) {
            return null;
        }
        return obstacles.get(idx).getXmapPathId();
    }

    // endregion


    // region 👇地图上下文缓存

    /**
     * 缓存静态数组
     */
    private static final Map<Long, MapContext> cache = new ConcurrentHashMap<>();

    @NonNull
    public static Collection<MapContext> values() {
        return cache.values();
    }

    /**
     * 获取地图上下文
     *
     * @throws ServiceException 如果地图上下文不存在则抛出异常
     */
    @NonNull
    public static MapContext get(long mapId) {
        MapContext mapContext = cache.get(mapId);
        if (mapContext == null) {
            throw new ServiceException("地图[" + mapId + "]上下文不存在");
        }
        return mapContext;
    }

    /**
     * 获取地图上下文
     *
     * @throws ServiceException 如果地图上下文不存在或机器人上下文不存在则抛出异常
     */
    public static MapContext getByRobotId(long robotId) {
        return get(RobotContext.get(robotId).getMapId());
    }

    public static boolean exists(long mapId) {
        return cache.containsKey(mapId);
    }

    /**
     * 缓存地图上下文
     */
    public static void put(MapContext mapContext) {
        cache.put(mapContext.getMapId(), mapContext);
    }

    /**
     * 移除地图上下文
     */
    public static void remove(long mapId) {
        cache.remove(mapId);
    }

    /**
     * 清空缓存
     */
    public static void clear() {
        cache.clear();
    }

    public static int size() {
        return cache.size();
    }

    /**
     * 获取地图点位
     *
     * @param xmapPointId xmap地图点位id
     * @param mapId       地图id
     * @return 点位
     */
    @Nullable
    public static Position getXmapPoint(@NonNull String xmapPointId, long mapId) {
        return get(mapId).getPoints().stream()
                .filter(p -> xmapPointId.equals(p.getXmapId()))
                .findAny().orElse(null);
    }

    /**
     * 获取所有指定类型的地图点位
     *
     * @param className 点位类型
     * @param mapId     地图id
     * @return 点位列表
     */
    @NonNull
    public static List<Position> getPointsByClass(PointClassNameEnum className, long mapId) {
        return get(mapId).getPoints().stream()
                .filter(p -> className.getCode().equals(p.getClassName()))
                .collect(Collectors.toList());
    }

    /**
     * 获取地图路径
     *
     * @param pathId 路径id
     * @param mapId  地图id
     * @return 路径
     */
    @Nullable
    public static Path getPath(@NonNull Long pathId, long mapId) {
        MapContext mapContext = get(mapId);
        if (mapContext.getPaths() == null) {
            return null;
        }

        return mapContext.getPaths().stream()
                .filter(p -> pathId.longValue() == p.getPathId())
                .findFirst().orElse(null);
    }

    /**
     * 获取地图路径
     *
     * @param xmapPathId xmap路径id
     * @param mapId      地图id
     * @return 路径
     */
    @NonNull
    public static List<Path> getPathsByXmapId(@NonNull String xmapPathId, long mapId) {
        return get(mapId).getPaths().stream()
                .filter(p -> xmapPathId.equals(p.getXmapId()))
                .collect(Collectors.toList());
    }

    // endregion


    // region 👇辅助路径计算方法

    /**
     * 检查路径是否走入死胡同
     *
     * @param mapId    地图id
     * @param tailPath 路径链尾部路径
     * @return true走入死胡同，false没有走入死胡同
     */
    public static boolean isImpasse(long mapId, @NonNull IPath tailPath) {
        MapContext mc = get(mapId);
        List<Path> nextPaths = mc.getPaths().stream()
                .filter(x -> x.isStartPoint(tailPath.getEndPos())
                        && !x.equalsOrReverse(tailPath))
                .collect(Collectors.toList());

        if (nextPaths.isEmpty()) {
            return true;
        }

        return nextPaths.stream().allMatch(mc::__isPathBlocked);
    }

    /**
     * 尝试获取反方向的path
     * 没有找到则返回null
     */
    @Nullable
    public static Path getReversePath(@NonNull IPath path, long mapId) {
        return get(mapId).getPaths().stream()
                .filter(p -> p.isReversePath(path))
                .findAny().orElse(null);
    }

    /**
     * 获取当前路径紧邻的上游路径（从哪里能到当前路径的起点）
     *
     * @param currentPath 当前路径
     * @return 上游路径列表
     */
    @NonNull
    public List<IPath> getPreviousPath(@NonNull Path currentPath) {
        return getPaths().stream()
                .filter(p -> p.isEndPoint(currentPath.getStartPos()))
                .collect(Collectors.toList());
    }

    /**
     * 获取当前点位的下游路径（当前点位可以走到哪条路径）
     *
     * @param currentPos 当前点位
     * @return 下游路径列表
     */
    @NonNull
    public List<IPath> getNextPaths(@NonNull IPosition currentPos) {
        return getNextPaths(currentPos, null);
    }

    /**
     * 获取当前点位的下游路径，排除障碍路径以及不想走的路径
     *
     * @param currentPos         当前点位
     * @param excludePathXmapIds 排除的路径
     * @return 下游路径列表
     */
    @NonNull
    public List<IPath> getNextPaths(@NonNull IPosition currentPos, List<String> excludePathXmapIds) {
        List<IPath> findPaths = getPaths().stream()
                .filter(p -> p.isStartPoint(currentPos) &&
                        obstacles.stream().noneMatch(o -> o.getXmapPathId().equals(p.getXmapId())))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(excludePathXmapIds)) {
            findPaths.forEach(fp -> excludePathXmapIds.forEach(ep -> {
                if (Objects.equals(ep, fp.getXmapId())) {
                    findPaths.remove(fp);
                }
            }));
        }

        return findPaths;
    }

    // endregion


    // region 👇私有方法

    private boolean __isPathBlocked(Path path) {
        return !obstacles.isEmpty() &&
                ((obstacles.size() == 1 && path.getXmapId().equals(obstacles.get(0).getXmapPathId())) ||
                        obstacles.stream().anyMatch(o -> path.getXmapId().equals(o.getXmapPathId()))
                );
    }

    // endregion


    // region 👇停车位

    @Getter
    private final Collection<ParkPositionBo> parkPoints = new ConcurrentLinkedDeque<>();

    // endregion

}
