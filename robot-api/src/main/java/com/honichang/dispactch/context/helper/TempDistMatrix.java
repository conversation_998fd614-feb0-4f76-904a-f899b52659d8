package com.honichang.dispactch.context.helper;

import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.honichang.dispactch.context.MapContext.UNREACHABLE_MM;

/**
 * 临时距离矩阵，仅供临时性增加不希望走的路径（障碍）<br/>
 * 用过就要丢弃
 *
 * <AUTHOR>
 */
public class TempDistMatrix implements IDistMatrix {

    /**
     * 全新的距离矩阵，不污染MapContext
     */
    @Getter
    private final int[][] distMatrix;

    /**
     * 缓存xmapPointId与距离矩阵id的关联关系
     */
    private final Map<String, Integer> xmapPointIdToMatrixId;

    private final List<String> obstacleXmapIds;

    private final MapContext mapContext;

    /**
     * 预热距离矩阵
     *
     * @param mc 地图上下文
     * @param obstacles 地图当下的障碍列表（路径xmapId）
     * @param xmapPointIdToMatrixId 地图xmapPointId与距离矩阵id的关联关系
     * @param addObstacleXmapPathId 要临时附加的不想经过的路径xmapId
     */
    public TempDistMatrix(
            @NonNull MapContext mc,
            @NonNull List<Obstacle> obstacles,
            @NonNull Map<String, Integer> xmapPointIdToMatrixId,
            @NonNull String... addObstacleXmapPathId) {

        this.mapContext = mc;

        if (CollectionUtils.isNotEmpty(obstacles)) {
            obstacleXmapIds = obstacles.stream().map(Obstacle::getXmapPathId).collect(Collectors.toList());
        } else {
            obstacleXmapIds = new ArrayList<>();
        }
        if (addObstacleXmapPathId.length > 0) {
            obstacleXmapIds.addAll(Arrays.asList(addObstacleXmapPathId));
        }

        this.xmapPointIdToMatrixId = xmapPointIdToMatrixId;

        int pointCount = mc.getPoints().size();

        distMatrix = new int[pointCount][pointCount];

        // 初始化最大值（表示不可达）
        for (int i = 0; i < pointCount; i++) {
            Arrays.fill(distMatrix[i], UNREACHABLE_MM); // 防止溢出
            distMatrix[i][i] = 0; // 自身距离是0
        }

        // 判断所有点位的互相可达最优距离
        for (IPath path : mc.getPaths()) {
            // 判断路径中是否有障碍点
            if (__isPathBlocked(path)) {
                continue;
            }
            int from = xmapPointIdToMatrixId.get(path.getStartPos().getXmapId());
            int to = xmapPointIdToMatrixId.get(path.getEndPos().getXmapId());
            int dist = (int) (path.getLength() * 1000); // 转换为毫米
            // 因为不存在双向，所以这里不需要写to到from的距离
            distMatrix[from][to] = dist;
        }

        // Floyd-Warshall算法计算所有点位间最短距离
        // 多线程并发,并行化中间层循环
        // 优势：避免频繁加锁，适合 CPU 密集型任务。
        IntStream.range(0, pointCount).parallel().forEach(k -> {
            for (int i = 0; i < pointCount; i++) {
                for (int j = 0; j < pointCount; j++) {
                    int nd = distMatrix[i][k] + distMatrix[k][j];
                    if (nd < distMatrix[i][j]) {
                        distMatrix[i][j] = nd;
                    }
                }
            }
        });
    }

    private boolean __isPathBlocked(IPath path) {
        return !obstacleXmapIds.isEmpty() &&
                ((obstacleXmapIds.size() == 1 && path.getXmapId().equals(obstacleXmapIds.get(0))) ||
                        obstacleXmapIds.stream().anyMatch(o -> path.getXmapId().equals(o)));
    }


    @Override
    public int obstacleSize() {
        return obstacleXmapIds.size();
    }

    @Override
    public boolean containsObstacle(String xmapPathId) {
        return obstacleXmapIds.contains(xmapPathId);
    }

    @Override
    public String getObstacleXmapPathId(int idx) {
        return obstacleXmapIds.get(idx);
    }

    @Override
    public List<Path> getPaths() {
        return mapContext.getPaths();
    }

    /**
     * 获取两点间距离
     *
     * @param fromXmapId 起点xmapId
     * @param toXmapId   终点xmapId
     * @return 距离米（单位毫米转换会有误差）；
     * 如果不可达则返回 UNREACHABLE_MM (Integer.MAX_VALUE / 2)
     */
    @Override
    public double getDist(String fromXmapId, String toXmapId) {
        try {
            return distMatrix[xmapPointIdToMatrixId.get(fromXmapId)][xmapPointIdToMatrixId.get(toXmapId)] / 1000.0;
        } catch (Exception e) {
            return UNREACHABLE_MM;
        }
    }

}
