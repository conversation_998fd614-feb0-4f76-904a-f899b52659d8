/**
  以biz_point表为核心
  left join task_instance_node b
  并按b表的update_timed倒叙排序，每一个biz_point只取最后更新的一条
 */
with cte_rn as (select b.*, row_number() over (PARTITION BY b.biz_point_id ORDER BY b.iden_time DESC) as rn
                from task_instance a,
                     task_instance_node b
                where a.id = b.task_instance_id
                  and a.create_time >= current_date - interval '1 month')
select b.biz_point_id
     , a.class_name
     , a.instance_name
     , b.iden_time
     , case b.audit_status
           when '1' then '审核通过'
           when '2' then '审核不通过'
           else '未审核'
    end as audit_status
     , case b.status
           when '0' then '未抵达'
           when '1' then '执行中'
           when '3' then '正常'
           when '7' then '识别异常'
           when '8' then '不可达'
           else b.rule_result
    end as rule_result
     , b.rule_result_dict
from biz_point a
         left join cte_rn b on (a.id = b.biz_point_id and b.rn = 1)
;
