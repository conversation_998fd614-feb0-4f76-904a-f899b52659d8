# 调度中心

## 结构体

### RobotContext机器人调度上下文

* 需要缓存
* 独立线程将其刷入DB

```java
public class RobotContext {
    //↓--------不常变属性
    /** 机器人id **/
    private long robotId;
    /** 地图id **/
    private long mapId;
    /** 机器人名称 **/
    private String robotName;
    /** 机器人序列号 **/
    private String sn;
    /** 机器人ip **/
    private String ip;
    /** 机器人端口 **/
    private int port;
    /** 机器人appCode **/
    private String appCode;
    /** 机器人优先级 **/
    private int priority;

    //↓--------常变属性
    /** 机器人当前坐标（米） **/
    private Position position;
    /** 机器人当前朝向（弧度rad） **/
    private double theta;

    /** 机器人当前所在路径 **/
    private String xmapPathId;
    /** 机器人当前所在点位 **/
    private String xmapPointId;
    /** 机器人当前所在路径的起始点位 **/
    private String xmapPathStartId;
    /** 机器人当前所在路径的截止点位 **/
    private String xmapPathEndId;
    /** 当前是正走还是倒走：1正走 | 2倒走 **/
    private int direction;
    /** 机器人当前任务类型 **/
    private int latestTaskClass;
    /** 机器人当前任务id **/
    private long latestTaskId;

    /** 机器人累计运行时间（分钟） **/
    private long totalRunning;
    /** 机器人累计里程数（米） **/
    private double totalOdom;
    /** 机器人当前电量 **/
    private double battery;

    /** 机器人状态: 0待机 | 1工作 | 4暂停 | 6定位中 |
     701载入地图失败 | 702定位失败 | 703导航失败
     8离线 | 9故障 **/
    private String status;

    /** 机器人当前工作状态（主优先级） **/
    private int workStatus;

    /** 工作模式：101自动模式 | 102遥控模式(后端遥控) | 201手动模式(厂家遥控器) **/
    private int workMode;
    /** 遥控模式最后操作时间，用于超时切回自动模式 **/
    private Date lastRemoteControlTime;

    /** 当前是否被阻挡 **/
    private boolean isBlocked;
    /** 阻挡开始时间，用于计算超时阈值 **/
    private Date blockStartTime;

    /** 设定runStep完成百分比（0.0-1.0）；0或1走完 **/
    private double runStepPercent;

    /**
     * 机器人任务队列
     * 包含正在进行的和等待执行的任务
     **/
    private TaskChain taskQueue;
    /** 预分配路径资源 **/
    private PathChain preAllocatedPaths;
    /** 已完成路径资源 **/
    private PathChain completedPaths;
}
```

### MapContext地图上下文

* 需要缓存

```java
public class MapContext {
    /** 地图id **/
    private long mapId;
    /** 地图名称 **/
    private String name;
    /** 地图类型 **/
    private String type;
    /** 地图最小坐标 **/
    private Position minPos;
    /** 地图最大坐标 **/
    private Position maxPos;
    /** 地图宽度 **/
    private double width;
    /** 地图高度 **/
    private double height;
    /** 地图路径, 取消both，如果是both则拆成两条路径 **/
    private List<Path> paths;
    /** 地图点位 **/
    private List<Point> points;
    /** 地图障碍 **/
    private List<Obstacle> obstacles;
}
```

### OfflineTask离线任务下发（暂不考虑）

```java
public class OfflineTask {
    /** 任务链列表 **/
    /** 每个点位的云台配置 **/
}
```

### TaskEscortContext陪同任务上下文

```java
public class TaskEscortContext {
    /** 陪同任务id **/
    private long id;
    /** 陪同任务名称 **/
    private String taskName;
    /** 执行类型: 1-立即执行 | 2-预约执行 **/
    private int execType;
    /** 陪同任务执行时间 **/
    private Date execTime;
    /** 陪同任务超时时间: 允许等待的时间（分钟） **/
    private Integer timeoutMin;
    /** 陪同任务状态 **/
    private String status;
    /** 陪同任务开始时间 **/
    private Date startTime;
    /** 陪同任务结束时间 **/
    private Date endTime;
    /** 陪同任务等待点位 **/
    private String waitPointId;
    /** 机器人到达等待点时间，用于计算等待超时 **/
    private Date waitArriveTime;
    /** 陪同任务到达点位 **/
    private String arrivePointId;
    /** 陪同任务机器人 **/
    private long robotId;
    /** 陪同任务负责人 **/
    private Long superintendent;
    /** 陪同任务暂停时间 **/
    private Integer pauseMin;
    /** 陪同任务恢复时间 **/
    private Date recoveryTime;
    /** 陪同任务UWB卡id **/
    private Long uwbCardId;
    /** 访客超出范围时间 **/
    private Date outOfRangeTime;
    /** 预分配路径资源 **/
    private PathChain preAllocatedPaths;
    /** 已发送路径资源 **/
    private PathChain hasBeenSendPaths;
    /** 已完成路径资源 **/
    private PathChain completedPaths;
}
```

### TaskChain

```java
public class TaskChain {
    /** 任务节点列表 **/
    private List<TaskNode> nodes;
}
```


## Function

### 查找点位

#### 查找当前位置的最近点位：

* getNearestPoint
* 应用场景：AI报警报告最近柱位

```java
/**
 * 查找当前位置的最近点位
 * 应用场景：AI报警，报告最近柱位
 *
 * @param x 当前坐标（米）
 * @param y 当前坐标（米）
 * @param xmapPathId 当前坐标所在xmap路径id
 * @param mapId 地图id
 * @return 成功返回最近xmap点位，失败返回null
 */
public String getNearestPoint(double x, double y, String xmapPathId, long mapId) {
    // 1. 根据pathId从MapContext中获取路径信息
    // 2. 根据坐标计算路径两段最近的距离
    // 3. 返回最近的点位
}

/**
 * 查找机器人当前位置的最近点位
 * 应用场景：AI报警，报告最近柱位
 *
 * @param robotId 机器人id
 * @return 成功返回最近xmap点位，失败返回null
 */
public String getNearestPoint(long robotId) {
    // 1. 获取机器人当前坐标（可能在路径中央）、当前所在路径
    // 2. 调用getNearestPoint方法
    // 3. 返回最近的点位
}
```

#### 查找机器人背后的最近点位

* getNearestPointBackward
* 应用场景：遭遇障碍后反方向就近停靠

```java
/**
 * 查找机器人背后的最近点位
 * 应用场景：遭遇障碍后反方向就近停靠
 *
 * @param robotId 机器人id
 * @return 成功返回最近xmap点位，失败返回null
 */
public String getNearestPointBackward(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径、朝向、已完成路径资源
    // 2. 根据已完成路径资源，计算机器人背后的路径并返回
}
```

#### 查找最近的十字路口

* getNearestCrossPoint
* 应用场景：①需要在十字路口调头后进入巷道；②需要给其他人让路；

```java
/**
 * 查找已走过或预分配路线中，离点位最近的十字路口
 * @param robotId 机器人id
 * @param xmapPointId 目标地图点位id
 * @return 成功返回十字路口xmap点位，失败返回null
 */
public String getNearestCrossPoint(long robotId, String xmapPointId) {
    // 1. 获取RobotContext中获取预分配路径
    // 2. 计算点位沿预分配路径最近的十字路口点位
    // 3. 返回最近的十字路口点位
}
```

```java
/**
 * 查找最近的十字路口
 * @param robotId 机器人id
 * @return 成功返回十字路口xmap点位，失败返回null
 */
public String getNearestCrossPoint(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 获取所有十字路口类点位
    // 3. 计算可达的最近的十字路口并返回
}
```

### 组合控制（遥控模式）

#### 工作模式切换

* switchWorkMode
* 应用场景：点位配置、智能机器人页面锁头打开后的车体控制
* 要求：
    * 需要特殊权限

```java
/**
 * 想要进行组合控制，必须先切换机器人工作模式到102=遥控模式<br/>
 * **需要特殊权限<p/>
 * 1.智能机器人页面锁头打开进入遥控模式，无操作下倒计时N分钟自动切回自动模式<br/>
 * 2.点位配置进入遥控模式，不会自动切换回来<br/>
 *
 * @param robotId 机器人id
 * @param workMode 工作模式：101自动模式 | 102遥控模式(后端遥控)
 * @param timeout 无操作自动切回自动模式的超时时间，单位秒，0无限
 * @return 成功返回true，失败返回false
 */
public boolean switchWorkMode(long robotId, int workMode, int timeout) {
    // 1. 判断RobotContext中当前机器人状态，如果已经处于此模式下，则直接返回成功
    // 2. workMode=102:切换遥控：
    // ① 判断当前机器人状态，如果处于陪同任务下，则返回失败（陪同不允许遥控）
    // ② 判断当前机器人状态，如果处于自动任务下，则立即原地暂停+取消导航，同时清空当前预分配路径资源
    //   pauseCurrentTask()
    // 3. 调用云台接口复位云台操作杆
    // 4. 切换自动：重新回到之前中断的任务队列上下文，继续调度逻辑
    // 5. 更新RobotContext
    // workMode=101: lastRemoteControlTime=null, status=1
    // workMode=102: lastRemoteControlTime=now, status=4
    // 6. 写日志
}
```

#### 机器人复位

* resetRobot
* 应用场景：智能机器人页面锁头打开后的复位机器人，用于手动挪移机器人后的复位

```java
/**
 * 让当前机器人复位到初始状态
 * 注意：这个点位是xmap点位，不是biz点位
 * @param robotId 机器人id
 * @param x 坐标x
 * @param y 坐标y
 * @param theta 朝向角度rad
 * @return 成功返回true，失败返回false
 */
public boolean resetRobot(long robotId, double x, double y, double theta) {
    // 1. 判断RobotContext中当前机器人状态是否是102=遥控模式，如果不是则返回false
    // 2. 调用机器人api更改为MANU手动模式
    // 3. 调用机器人api手动定位
    // 4. 调用机器人api确认机器人位置
    // 5. 调用机器人api更改为AUTO自动模式
    // 6. 写日志
    // 7. 更新RobotContext
    // lastRemoteControlTime=now
    // 8. 返回成功
}
```

#### 前进到最近点位

* forwardStepPoint
* 应用场景：点位配置、智能机器人页面锁头打开后的车体控制

```java
/**
 * 让当前机器人走入前方最近点位，如果当前在十字路口，则根据theta确定哪里是前方，没有前方则放弃
 * 注意：这个点位是xmap点位，不是biz点位
 * @param robotId 机器人id
 * @return 成功返回前进点位，失败返回null
 */
public String forwardStepPoint(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 根据theta计算机器人当前朝向
    // 3. 根据机器人当前朝向，计算机器人前方的一段路径
    // 4. 根据路径计算路径另一头点位
    // 5. 调用runStepPoint方法
}
```

#### 后退到最近点位

* backwardStepPoint
* 应用场景：点位配置、智能机器人页面锁头打开后的车体控制

```java
/**
 * 让当前机器人退到后方最近点位，如果当前在十字路口，则根据theta确定哪里是后方，没有后方则放弃
 * 注意：这个点位是xmap点位，不是biz点位
 * @param robotId 机器人id
 * @return 成功返回后退点位，失败返回null
 */
public String backwardStepPoint(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 根据theta计算机器人当前朝向
    // 3. 根据机器人当前朝向，计算机器人后方的一段路径
    // 4. 根据路径计算路径另一头点位
    // 5. 调用runStepPoint方法
}
```

### 调度（非手动模式）

#### runStep

* runStepPoint
* 应用场景：遥控模式单一路径指令；自动模式所有调度都要用到这个函数

```java
/**
 * 走一步
 * 应用场景：遥控模式单一路径指令；自动模式所有调度都要用到这个函数
 *
 * @param robotId 机器人id
 * @param pathId 要走的路径id
 * @param startPointId 起始点位id
 * @param endPointId 走向终止点位id
 * @param direction 1正走 | 2倒走
 * @return 成功true，失败返回false
 */
public boolean runStepPoint(long robotId, String pathId, String startPointId, String endPointId, int direction) {
    // 1. 检查当前机器人当前状态
    // 2. 判断路径是否是当前位置的延续
    // 3. 检查path与startPointId、endPointId是否匹配
    // 4. 调整云台方向
    // if RobotContext.direction=1 and direction=2则调用云台180度向后
    // if RobotContext.direction=2 and direction=1则调用云台复位
    // 5. 更新RobotContext
    // _.direction=direction
    // _.xmapPathId=pathId
    // _.xmapPathStartId=startPointId
    // _.xmapPathEndId=endPointId
    // if workMode=102 then _.lastRemoteControlTime=now
    // 6. 调用机器人API
}
```

#### runStepPercent

* runStepPercent
* 应用场景：一个机器人给高优先级的机器人让路，退回到十字路口选择对方不走的方向退

```java
/**
 * 让当前机器人走临近路径的百分比长度
 * 期间遇障碍不汇报，障碍超过1分钟原地暂停
 * 函数体内部分多步+实时监控完成<p/>
 *
 * ①发送从start走向end的指令；<br/>
 * ②实时监控机器人状态判断完成路径百分比；<br/>
 * ③>=percent发送暂停指令<br/>
 * @param robotId 机器人id
 * @param pathId 要走的路径id
 * @param startPointId 起始点位id
 * @param endPointId 走向终止点位id
 * @param percent 百分比，范围0.1-1.0，step=0.1
 * @param direction 1正走 | 2倒走
 * @return 成功返回true，失败返回false
 */
public boolean runStepPercent(long robotId, String pathId, String startPointId, String endPointId, double percent, int direction) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 根据theta计算机器人当前朝向
    // 3. 根据机器人当前朝向，计算机器人前方的一段路径
    // 4. 根据路径计算路径另一头点位
    // 5. 更新RobotContext
    // _.runStepPercent=percent
    // 5. 调用runStepPoint方法
    // 6. 监听并判断机器人是否到达指定百分比
    // 7. 到达指定百分比后，调用pause方法暂停机器人
}
```

#### runTo

* runToPoint
* 应用场景：机器人运行到指定点位

```java
/**
 * 让当前机器人运行到指定点位
 * 必须处于遥控模式下
 *
 * @param robotId 机器人id
 * @param pointId runTo的点位id
 * @return 成功返回true，失败返回false，如果失败见机器人日志
 */
public boolean runToPoint(long robotId, String pointId) {
    // 1. 判断RobotContext中当前机器人状态，如果状态异常则返回false
    // 2. 如果当前机器人不在点位上，则基于当前路径的两端（可达）
    // 3. 计算第2步点位（可能是两个）到达目标点位的最优路径planPath
    // 4. 写RobotContext
    // preAllocatedPaths=最优路径, lastRemoteControlTime=now, workStatus=30900
    // 5. 由Schedule调度一步步下达指令
}
```

### 路径规划

#### 最优路径

* planPath
* 应用场景：任务下发、遭遇障碍后重新规划路径、runTo后回到自动模式重新规划路径

```java
/**
 * 从当前点位到目标点位规划路径，中间不允许跳路径，必须连续
 * ignoreObstacle=false, 要求排除当前地图其他人发现的障碍点
 *
 * @param robotId 机器人id
 * @param startPointId 起始点位id
 * @param endPointId 目标点位id
 * @param ignoreObstacle 是否忽略障碍
 * @return 成功返回路径，失败返回null
 */
public PathChain planPath(long robotId, String startPointId, String endPointId, boolean ignoreObstacle) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 根据当前坐标、当前所在路径、起始点位、目标点位，规划最优路径，按照路径的长度给可能的路径链数组排序
    // 3. 在返回的可选的路径链列表中，循环判断当前是否与其他机器人的预分配路径冲突，如果冲突则找下一条路径链
    // 4. 返回规划的路径
}
```

#### 找最近的无人预约的充电桩

* findNearestAvailableCharger
* 应用场景：电量低时寻找最近的无人预约的充电桩

```java
/**
 * 找最近的无人预约的充电桩
 *
 * @param robotId 机器人id
 * @return 成功返回充电桩id，失败没找到返回null
 */
public Long findNearestAvailableCharger(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 获取所有充电桩点位
    // 3. 计算可达的最近的充电桩点位并返回
}
```

#### 找充电排队最少的充电桩

* findLeastChargerQueue
* 应用场景：电量低时寻找充电排队最少的充电桩

```java
/**
 * 找充电排队最少的充电桩，同样排队数量的找最近的
 *
 * @param robotId 机器人id
 * @return 成功返回充电桩id，失败没找到返回null
 */
public Long findLeastChargerQueue(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 获取所有充电桩点位
    // 3. 计算可达的最近的充电桩点位并返回
}
```

#### 找某点附件最近的空闲停车位

* findNearestAvailableParking
* 应用场景：任务完成后寻找最近的空闲停车位

```java
/**
 * 找某点附件最近的空闲停车位
 *
 * @param robotId 机器人id
 * @param pointId 目标点位id
 * @return 成功返回停车位id，失败没找到返回null
 */
public Long findNearestAvailableParking(long robotId, String pointId) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 获取所有停车位点位
    // 3. 计算可达的最近的停车位点位并返回
}
```

### 交通管制

#### 冲突检测

* predictFutureOccupancy
* 应用场景：预测未来N步是否与其他机器人预分配路径冲突
* 未来N步是否有十字路口，如果有判断是否应该走入十字路口
    * 获取预分配路径的下一个十字路口要进入的下一段一直到下一个十字路口的路径链
    * 判断这一段路径链是否有其他机器人存在，如果是迎面走来，需要等待其出来
    * 判断这一段路径链是否有其他高优先级机器人预定路线，如果有需要等待
    * 在预分配路径的最近十字路口前一个点位等待即可

```java
/**
 * 预测未来N步是否与其他机器人预分配路径冲突
 *
 * @param robotId 机器人id
 * @param steps 预测步数
 * @return 成功返回true，失败返回false
 */
public boolean predictFutureOccupancy(long robotId, int steps) {
    // 1. 获取机器人当前坐标、当前所在路径、预分配路径
    // 2. 计算预分配路径的下一个十字路口要进入的下一段一直到下一个十字路口的路径链
    // 3. 判断这一段路径链是否有其他机器人存在，如果是迎面走来，需要等待其出来
    // 4. 判断这一段路径链是否有其他高优先级机器人预定路线，如果有需要等待
}
```

#### 冲突处理

* resolveConflict
* 应用场景：已经发生冲突，控制优先级低的机器人退到走过的最近十字路口再让出十字路口

```java
/**
 * 处理机器人冲突，控制优先级低的机器人退到走过的最近十字路口再让出十字路口
 *
 * @param robotId 机器人id
 * @return 成功返回true，失败返回false
 */
public boolean resolveConflict(long robotId) {
    // 1. 获取机器人当前坐标、当前所在路径、预分配路径、已完成路径
    // 2. 找到已完成路径最近的十字路口（没有则找身后最近十字路口）
    // 3. 调度机器人退到这个十字路口
    // 4. 判断对面高优先级机器人预分配路径是否包含本十字路口
    // 5. 如果不包含就在这里待命
    // 6. 如果包含则判断对面机器人要拐向那条路径，自己则向其他路径规避，十字路口后最近点位等待
}
```

### 任务

#### 主备切换（低优先级）

* switchTaskMaster
* 场景：机器人离开当前任务工作时，抛出pauseCurrentTaskEvent事件

```java
/**
 * 任务主备切换
 *
 * @param robotId 机器人id
 * @return 成功返回true，失败返回false
 */
public boolean switchTaskMaster(long robotId) {
    // 1. 获取当前机器人下的任务队列
    // 2. 循环任务队列，看任务定义中是否有备用机器人
    // 3. 备用机器人在线则推入任务队列
    // 4. 推入备用机器人的任务，要从当前机器人的任务队列中推出
}
```

#### 给任务排列节点，并规划路径

```java
/**
 * 给任务排列节点，并规划路径
 *
 * @param robotId 机器人id
 * @param taskDefId 任务定义ID
 * @param taskChain 引用参数返回规划后的任务队列
 * @param unreachableNodes 引用参数返回不可达节点
 * @param paths 引用参数返回规划后的路径
 * @return 成功返回true，失败返回false
 */
public boolean planTaskPath(long robotId, long taskDefId, TaskChain taskChain, List<Long> unreachableNodes, PathChain paths) {
    // 1. 获取机器人当前坐标、当前所在路径
    // 2. 获取任务定义的所有任务节点定义
    // 3. 计算当前机器人距离每一个任务节点的距离，如果到A节点经过了B节点说明B是更理想下一个节点，最终循环完将最理想的推入TaskChain
    // 4. 直到所有节点都排完为止，不可达节点做出特殊标记
}
```

## Event

### toChargingEvent 开始充电事件

* payload：robotId
* 场景：预约陪同触发或立即陪同、任务中电量低于阈值
* 非异步事件，要求加事务
* 行为：
    * pauseCurrentTask()
    * toCharge()

### finishChargingEvent 完成充电事件

* payload：robotId
* 场景：充电桩检测到电量充满
* 非异步事件，要求加事务
* 行为：
    * 调用机器人api收回充电口（可能没有？） 
    * restoreTask()

### toEscortEvent 开始陪同事件

* payload: taskEscortId
* 场景：预约陪同触发或立即陪同
    * pauseCurrentTask()
    * escortRun()

### outOfRangeEscortEvent 访客超出陪同距离事件

* payload: robotId
* 场景：uwb检测到访客超出阈值
* 异步事件，无事务
* 行为：
    * `if _.outOfRangeTime!=null && now-outOfRangeTime>config.outOfRangeMinutes`
        * 陪同任务异常终止 `escortAbort()`
        * `return`
    * `if _.outOfRangeTime=null`
        * `set _.outOfRangeTime=now`
        * 调用机器人api：暂停
    * 每间隔5秒钟循环发送语音警告场景：访客距离过远（但不报警）

### arriveWaitPointEvent 到达waitPointId（接客、送客）

* payload: robotId
* 场景：机器人到达waitPointId
* 异步事件，要求加事务
* 行为：
    * `if _.status=101`: 101->102 接访客
        * `set _.status=102`抵达等待点（等待访客）
        * `set _.preAllocatedPaths=empty, _.hasBeenSendPaths=empty`
        * `set _.waitArriveTime=now`
        * 语音播报场景：等待访客
        * 写陪同日志 log_escort
    * `if _.status=401`: 401->402 送访客
        * `set _.status=402`抵达出口（等待访客确认离开）
        * `set _.preAllocatedPaths=empty, _.hasBeenSendPaths=empty`
        * 语音播报场景：访客到达目的地
        * 写陪同日志 log_escort

### arriveTargetPointEvent 到达arrivePointId（送到目的地）

* payload: robotId
* 场景：机器人到达arrivePointId
* 异步事件，要求加事务
* 行为：
    * `set _.status=301`抵达目的地（等待访客确认完成任务）
    * `set _.preAllocatedPaths=empty, _.hasBeenSendPaths=empty`
    * 语音播报场景：访客到达目的地
    * 写陪同日志 log_escort

### blockEvent 遭遇障碍

* payload: robotId
* 场景：机器人遇到障碍
* 异步事件，要求加事务
* 行为：
    * RobotContext中记录是否处于被阻挡状态 `if _.isBlocked=false`
        * 更新RobotContext
            * `set _.isBlocked=true, _.blockStartTime=now`
            * 语音播报场景：遇到障碍
    * `if _.isBlocked=true`
        * `if now-_.blockStartTime>config.blockAlarmTimeout`
            * 调用`getNearestPointBackward()`获取障碍最近走过的点位
            * 清空 `_.preAllocatedPaths`
            * 强行后退一下 `backwardStepPoint()`
            * 重新规划`_.preAllocatedPaths`中的路径，从后退的那个点位开始
            * 更新RobotContext
                * `set _.isBlocked=false, _.blockStartTime=null`
        * `else`
            * 间隔5秒循环播报语音场景：遇到障碍
    * 写机器人日志 log_robot

## Schedule

### **核心调度**

* 核心常驻守护线程，无限循环读取所有机器人的状态信息
* 更新RobotContext中的状态信息（地图刷新也依据RobotContext而非DB）
    * 调用机器人api获取机器人状态
    * 刷入 RobotContext

* 是否在遥控模式下 `if RobotContext.workMode=102`
  * `if now-_.lastRemoteControlTime>config.remoteControlTimeout`
    * `switchWorkMode(robotId, 101, 0)`

* 陪同任务TaskEscortContext
    * 路径调度：`if _.outOfRangeTime!=null && _.preAllocatedPaths<>empty && _.hasBeenSendPaths.size<2`
        * 为保证机器人不卡顿，判断当前机器人是否进入_.hasBeenSendPaths.head，已进入则继续发一条runStep函数
        * 每走完一步要将_.hasBeenSendPaths.head换掉
    * 距离报警：`if _.status>=201 && _.status<=401 && _.status<>302`，则检测uwb距离
        * 超出距离 `uwbDistance>config.outOfRangeDistance`
            * 发送事件 **outOfRangeEscortEvent**
        * 未超出距离清空 `if _.outOfRangeTime!=null && uwbDistance<=config.outOfRangeDistance`
            * `set _.outOfRangeTime=null`
            * 调用机器人api：继续
        * 等待超时判断：`if _status=102`
            * `if minute(now-_.waitArriveTime)>_.timeoutMin`
                * 陪同任务异常终止 `escortAbort()`
            * else
                * 每间隔5秒钟循环播报语音场景：等待访客
    * 关闭报警超时：`if _.status=302 && now>_.recoveryTime`
        * `set _.status=301, _.outOfRangeTime=null, _.recoveryTime=null, _.pauseMin=null`
        * 写陪同日志
    * 抵达判断：
        * 到达waitPointId (101->102 || 401->402) `if _.status in (101,401)`
            * 发送事件 **arriveWaitPointEvent**
        * 到达arrivePointId (201->301): `if _.status=201` 
            * 发送事件 **arriveTargetPointEvent**

* 百分比行进逻辑 `if RobotContext.runStepPercent>0.0 and <1.0`
    * 判断当前机器人是否已经完成计划的百分比
    * 如果完成则：
        * 发送机器人api暂停指令
        * `set runStepPercent=0.0`

* 核心常驻守护线程，无限循环读取所有机器人上下文信息
* 任务中RobotContext.status=1
    * 陪同任务 if workMode=101 and workStatus=1
        * 调用runStep函数
    * if workMode=101 and workStatus=4
        * 调用pause函数

### 遭遇障碍倒计时

* 每秒读取RobotContext障碍信息
* 遭遇障碍后`if isBlocked=true`：
    * `set blockStartTime=now`
    * 发送循环语音指令；
    * 写日志
* 阈值内`if isBlocked=false`：
    * 障碍移开则继续当前任务；
    * 取消循环语音指令；
    * 写日志
* 超出阈值if now-blockStartTime>阈值：
    * 汇报障碍；
    * 调用getNearestPointBackward函数，后退就近停靠；
    * 重新规划路径：
        * 清空RobotContext中预分配路径资源；
        * 重新调用路径规划函数，生成新的预分配路径资源；
    * set isBlocked=false, blockStartTime=null；
    * 取消循环语音指令；
    * 写日志

### 实时刷入DB

* （单例线程）每N秒触发，自动将RobotContext中的状态信息刷入DB

### 陪同任务

#### 预约陪同电量判断

* 每60秒判断机器人是否有预约任务
    * 预约时间进入3小时内，电量小于80%
        * 暂停当前任务队列
        * 计算最近的没有预约充电的充电桩，返回路径链
        * 导航前往充电（高优先级）
        * 写日志
    * 预约时间进入20分钟内，电量>75% and <80%
        * 暂停当前任务队列
        * 不去充电了，直接前往接人出发点，走陪同逻辑

#### 陪同触发

* 每60秒判断预约时间进入10分钟内（含立即执行）
  * toEscortEvent




## 场景

### 智能机器人实时大屏（实时地图）

#### 地图画布

* 刷新频率：4帧（即250ms刷新一次）
* 通讯方式：Websocket长连接
* 实时显示机器人状态&位置
    * RobotContext.status状态颜色对应关系
        * 蓝色闪烁：6定位中
        * 蓝色：0待机 | 3__充电/升级
        * 绿色：1工作 | 4暂停
        * 灰色：8离线
        * 橙色：901手动故障
        * 红色：7__定位失败 | 902硬件报警
* 轨迹复选框勾选
    * RobotContext.completedPaths已完成路径资源
        * 紫色带阴影线条
    * RobotContext.preAllocatedPaths预分配路径资源
        * 半透明紫色线条
* 机器人列表及其他状态信息
    * 略
* 机器人朝向有一个半透明的扇形辐射标记
* 点位图层
    * 识别点位复选框
        * 显示所有识别点位在地图上（除正常外，都永久显示，直到变绿）
        * 绿色：正常
        * 黄色：识别异常
        * 红色：识别报警
        * 灰色：不可达
    * 标记点
        * 固定一个图标颜色
    * 十字路口
        * 固定一个图标颜色
    * 出口
        * 固定一个图标颜色
    * 红外测温
        * 绿色：正常
        * 红色：报警（永久显示，直到变绿）

#### 右侧机器人清单

* RobotContext详情

#### 车体控制（页面小锁头）

* switchWorkMode
* 前后
    * forwardStepPoint
    * backwardStepPoint
* runTo
    * 遥控模式下点击点位，context菜单出现runTo
    * runToPoint
* 暂停
* 重定位
    * 选择复位点位
    * resetRobot
* 云台
    * 伸缩杆高低
        * 支持输入数值
    * 复位伸缩杆
* 可见光摄像头
    * 自动聚焦
    * 变焦+-
    * 变倍+-

#### 故障

* 设为故障
* 查看故障记录
* 复位（取消故障）

### 陪同任务

#### escortRun: 0->101

* 说明：陪同任务立即执行
* 检查RobotContext
    * `status in (0,1,3,4)`，其他要报错写日志
    * `taskQueue.head.status=1`
* 更新task_escort数据库表
    * `status=101`接访客
    * `startTime=now`
* 写陪同日志
* 更新RobotContext
    * `workStatus=10100`陪同任务
    * `status=1`工作
    * `preAllocatedPaths=从当前位置到waitPointId的路径`
    * `completedPaths=empty`
* 更新EscortContext
    * `status=101`接访客
    * `startTime=now`
    * `preAllocatedPaths=RobotContext.preAllocatedPaths`
    * `hasBeenSendPaths.add(preAllocatedPaths.head)`
    * `completedPaths=empty`
* 由Schedule调度一步步下达指令前进

#### escortConfirmVisit: 102->201

* 说明：访客到达起始点确认
* 场景：访客触摸屏点击确认到达
* 检查：`if status!=102 then return false`
* 更新task_escort数据库表
    * `set status='201';`陪同中
* 写陪同日志
* 更新RobotContext
    * `set preAllocatedPaths=从当前位置到arrivePointId的路径`
    * `set completedPaths=empty`
* 更新EscortContext
    * `set status=201`陪同中
    * `set outOfRangeTime=null`
    * `set preAllocatedPaths=RobotContext.preAllocatedPaths`
    * `set hasBeenSendPaths.add(preAllocatedPaths.head)`
    * `set completedPaths=RobotContext.completedPaths`
* 由Schedule调度一步步下达指令前进

#### escortConfirmComplete: 301->401

* 说明：访客在目的地完成工作后确认
* 场景：访客触摸屏点击确认本次目的完成，随后调度机器人送访客
* 检查：`if status!=301 || 302 then return false`
* 更新task_escort数据库表
    * `set status='401';` 送访客
* 写陪同日志
* 更新RobotContext
    * `set preAllocatedPaths=从当前位置到waitPointId的路径`
    * `set completedPaths=empty`
* 更新EscortContext
    * `set status=401` 送访客
    * `set outOfRangeTime=null`
    * `set preAllocatedPaths=RobotContext.preAllocatedPaths`
    * `set hasBeenSendPaths.add(preAllocatedPaths.head)`
    * `set completedPaths=RobotContext.completedPaths`
* 由Schedule调度一步步下达指令前进

#### escortPauseAlarm: 301->302

* 说明：关闭报警（暂停访客距离逻辑判断）
* 场景：管理员暂时陪同任务关闭报警
* 判断：
    * 只能在抵达目标点才能执行此操作 `if status!=301 then return false`
* 更新task_escort数据库表
    * `set status=302`
    * `set recovery_time=now+填写的超时分钟`
* 更新TaskEscortContext
* 写陪同日志

#### escortRestoreAlarm: 302->301

* 说明：取消关闭报警（恢复访客距离逻辑判断）
* 场景：管理员恢复陪同任务关闭报警
* 判断：
    * 只能在关闭报警状态下才能执行此操作 `if status!=302 then return false`
* 更新task_escort数据库表
    * `set status=301, _.outOfRangeTime=null, _.recoveryTime=null, _.pauseMin=null`
* 更新TaskEscortContext
* 写陪同日志

#### escortAbort: XXX -> XX9

* 说明：陪同任务异常终止
* 更新task_escort数据库表
    * `status=substring(status, 2) + '9';`
    * `endTime=now`
* 写陪同报警
* 给主办负责人发送报警通知
    * insert: 消息推送表
* 写陪同日志
* 更新RobotContext
    * `status=1`工作
    * `preAllocatedPaths=empty`
    * `completedPaths=empty`
* 销毁EscortContext
* ↓ @Transactional(propagation = Propagation.REQUIRES_NEW)
* 继续这个机器人的普通任务: restoreTask
* 根据restoreTask返回值更新RobotContext：
    * 0: `workStatus=10300`陪同返航
    * 1: `workStatus=60100`周期任务
    * 2: `workStatus=60200`普通任务

#### escortCompleteLeave: 402->501!

* 说明：访客到达出口确认，陪同任务完成
* 场景：①访客触摸屏点击确认结束；②`status=402` && uwb卡超出阈值5分钟后
* 更新task_escort数据库表
    * `status='501';`
    * `endTime=now`
* 给主办负责人发送报警通知
    * insert: 消息推送表
* 更新RobotContext
    * `status=1`工作
    * `preAllocatedPaths=empty`
    * `completedPaths=empty`
* 写陪同日志
* 销毁EscortContext
* ↓ @Transactional(propagation = Propagation.REQUIRES_NEW)
* 继续这个机器人的任务: restoreTask
* 根据restoreTask返回值更新RobotContext：
    * 0: `workStatus=10300`陪同返航
    * 1: `workStatus=60100`周期任务
    * 2: `workStatus=60200`普通任务




### 普通任务

#### initTask

* 说明：初始化所有机器人的任务队列
* 场景：服务初启动时调用一次
* 从task_def/task_instance中读取
  * task_def周期任务已到期的推入顶层
  * task_instance未完成的任务推入
  * task_def按优先级推入

#### restoreTask

* 说明：恢复指定机器人的任务队列
* 场景：①进入遥控模式再次恢复回自动模式时；②陪同任务完成或终止时；③新布置机器人时
* 返回：有周期任务返回1，有普通任务返回2，没有任务返回0
* 事务：@Transactional(propagation = Propagation.REQUIRES_NEW)
* 更新task_instance状态
    * `if _.status=4 then _.status=1`执行中
* 更新RobotContext
    * 根据任务队列.head判断 `workStatus=60100`周期任务 | `workStatus=60200`普通任务
    * `status=1`工作
    * `preAllocatedPaths=重新规划任务节点顺序及路径`
    * `completedPaths=empty`
* 由Schedule调度一步步下达指令前进

#### pauseCurrentTask

* 说明：暂停当前任务
* 参数：robotId, pauseReason
* 场景：toChargingEvent事件触发、remoteControlEvent事件触发、toEscortEvent事件触发
* 更新task_instance状态为暂停
    * `set task_instance.status=4`
* 写机器人日志
* 调用机器人api发送暂停指令


### 充电任务

#### toCharge





## 系统配置 config

* 陪同任务报警阈值(超出多少米): outOfRangeDistance
* 陪同任务报警阈值(超出距离持续多少分钟): outOfRangeMinutes
* 遥控模式idle超时恢复(分钟): remoteControlTimeout
* 遇到障碍报警阈值(分钟): blockAlarmTimeout

## 注意事项

* 机器人地图点位记录了angle朝向角，因此如果在一个地图点位上所有两边都要采集识别，请定义两个采集点位。
* path如果是单向的，只能从start到end，至于正走倒走不关心
* 机器人只能在十字路口调头，其他任何点位不允许调头

## 资源

### 图标

*

头朝向wifi：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"/></svg>

* 机器人robot：
*

识别点compass：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm50.7-186.9L162.4 380.6c-19.4 7.5-38.5-11.6-31-31l55.5-144.3c3.3-8.5 9.9-15.1 18.4-18.4l144.3-55.5c19.4-7.5 38.5 11.6 31 31L325.1 306.7c-3.2 8.5-9.9 15.1-18.4 18.4zM288 256a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"/></svg>

*

标记点mark：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M384 192c0 87.4-117 243-168.3 307.2c-12.3 15.3-35.1 15.3-47.4 0C117 435 0 279.4 0 192C0 86 86 0 192 0S384 86 384 192z"/></svg>

*

十字路口crosshairs：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M256 0c17.7 0 32 14.3 32 32l0 10.4c93.7 13.9 167.7 88 181.6 181.6l10.4 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-10.4 0c-13.9 93.7-88 167.7-181.6 181.6l0 10.4c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-10.4C130.3 455.7 56.3 381.7 42.4 288L32 288c-17.7 0-32-14.3-32-32s14.3-32 32-32l10.4 0C56.3 130.3 130.3 56.3 224 42.4L224 32c0-17.7 14.3-32 32-32zM107.4 288c12.5 58.3 58.4 104.1 116.6 116.6l0-20.6c0-17.7 14.3-32 32-32s32 14.3 32 32l0 20.6c58.3-12.5 104.1-58.4 116.6-116.6L384 288c-17.7 0-32-14.3-32-32s14.3-32 32-32l20.6 0C392.1 165.7 346.3 119.9 288 107.4l0 20.6c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-20.6C165.7 119.9 119.9 165.7 107.4 224l20.6 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-20.6 0zM256 224a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"/></svg>

*

出口person-walking-arrow-right：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M208 96a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM123.7 200.5c1-.4 1.9-.8 2.9-1.2l-16.9 63.5c-5.6 21.1-.1 43.6 14.7 59.7l70.7 77.1 22 88.1c4.3 17.1 21.7 27.6 38.8 23.3s27.6-21.7 23.3-38.8l-23-92.1c-1.9-7.8-5.8-14.9-11.2-20.8l-49.5-54 19.3-65.5 9.6 23c4.4 10.6 12.5 19.3 22.8 24.5l26.7 13.3c15.8 7.9 35 1.5 42.9-14.3s1.5-35-14.3-42.9L281 232.7l-15.3-36.8C248.5 154.8 208.3 128 163.7 128c-22.8 0-45.3 4.8-66.1 14l-8 3.5c-32.9 14.6-58.1 42.4-69.4 76.5l-2.6 7.8c-5.6 16.8 3.5 34.9 20.2 40.5s34.9-3.5 40.5-20.2l2.6-7.8c5.7-17.1 18.3-30.9 34.7-38.2l8-3.5zm-30 135.1L68.7 398 9.4 457.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L116.3 441c4.6-4.6 8.2-10.1 10.6-16.1l14.5-36.2-40.7-44.4c-2.5-2.7-4.8-5.6-7-8.6zM550.6 153.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L530.7 224 384 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l146.7 0-25.4 25.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l80-80c12.5-12.5 12.5-32.8 0-45.3l-80-80z"/></svg>

*

红外测温正常temperature-low：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M448 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM320 96a96 96 0 1 1 192 0A96 96 0 1 1 320 96zM144 64c-26.5 0-48 21.5-48 48l0 164.5c0 17.3-7.1 31.9-15.3 42.5C70.2 332.6 64 349.5 64 368c0 44.2 35.8 80 80 80s80-35.8 80-80c0-18.5-6.2-35.4-16.7-48.9c-8.2-10.6-15.3-25.2-15.3-42.5L192 112c0-26.5-21.5-48-48-48zM32 112C32 50.2 82.1 0 144 0s112 50.1 112 112l0 164.4c0 .1 .1 .3 .2 .6c.2 .6 .8 1.6 1.7 2.8c18.9 24.4 30.1 55 30.1 88.1c0 79.5-64.5 144-144 144S0 447.5 0 368c0-33.2 11.2-63.8 30.1-88.1c.9-1.2 1.5-2.2 1.7-2.8c.1-.3 .2-.5 .2-.6L32 112zM192 368c0 26.5-21.5 48-48 48s-48-21.5-48-48c0-20.9 13.4-38.7 32-45.3l0-50.7c0-8.8 7.2-16 16-16s16 7.2 16 16l0 50.7c18.6 6.6 32 24.4 32 45.3z"/></svg>

*

红外测温异常temperature-high：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M416 64a32 32 0 1 1 0 64 32 32 0 1 1 0-64zm0 128A96 96 0 1 0 416 0a96 96 0 1 0 0 192zM96 112c0-26.5 21.5-48 48-48s48 21.5 48 48l0 164.5c0 17.3 7.1 31.9 15.3 42.5C217.8 332.6 224 349.5 224 368c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-18.5 6.2-35.4 16.7-48.9C88.9 308.4 96 293.8 96 276.5L96 112zM144 0C82.1 0 32 50.2 32 112l0 164.4c0 .1-.1 .3-.2 .6c-.2 .6-.8 1.6-1.7 2.8C11.2 304.2 0 334.8 0 368c0 79.5 64.5 144 144 144s144-64.5 144-144c0-33.2-11.2-63.8-30.1-88.1c-.9-1.2-1.5-2.2-1.7-2.8c-.1-.3-.2-.5-.2-.6L256 112C256 50.2 205.9 0 144 0zm0 416c26.5 0 48-21.5 48-48c0-20.9-13.4-38.7-32-45.3L160 112c0-8.8-7.2-16-16-16s-16 7.2-16 16l0 210.7c-18.6 6.6-32 24.4-32 45.3c0 26.5 21.5 48 48 48z"/></svg>

*

障碍点xmarks-lines：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 32zm0 384c-17.7 0-32 14.3-32 32s14.3 32 32 32l576 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 416zM7 167c-9.4 9.4-9.4 24.6 0 33.9l55 55L7 311c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l55-55 55 55c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-55-55 55-55c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-55 55L41 167c-9.4-9.4-24.6-9.4-33.9 0zM265 167c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l55 55-55 55c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l55-55 55 55c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-55-55 55-55c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-55 55-55-55zM455 167c-9.4 9.4-9.4 24.6 0 33.9l55 55-55 55c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l55-55 55 55c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-55-55 55-55c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-55 55-55-55c-9.4-9.4-24.6-9.4-33.9 0z"/></svg>

*

漏液点water：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M269.5 69.9c11.1-7.9 25.9-7.9 37 0C329 85.4 356.5 96 384 96c26.9 0 55.4-10.8 77.4-26.1c0 0 0 0 0 0c11.9-8.5 28.1-7.8 39.2 1.7c14.4 11.9 32.5 21 50.6 25.2c17.2 4 27.9 21.2 23.9 38.4s-21.2 27.9-38.4 23.9c-24.5-5.7-44.9-16.5-58.2-25C449.5 149.7 417 160 384 160c-31.9 0-60.6-9.9-80.4-18.9c-5.8-2.7-11.1-5.3-15.6-7.7c-4.5 2.4-9.7 5.1-15.6 7.7c-19.8 9-48.5 18.9-80.4 18.9c-33 0-65.5-10.3-94.5-25.8c-13.4 8.4-33.7 19.3-58.2 25c-17.2 4-34.4-6.7-38.4-23.9s6.7-34.4 23.9-38.4C42.8 92.6 61 83.5 75.3 71.6c11.1-9.5 27.3-10.1 39.2-1.7c0 0 0 0 0 0C136.7 85.2 165.1 96 192 96c27.5 0 55-10.6 77.5-26.1zm37 288C329 373.4 356.5 384 384 384c26.9 0 55.4-10.8 77.4-26.1c0 0 0 0 0 0c11.9-8.5 28.1-7.8 39.2 1.7c14.4 11.9 32.5 21 50.6 25.2c17.2 4 27.9 21.2 23.9 38.4s-21.2 27.9-38.4 23.9c-24.5-5.7-44.9-16.5-58.2-25C449.5 437.7 417 448 384 448c-31.9 0-60.6-9.9-80.4-18.9c-5.8-2.7-11.1-5.3-15.6-7.7c-4.5 2.4-9.7 5.1-15.6 7.7c-19.8 9-48.5 18.9-80.4 18.9c-33 0-65.5-10.3-94.5-25.8c-13.4 8.4-33.7 19.3-58.2 25c-17.2 4-34.4-6.7-38.4-23.9s6.7-34.4 23.9-38.4c18.1-4.2 36.2-13.3 50.6-25.2c11.1-9.4 27.3-10.1 39.2-1.7c0 0 0 0 0 0C136.7 373.2 165.1 384 192 384c27.5 0 55-10.6 77.5-26.1c11.1-7.9 25.9-7.9 37 0zm0-144C329 229.4 356.5 240 384 240c26.9 0 55.4-10.8 77.4-26.1c0 0 0 0 0 0c11.9-8.5 28.1-7.8 39.2 1.7c14.4 11.9 32.5 21 50.6 25.2c17.2 4 27.9 21.2 23.9 38.4s-21.2 27.9-38.4 23.9c-24.5-5.7-44.9-16.5-58.2-25C449.5 293.7 417 304 384 304c-31.9 0-60.6-9.9-80.4-18.9c-5.8-2.7-11.1-5.3-15.6-7.7c-4.5 2.4-9.7 5.1-15.6 7.7c-19.8 9-48.5 18.9-80.4 18.9c-33 0-65.5-10.3-94.5-25.8c-13.4 8.4-33.7 19.3-58.2 25c-17.2 4-34.4-6.7-38.4-23.9s6.7-34.4 23.9-38.4c18.1-4.2 36.2-13.3 50.6-25.2c11.1-9.5 27.3-10.1 39.2-1.7c0 0 0 0 0 0C136.7 229.2 165.1 240 192 240c27.5 0 55-10.6 77.5-26.1c11.1-7.9 25.9-7.9 37 0z"/></svg>

*

异物ghost：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M40.1 467.1l-11.2 9c-3.2 2.5-7.1 3.9-11.1 3.9C8 480 0 472 0 462.2L0 192C0 86 86 0 192 0S384 86 384 192l0 270.2c0 9.8-8 17.8-17.8 17.8c-4 0-7.9-1.4-11.1-3.9l-11.2-9c-13.4-10.7-32.8-9-44.1 3.9L269.3 506c-3.3 3.8-8.2 6-13.3 6s-9.9-2.2-13.3-6l-26.6-30.5c-12.7-14.6-35.4-14.6-48.2 0L141.3 506c-3.3 3.8-8.2 6-13.3 6s-9.9-2.2-13.3-6L84.2 471c-11.3-12.9-30.7-14.6-44.1-3.9zM160 192a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm96 32a32 32 0 1 0 0-64 32 32 0 1 0 0 64z"/></svg>

*

充电点car-battery：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M80 96c0-17.7 14.3-32 32-32l64 0c17.7 0 32 14.3 32 32l96 0c0-17.7 14.3-32 32-32l64 0c17.7 0 32 14.3 32 32l16 0c35.3 0 64 28.7 64 64l0 224c0 35.3-28.7 64-64 64L64 448c-35.3 0-64-28.7-64-64L0 160c0-35.3 28.7-64 64-64l16 0zm304 96c0-8.8-7.2-16-16-16s-16 7.2-16 16l0 32-32 0c-8.8 0-16 7.2-16 16s7.2 16 16 16l32 0 0 32c0 8.8 7.2 16 16 16s16-7.2 16-16l0-32 32 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-32 0 0-32zM80 240c0 8.8 7.2 16 16 16l96 0c8.8 0 16-7.2 16-16s-7.2-16-16-16l-96 0c-8.8 0-16 7.2-16 16z"/></svg>

*

机器人定位图标location-crosshairs：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M256 0c17.7 0 32 14.3 32 32l0 34.7C368.4 80.1 431.9 143.6 445.3 224l34.7 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-34.7 0C431.9 368.4 368.4 431.9 288 445.3l0 34.7c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-34.7C143.6 431.9 80.1 368.4 66.7 288L32 288c-17.7 0-32-14.3-32-32s14.3-32 32-32l34.7 0C80.1 143.6 143.6 80.1 224 66.7L224 32c0-17.7 14.3-32 32-32zM128 256a128 128 0 1 0 256 0 128 128 0 1 0 -256 0zm128-80a80 80 0 1 1 0 160 80 80 0 1 1 0-160z"/></svg>

*

录像图标video：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M0 128C0 92.7 28.7 64 64 64l256 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64L64 448c-35.3 0-64-28.7-64-64L0 128zM559.1 99.8c10.4 5.6 16.9 16.4 16.9 28.2l0 256c0 11.8-6.5 22.6-16.9 28.2s-23 5-32.9-1.6l-96-64L416 337.1l0-17.1 0-128 0-17.1 14.2-9.5 96-64c9.8-6.5 22.4-7.2 32.9-1.6z"/></svg>

*

相机图标camera：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M149.1 64.8L138.7 96 64 96C28.7 96 0 124.7 0 160L0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-256c0-35.3-28.7-64-64-64l-74.7 0L362.9 64.8C356.4 45.2 338.1 32 317.4 32L194.6 32c-20.7 0-39 13.2-45.5 32.8zM256 192a96 96 0 1 1 0 192 96 96 0 1 1 0-192z"/></svg>

*

麦克风打开microphone：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M192 0C139 0 96 43 96 96l0 160c0 53 43 96 96 96s96-43 96-96l0-160c0-53-43-96-96-96zM64 216c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 40c0 89.1 66.2 162.7 152 174.4l0 33.6-48 0c-13.3 0-24 10.7-24 24s10.7 24 24 24l72 0 72 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-48 0 0-33.6c85.8-11.7 152-85.3 152-174.4l0-40c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 40c0 70.7-57.3 128-128 128s-128-57.3-128-128l0-40z"/></svg>

*

麦克风关闭microphone-slash：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L472.1 344.7c15.2-26 23.9-56.3 23.9-88.7l0-40c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 40c0 21.2-5.1 41.1-14.2 58.7L416 300.8 416 96c0-53-43-96-96-96s-96 43-96 96l0 54.3L38.8 5.1zM344 430.4c20.4-2.8 39.7-9.1 57.3-18.2l-43.1-33.9C346.1 382 333.3 384 320 384c-70.7 0-128-57.3-128-128l0-8.7L144.7 210c-.5 1.9-.7 3.9-.7 6l0 40c0 89.1 66.2 162.7 152 174.4l0 33.6-48 0c-13.3 0-24 10.7-24 24s10.7 24 24 24l72 0 72 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-48 0 0-33.6z"/></svg>

*

暂停circle-pause：<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM224 192l0 128c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-128c0-17.7 14.3-32 32-32s32 14.3 32 32zm128 0l0 128c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-128c0-17.7 14.3-32 32-32s32 14.3 32 32z"/></svg>


