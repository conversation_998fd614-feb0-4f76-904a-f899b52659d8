# 硬件工控接口报文

## 1. 任务链批量统一下发

```text
{
  "id": 1234,
  "cnt": 1500,
  "title": ["sp","gp","pn","biz","pat","pal","cf","cz","th","dc"]
  "data": [[1,9,8,101,10,10,1,1,800,1],[9,9,0,102,4,4,1,1,340,1]]
  "ptst": ["s","e","d"]
  "pts": [[[1,2,1],[2,3,1],[3,4,2],[4,5,2],[5,6,1],[6,7,1],[7,8,1],[8,9,1]],[]]
}
```

```text
{
  "id": 1234,
  "cnt": 1500,
  "data": [[1,9,8,101,10,10,1,1,800,1],[9,9,0,102,4,4,1,1,340,1]],
  "pts": [[[1,2,1],[2,3,1],[3,4,2],[4,5,2],[5,6,1],[6,7,1],[7,8,1],[8,9,1]],[]]
}
```

* id：任务实例id（32位整型）返回识别结果时需要
* cnt: 任务点位总数
* data: 任务链数组👇
  * sp: 起始点位id
  * gp: 目标点位id
  * pn: 指定路径的路径数量
  * bid: 识别点位id（16位整型）返回识别结果时需要
  * pat: 云台上下角度
  * pal: 云台左右角度
  * cf: 摄像头聚焦
  * cz: 摄像头变倍
  * th: 伸缩杆高度（毫米）
  * dc: 识别类型：1可见光识别,2红外识别
  * pts: 每条路径的起点、终点、方向👇
    * s: 路径起点id
    * e: 路径终点id
    * d: 方向：0不指定,1正走向终点,2倒走向终点

## 2. 识别结果推送

```json
{
  "id": 123412,
  "bid": 101,
  "pic": [
    "",
    ""
  ],
  "temperature": 36.5,
  "tm": "2025-06-15 12:34:56",
  "atm": "2025-06-15 12:34:00"
}
```

* id：任务实例id（32位整型）
* bid: 识别点位id（32位整型）
* pic: 识别结果图片数组
  * 第一个是可见光识别图片
  * 第二个是红外识别图片（仅限dc=2）
* temperature: 红外测温结果（仅限dc=2）
* tm: 识别时间
* atm: 到达点位的时间


## 3. 陪同

### 3.1. 等待接人

* 播放语音，同时进入等待UWB刷卡状态
* 刷到卡后有明确回调：
  * 参数：①UWB卡ID
* 服务端回馈指令：
  * 参数：①UWB卡是否匹配：0不匹配 | 1匹配

### 3.2. 到达目的地

* 播放语音，同时呼出【我已完成】按钮
* 点击【我已完成】按钮后有明确回调

### 3.3. 到达出口送人走

* 播放语音，同时呼出【确认离开】按钮
* 点击【确认离开】按钮后有明确回调
