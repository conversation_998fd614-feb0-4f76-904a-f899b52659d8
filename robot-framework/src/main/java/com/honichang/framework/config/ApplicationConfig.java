package com.honichang.framework.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.util.TimeZone;

/**
 * 程序注解配置
 *
 * <AUTHOR>
 */
@Configuration
// 表示通过aop框架暴露该代理对象,AopContext能够访问
@EnableAspectJAutoProxy(exposeProxy = true)
// 指定要扫描的Mapper类的包的路径
@MapperScan("com.honichang.**.mapper")
public class ApplicationConfig
{
    /**
     * 自定义序列化
     * @param builder builder
     */
    private static void customize(Jackson2ObjectMapperBuilder builder) {
        SimpleModule module = new SimpleModule();
        // long数据如果过大传输到js前端会丢失精度
        // 因此这里统一转为String类型
        module.addSerializer(Long.class, new LongJsonSerializer());
        module.addSerializer(Long.TYPE, new LongJsonSerializer());
        builder.modules(module);
        builder.timeZone(TimeZone.getDefault());
    }

    /**
     * 时区配置
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization()
    {
        return ApplicationConfig::customize;
//        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
    }
}
