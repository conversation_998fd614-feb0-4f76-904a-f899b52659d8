<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>robot</artifactId>
        <groupId>com.honichang</groupId>
        <version>3.8.9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>robot-cartography</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.honichang</groupId>
            <artifactId>robot-framework</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.honichang</groupId>
            <artifactId>robot-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>

    </dependencies>

</project>