package com.honichang.cartography.controller;

import com.honichang.cartography.controller.dto.MapMainGetByIdResponse;
import com.honichang.cartography.controller.dto.MapMainGetLatestResponse;
import com.honichang.cartography.controller.dto.MapMainUpdateRequest;
import com.honichang.point.model.vo.MapResponseBase;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.MapMain;
import com.honichang.point.service.MapMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 地图管理
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/map/main")
public class MapMainController {

    @Resource
    private MapMainService mapMainService;

    /**
     * 只能修改名称和比例尺
     * url[put]: /map/main/update
     */
    @Log(title = "地图信息更新", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@Validated @RequestBody MapMainUpdateRequest request) {
        mapMainService.updateNameAndScale(request.getMapId(), request.getMapName(), request.getScale());
        return AjaxResult.success();
    }

    /**
     * 获取最近修改的地图，用于前端默认展示
     * url[get]: /map/main/latest
     */
    @GetMapping("/latest")
    public AjaxResult getLatest() {
        MapMain mapMain = mapMainService.getLatest();
        if (mapMain != null) {
            MapMainGetLatestResponse response = MapResponseBase.convertFrom(MapMainGetLatestResponse.class, mapMain);
            __congestion(response);
            return AjaxResult.success(response);
        } else {
            return AjaxResult.error("未找到地图");
        }
    }

    /**
     * 根据ID获取地图信息
     * url[get]: /map/main/{id}
     */
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable("id") Long id) {
        MapMain mapMain = mapMainService.getById(id);
        if (mapMain != null) {
            MapMainGetByIdResponse response = MapResponseBase.convertFrom(MapMainGetByIdResponse.class, mapMain);
            __congestion(response);
            return AjaxResult.success(response);
        } else {
            return AjaxResult.error("未找到地图");
        }
    }

    private <T extends MapResponseBase> void __congestion(T response) {
        mapMainService.loadPointsAndPaths(response);
        mapMainService.loadObstacles(response);
        response.setMergeSvgUrl(response.getXmapWidth() == null ? null : mapMainService.generateMergeSvgPathWithRandom(response.getMapId()));
    }

}
