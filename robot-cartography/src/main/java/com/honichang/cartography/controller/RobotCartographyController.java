package com.honichang.cartography.controller;

import com.honichang.cartography.controller.dto.SaveOffsetRequest;
import com.honichang.cartography.service.RobotCartographyService;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.point.service.MapMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 机器人制图控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/cartography")
public class RobotCartographyController {

    @Resource
    private MapMainService mapMainService;

    @Resource
    private RobotCartographyService robotCartographyService;

    /**
     * 上传地图
     * url[post]: /cartography/import-layer
     * CAD图层只能是svg
     * XMAP图层只能是xmap
     */
    @PostMapping("/import-layer")
    public AjaxResult importLayer(@RequestParam("file") MultipartFile file, @RequestParam("mapId") Long mapId) {
        robotCartographyService.importLayer(file, mapId);
        return AjaxResult.success();
    }

    /**
     * 保存地图设计偏移量，不整合svg，仅保存偏移即可
     * url[post]: /cartography/save-offset
     */
    @PostMapping("/save-offset")
    public AjaxResult saveOffset(@RequestBody SaveOffsetRequest request) {
        log.info("保存地图偏移量，mapId={}, offsetX={}, offsetY={}", request.getMapId(), request.getOffsetX(), request.getOffsetY());
        robotCartographyService.saveOffset(request.getMapId(), request.getOffsetX(), request.getOffsetY());
        return AjaxResult.success(mapMainService.generateMergeSvgPathWithRandom(request.getMapId()));
    }

}
