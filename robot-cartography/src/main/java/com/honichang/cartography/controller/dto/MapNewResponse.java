package com.honichang.cartography.controller.dto;

import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.point.model.vo.MapResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class MapNewResponse extends MapResponseBase {

    /**
     * 全景地图的路径点（偏移后的）
     */
    private List<Position> advancedPoints;
    /**
     * 全景地图的路径（偏移后的）
     */
    private List<Path> advancedPaths;
    /**
     * 障碍点信息
     */
    private List<Obstacle> obstacles;
}
