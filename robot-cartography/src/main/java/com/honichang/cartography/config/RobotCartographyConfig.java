package com.honichang.cartography.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 科聪专属配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "robot.cartography")
public class RobotCartographyConfig {

    /**
     * 路径点size
     */
    private int advancedPointSize = 2;

    /**
     * 图片的页边距
     */
    private double padding = 0;

    /**
     * 扫描点的颜色
     */
    private String pointColor = "gray";

    /**
     * 路径的颜色
     */
    private String routeColor = "black";

    /**
     * 路径的宽度
     */
    private int routeWidth = 2;

    /**
     * 路径点的颜色
     */
    private String advancedPointColor = "red";

    /**
     * 是否显示边界线
     */
    private boolean showBoundary = true;

    /**
     * 是否显示坐标轴
     */
    private boolean showAxis = true;

}
