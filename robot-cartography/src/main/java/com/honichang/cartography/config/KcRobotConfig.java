package com.ruoyi.robot.vendor.kc;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 科聪专属配置
 */
@Component
@ConfigurationProperties(prefix = "robot.vendor.kc")
public class KcRobotConfig {

    /**
     * 路径点size
     */
    private int advancedPointSize = 2;

    /**
     * 图片的页边距
     */
    private double padding = 0;

    /**
     * 扫描点的颜色
     */
    private String pointColor = "gray";

    /**
     * 路径的颜色
     */
    private String routeColor = "black";

    /**
     * 路径的宽度
     */
    private int routeWidth = 2;

    /**
     * 路径点的颜色
     */
    private String advancedPointColor = "red";

    /**
     * 是否显示边界线
     */
    private boolean showBoundary = true;

    /**
     * 是否显示坐标轴
     */
    private boolean showAxis = true;


    // -- getter/setter --
    public boolean isShowAxis() {
        return showAxis;
    }

    public void setShowAxis(boolean showAxis) {
        this.showAxis = showAxis;
    }

    public int getRouteWidth() {
        return routeWidth;
    }

    public void setRouteWidth(int routeWidth) {
        this.routeWidth = routeWidth;
    }

    public String getAdvancedPointColor() {
        return advancedPointColor;
    }

    public void setAdvancedPointColor(String advancedPointColor) {
        this.advancedPointColor = advancedPointColor;
    }

    public String getRouteColor() {
        return routeColor;
    }

    public void setRouteColor(String routeColor) {
        this.routeColor = routeColor;
    }

    public String getPointColor() {
        return pointColor;
    }

    public void setPointColor(String pointColor) {
        this.pointColor = pointColor;
    }

    public boolean isShowBoundary() {
        return showBoundary;
    }

    public void setShowBoundary(boolean showBoundary) {
        this.showBoundary = showBoundary;
    }

    public double getPadding() {
        return padding;
    }

    public void setPadding(double padding) {
        this.padding = padding;
    }

    public int getAdvancedPointSize() {
        return advancedPointSize;
    }

    public void setAdvancedPointSize(int advancedPointSize) {
        this.advancedPointSize = advancedPointSize;
    }
}
