package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPoint;
import lombok.Data;

/**
 * 点位数据模型
 */
@Data
public class Point implements IPoint {
    private double x;
    private double y;
    private double z;
    private String t;
    
    public Point() {
    }
    
    public Point(double x, double y, double z, String t) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.t = t;
    }
}