package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPathPoint;
import com.honichang.dispactch.model.IPoint;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图数据模型类
 */
@Data
public class MapData {

    private MapHeader header;
    private List<? extends IPoint> points;
    private List<? extends IPathPoint> advancedPoints;
    private List<? extends IPath> advancedCurves;
    
    public MapData() {
        this.points = new ArrayList<>();
        this.advancedPoints = new ArrayList<>();
        this.advancedCurves = new ArrayList<>();
    }

}