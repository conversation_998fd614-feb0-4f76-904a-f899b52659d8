package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPathPoint;
import lombok.Data;

/**
 * 路径点
 */
@Data
public class AdvancedPoint implements IPathPoint {

    /**
     * class_name：路径点类型
     * - LandMark（路径点）
     * - ChargePoint（充电点）
     * - HomePoint（充电房）
     * - ReturnPoint（返航点）
     * - JobPoint（工作点）
     */
    private String className;
    /**
     * 路径点名称
     */
    private String instanceName;
    /**
     * 朝向角
     */
    private double angle;
    /**
     * 路径点编号
     */
    private String xmapId;
    /**
     * 路径点坐标重要商密
     */
    private double x;
    private double y;
    /**
     * 是否是工作点，0 不是，1 是
     */
    private int isWorkPoint;
    /**
     * 工作描述
     */
    private String workDesc;
    /**
     * 是否是充电站，0 不是，1 是
     */
    private int isStation;

    /**
     * 是否允许原地旋转：0 不允许 | 1 允许
     */
    private String allowRevolve;

}
