package com.honichang.cartography.model;

import lombok.Data;

/**
 * 地图头信息
 */
@Data
public class MapHeader {
    /**
     * 地图类型:
     * - 2D_Nature：2D 自然地图，无反激光地图
     * - 2D_Sign：2D 标记地图，有反激光地图
     * - 2D_Trajecyory：2D 轨道地图，用于磁条导航
     * - 2D_QRCode：2D 二维码地图
     * - 3D_Nature：3D自然地图
     */
    private String type;

    /**
     * 地图名称
     */
    private String name;

    /**
     * 地图分辨率(m)
     */
    private double resolution;

    /**
     * 标记点列数
     */
    private int colum;

    /**
     * 标记点行数
     */
    private int row;

    /**
     * 相邻行和列标记点的距离，单位 m
     */
    private double distance;

    /**
     * 地图版本号，每保存一次，版本号加1，从0开始
     */
    private double mapVersion;

    /**
     * 地图最小点坐标(m)
     * 例如：
     * X:[-31.071, 29.386](m)
     * Y:[-22.486, 34.226](m)
     */
    private Position minPos;

    /**
     * 地图最大的坐标
     */
    private Position maxPos;

    /**
     * 地图宽度(m)
     */
    private double width;

    /**
     * 地图高度(m)
     */
    private double height;

    /**
     * 当前缩放比
     */
    private double scale;

    /**
     * 当前缩放比下地图的宽度
     */
    private double imageWidth;

    /**
     * 当前缩放比下地图的高度
     */
    private double imageHeight;

}