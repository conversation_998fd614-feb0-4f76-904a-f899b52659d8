package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPath;
import lombok.Data;

/**
 * 路径
 */
@Data
public class AdvancedCurve implements IPath {

    private String xmapId;

    /**
     * 路径线类型
     */
    private String className;

    /**
     * 路径名称
     */
    private String instanceName;

    /**
     * 路径类型
     * bezier_curve - 贝塞尔曲线
     * straight_line - 直线
     * convex - 凸弧线
     * concave_arc - 凹弧线
     */
    private String curveType;

    /**
     * 弧度值(仅弧线类型需要),单位:rad
     */
    private double radian;

    /**
     * 起点坐标
     */
    private Position startPos;

    /**
     * 终点坐标
     */
    private Position endPos;
    /**
     * 路径控制点1坐标
     */
    private Position ctrlPos1;

    /**
     * 路径控制点2坐标
     */
    private Position ctrlPos2;

    /**
     * 禁行标志位
     * 0 表示可行
     * 1 表示不可行
     */
    private int isForbidden = 0;

    /**
     * 道路宽度(m)
     */
    private double roadWidth;

    /**
     * 角度补偿
     */
    private double angleCompensation;

    /**
     * 方向: forward 表示正走，backward 表示倒走
     */
    private String direction;

    @Override
    public String getRouteType() {
        return curveType;
    }

    /**
     * 在绘图期间暂时暂时用不上
     */
    public double getLength() {
        throw new UnsupportedOperationException("未实现");
    }
}
