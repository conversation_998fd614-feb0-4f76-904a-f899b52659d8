package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPoint;
import com.honichang.dispactch.model.IPosition;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import lombok.Data;

import java.util.Objects;

/**
 * 位置信息
 */
@Data
public class Position implements IPosition {

    private String xmapId;
    private double x;
    private double y;
    private double angle;
    private String className;

    public Position() {
    }

    public Position(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public Position(String xmapId, double x, double y, Double angle, String className) {
        this.xmapId = xmapId;
        this.x = x;
        this.y = y;
        this.angle = angle == null ? 0.0 : angle;
        this.className = className;
    }

    @Override
    public boolean equals(IPoint other) {
        if (other == null) return false;
        if (other.getClass() != this.getClass()) return false;
        return Objects.equals(xmapId, ((Position) other).xmapId);
    }

    @Override
    public boolean isCrossroads() {
        return PointClassNameEnum.CROSSROADS.getCode().equals(className);
    }

    @Override
    public Long getBizPointId() {
        throw new UnsupportedOperationException("未实现");
    }

    @Override
    public void setCrossroads(boolean b) {
        throw new UnsupportedOperationException("未实现");
    }
}