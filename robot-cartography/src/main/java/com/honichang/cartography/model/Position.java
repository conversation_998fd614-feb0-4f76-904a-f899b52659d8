package com.honichang.cartography.model;

import com.honichang.dispactch.model.IPosition;
import lombok.Data;

/**
 * 位置信息
 */
@Data
public class Position implements IPosition {

    private String xmapId;
    private double x;
    private double y;
    private Double angle;

    public Position() {
    }

    public Position(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public Position(String xmapId, double x, double y, Double angle) {
        this.xmapId = xmapId;
        this.x = x;
        this.y = y;
        this.angle = angle;
    }

}