package com.honichang.cartography.util;

import com.honichang.cartography.config.RobotCartographyConfig;
import com.honichang.cartography.model.MapData;
import com.honichang.cartography.model.MapHeader;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPathPoint;
import com.honichang.dispactch.model.IPoint;
import com.honichang.dispactch.model.IPosition;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * 矢量图渲染工具
 */
public class VectorMapRenderer {

    /**
     * 将地图数据渲染为矢量图并保存
     * 固定为svg格式
     *
     * @param mapData    地图数据
     * @param outputPath 输出路径
     * @param scale      缩放比
     * @throws Exception 保存SVG文件时发生错误
     */
    public static void renderToImage(MapData mapData, String outputPath, RobotCartographyConfig config, Double scale) throws Exception {
        MapHeader header = mapData.getHeader();
        List<? extends IPoint> points = mapData.getPoints();
        List<? extends IPath> advancedCurves = mapData.getAdvancedCurves();
        List<? extends IPathPoint> advancedPoints = mapData.getAdvancedPoints();

        // 计算尺寸(m)
        double minX = header.getMinPos().getX();
        double minY = header.getMinPos().getY();
        double maxX = header.getMaxPos().getX();
        double maxY = header.getMaxPos().getY();

        // 添加边距，使图像有一定的边界空间
        double padding = 0; // 不要边距 [padding = config.getPadding()]

        // 实际物理宽高
        double width = maxX - minX + 2 * padding;
        double height = maxY - minY + 2 * padding;
        header.setWidth(maxX - minX);
        header.setHeight(maxY - minY);

        // 获取分辨率(米/像素)
//        double resolution = header.getResolution();

        // 缩放因子，使图像适合显示
        if (scale <= 0) {
            // 目标尺寸为2000像素，更适合一般显示
            // 计算缩放比例，使较长的边等于目标尺寸
            scale = Math.min(2000 / width, 2000 / height);
        } else {
            // 根据地图实际缩放比计算scale(pt/m)
            scale = scale * (72000 / 25.4);
        }
        // 重要：这里依据默认2000的展示单位所设定的路径与点位的宽度
        // 但实际上的比例尺可能并非依据2000展示，所以需要换算，使路径与点位都匹配当前地图大小
        double ss = scale / Math.min(2000 / width, 2000 / height);
        // 与其他图层保持统一缩放比
        header.setScale(scale);

        // 计算最终图像尺寸(像素)
        double imageWidth = width * scale;
        double imageHeight = height * scale;
        header.setImageWidth(imageWidth);
        header.setImageHeight(imageHeight);

        // 创建SVG文件
        StringBuilder svg = new StringBuilder();
        svg.append("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n");
        svg.append("<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n");
        svg.append("<svg width=\"").append(imageWidth).append("pt\" height=\"").append(imageHeight)
                .append("pt\" viewBox=\"0 0 ").append(imageWidth).append(" ").append(imageHeight)
                .append("\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n");

        // 添加白色背景
        svg.append("<rect width=\"100%\" height=\"100%\" fill=\"none\"/>\n");

        // 绘制坐标轴
        if (config.isShowAxis()) {
            double centerX = imageWidth / 2;
            double centerY = imageHeight / 2;
            svg.append("<line x1=\"0\" y1=\"").append(centerY).append("\" x2=\"").append(imageWidth)
                    .append("\" y2=\"").append(centerY).append("\" stroke=\"lightgray\" stroke-width=\"1pt\"/>\n"); // X轴
            svg.append("<line x1=\"").append(centerX).append("\" y1=\"0\" x2=\"").append(centerX)
                    .append("\" y2=\"").append(imageHeight).append("\" stroke=\"lightgray\" stroke-width=\"1pt\"/>\n"); // Y轴
        }

        // 绘制点
        double size = 1;
        for (IPoint point : points) {
            // 转换坐标，考虑边距
            int x = (int) ((point.getX() - minX + padding) * scale);
            int y = (int) ((maxY - point.getY() + padding) * scale); // 翻转Y轴，使Y轴向上为正

            // 根据点类型设置颜色
            String color = config.getPointColor();
            // if ("c".equals(point.getT())) {
            //     color = config.getPointColorC(); // c类型点为红色
            // }

            // 根据Z值设置点的大小
            // double size = 2 + point.getZ() / 2;
            svg.append("<circle cx=\"").append(x).append("\" cy=\"").append(y)
                    .append("\" r=\"").append(size / 2).append("pt\" fill=\"").append(color).append("\"/>\n");
        }

        // 绘制高级曲线
        if (advancedCurves != null) {
            for (IPath curve : advancedCurves) {
                String curveType = curve.getRouteType();
                IPosition startPos = curve.getStartPos();
                IPosition endPos = curve.getEndPos();
                // double roadWidth = curve.getRoadWidth();

                // 转换坐标，考虑边距
                int startX = (int) ((startPos.getX() - minX + padding) * scale);
                int startY = (int) ((maxY - startPos.getY() + padding) * scale);
                int endX = (int) ((endPos.getX() - minX + padding) * scale);
                int endY = (int) ((maxY - endPos.getY() + padding) * scale);

                // 计算线条宽度，根据道路宽度和缩放比例
                // int strokeWidth = (int) (roadWidth * scale);
                // int strokeWidth = (int) (roadWidth * scale) / 3;
                double strokeWidth = config.getRouteWidth() * ss;

                if ("straight_line".equals(curveType)) {
                    // 直线
                    svg.append("<line x1=\"").append(startX).append("\" y1=\"").append(startY)
                            .append("\" x2=\"").append(endX).append("\" y2=\"").append(endY)
                            .append("\" stroke=\"").append(config.getRouteColor())
                            .append("\" stroke-width=\"").append(strokeWidth).append("pt\"/>\n");
                } else if ("bezier_curve".equals(curveType)) {
                    // 贝塞尔曲线
                    IPosition control1 = curve.getCtrlPos1();
                    IPosition control2 = curve.getCtrlPos2();

                    // 转换控制点坐标
                    int control1X = (int) ((control1.getX() - minX + padding) * scale);
                    int control1Y = (int) ((maxY - control1.getY() + padding) * scale);
                    int control2X = (int) ((control2.getX() - minX + padding) * scale);
                    int control2Y = (int) ((maxY - control2.getY() + padding) * scale);

                    svg.append("<path d=\"M").append(startX).append(",").append(startY)
                            .append(" C").append(control1X).append(",").append(control1Y)
                            .append(" ").append(control2X).append(",").append(control2Y)
                            .append(" ").append(endX).append(",").append(endY)
                            .append("\" fill=\"none\" stroke=\"").append(config.getRouteColor())
                            .append("\" stroke-width=\"").append(strokeWidth).append("pt\"/>\n");
                } else if ("convex".equals(curveType)) {
                    // 凸弧线 - 使用IRoute.getRadian()获取弧度值
                    // 计算两点之间的距离
                    double dx = endX - startX;
                    double dy = endY - startY;
                    double distance = Math.sqrt(dx * dx + dy * dy);

                    // 使用getRadian()获取弧度值计算半径
                    double radian = curve.getRadian();
                    double radius = 0;

                    // 根据弧度计算半径
                    if (Math.abs(radian) > 0.0001) { // 避免除以接近0的值
                        // 弦长公式: distance = 2 * radius * sin(radian/2)
                        // 因此 radius = distance / (2 * sin(radian/2))
                        radius = distance / (2 * Math.sin(radian / 2));
                    } else {
                        // 如果弧度接近0，则使用默认的半径计算方式
                        radius = distance / 2;
                    }

                    // 计算两点连线的中点
                    double midX = (double) (startX + endX) / 2;
                    double midY = (double) (startY + endY) / 2;

                    // 计算两点连线的垂直方向向量
                    double nx = -dy / distance;
                    double ny = dx / distance;

                    // 计算圆心（在两点连线中点的垂直方向上）
                    // 根据弧度调整圆心位置
                    double sagitta = radius - Math.sqrt(radius * radius - (distance / 2) * (distance / 2));
                    double cx = midX + nx * (radius - sagitta);
                    double cy = midY + ny * (radius - sagitta);

                    // 计算起点和终点相对于圆心的角度
                    double startAngle = Math.atan2(startY - cy, startX - cx);
                    double endAngle = Math.atan2(endY - cy, endX - cx);

                    // 计算角度差，确定是大弧还是小弧
                    double angleDiff = endAngle - startAngle;
                    if (angleDiff < 0) angleDiff += 2 * Math.PI;

                    // 确定大弧标志和顺逆时针标志
                    int largeArcFlag = (angleDiff > Math.PI) ? 1 : 0;
                    int sweepFlag = 1; // 顺时针方向

                    // 使用SVG的path和A命令绘制圆弧
                    svg.append("<path d=\"M").append(startX).append(",").append(startY)
                            .append(" A").append(radius).append(",").append(radius)
                            .append(" 0 ").append(largeArcFlag).append(" ").append(sweepFlag).append(" ")
                            .append(endX).append(",").append(endY)
                            .append("\" fill=\"none\" stroke=\"").append(config.getRouteColor())
                            .append("\" stroke-width=\"").append(strokeWidth).append("pt\"/>\n");
                } else if ("concave_arc".equals(curveType)) {
                    // 凹弧线 - 使用IRoute.getRadian()获取弧度值
                    // 计算两点之间的距离
                    double dx = endX - startX;
                    double dy = endY - startY;
                    double distance = Math.sqrt(dx * dx + dy * dy);

                    // 使用getRadian()获取弧度值计算半径
                    double radian = curve.getRadian();
                    double radius = 0;

                    // 根据弧度计算半径
                    if (Math.abs(radian) > 0.0001) { // 避免除以接近0的值
                        // 弦长公式: distance = 2 * radius * sin(radian/2)
                        // 因此 radius = distance / (2 * sin(radian/2))
                        radius = distance / (2 * Math.sin(radian / 2));
                    } else {
                        // 如果弧度接近0，则使用默认的半径计算方式
                        radius = distance / 2;
                    }

                    // 计算两点连线的中点
                    double midX = (double) (startX + endX) / 2;
                    double midY = (double) (startY + endY) / 2;

                    // 计算两点连线的垂直方向向量（注意：与凸弧线相反）
                    double nx = dy / distance;  // 符号与凸弧线相反
                    double ny = -dx / distance; // 符号与凸弧线相反

                    // 计算圆心（在两点连线中点的垂直方向上）
                    // 根据弧度调整圆心位置
                    double sagitta = radius - Math.sqrt(radius * radius - (distance / 2) * (distance / 2));
                    double cx = midX + nx * (radius - sagitta);
                    double cy = midY + ny * (radius - sagitta);

                    // 计算起点和终点相对于圆心的角度
                    double startAngle = Math.atan2(startY - cy, startX - cx);
                    double endAngle = Math.atan2(endY - cy, endX - cx);

                    // 计算角度差，确定是大弧还是小弧
                    double angleDiff = endAngle - startAngle;
                    if (angleDiff < 0) angleDiff += 2 * Math.PI;

                    // 确定大弧标志和顺逆时针标志
                    int largeArcFlag = (angleDiff > Math.PI) ? 1 : 0;
                    int sweepFlag = 1; // 顺时针方向

                    // 使用SVG的path和A命令绘制圆弧
                    svg.append("<path d=\"M").append(startX).append(",").append(startY)
                            .append(" A").append(radius).append(",").append(radius)
                            .append(" 0 ").append(largeArcFlag).append(" ").append(sweepFlag).append(" ")
                            .append(endX).append(",").append(endY)
                            .append("\" fill=\"none\" stroke=\"").append(config.getRouteColor())
                            .append("\" stroke-width=\"").append(strokeWidth).append("pt\"/>\n");
                }
            }
        }

        // 绘制路径点
        double pointSize = config.getAdvancedPointSize() * ss;
        if (advancedPoints != null) {
            for (IPathPoint point : advancedPoints) {
                // 转换坐标，考虑边距
                int x = (int) ((point.getX() - minX + padding) * scale);
                int y = (int) ((maxY - point.getY() + padding) * scale);

                // 根据点类型设置颜色
                String color = config.getAdvancedPointColor(); // 默认LandMark为蓝色点
                // if ("ChargePoint".equals(point.getClassName())) {
                //     color = config.getChargePointColor(); // 充电点为红色
                // } else if ("JobPoint".equals(point.getClassName())) {
                //     color = config.getJobPointColor(); // 工作点为绿色
                // }

                // 根据Z值设置点的大小
                svg.append("<circle cx=\"").append(x).append("\" cy=\"").append(y)
                        .append("\" r=\"").append(pointSize).append("pt\" fill=\"").append(color).append("\"/>\n");
            }
        }

        // 绘制地图边界，要求是淡灰色，不填充只画边框
        if (config.isShowBoundary()) {
            // 使用路径而不是矩形，以确保所有边都能正确显示
            double rectX = padding * scale;
            double rectY = padding * scale;
            double rectWidth = header.getWidth() * scale;
            double rectHeight = header.getHeight() * scale;

            svg.append("<path d=\"M").append(rectX).append(",").append(rectY)
                    .append(" L").append(rectX + rectWidth).append(",").append(rectY)
                    .append(" L").append(rectX + rectWidth).append(",").append(rectY + rectHeight)
                    .append(" L").append(rectX).append(",").append(rectY + rectHeight)
                    .append(" Z\" fill=\"none\" stroke=\"#808080\" stroke-width=\"2pt\"/>\n");
        }

        // 绘制地图信息
        // if (config.isShowMapInfo()) {
        //     svg.append("<text x=\"10\" y=\"20\" font-family=\"Arial\" font-size=\"12\" fill=\"black\">地图名称: ")
        //     .append(header.getName()).append("</text>\n");
        //     svg.append("<text x=\"10\" y=\"40\" font-family=\"Arial\" font-size=\"12\" fill=\"black\">地图类型: ")
        //     .append(header.getType()).append("</text>\n");
        //     svg.append("<text x=\"10\" y=\"60\" font-family=\"Arial\" font-size=\"12\" fill=\"black\">地图版本: ")
        //     .append(header.getMapVersion()).append("</text>\n");
        //     svg.append("<text x=\"10\" y=\"80\" font-family=\"Arial\" font-size=\"12\" fill=\"black\">点数量: ")
        //     .append(points.size()).append("</text>\n");
        // }

        // 结束SVG
        svg.append("</svg>");

        // 保存SVG文件
        try {
            File outputFile = new File(outputPath);
            try (FileWriter writer = new FileWriter(outputFile)) {
                writer.write(svg.toString());
            }
            System.out.println("SVG矢量图已保存到: " + outputPath);
        } catch (IOException e) {
            throw new Exception("保存SVG文件时发生错误: " + e.getMessage());
        }
    }
}
