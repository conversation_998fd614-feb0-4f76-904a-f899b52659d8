package com.honichang.cartography.util;

import com.honichang.common.utils.StringUtils;
import com.honichang.point.domain.MapMain;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.ParseException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SVG格式CAD图片解析工具类
 * 用于解析SVG文件并提取信息填充MapMain对象
 *
 * <AUTHOR>
 */
@Slf4j
public class SvgMapParser {

    /**
     * 解析SVG文件并填充MapMain对象，同时设置URL
     *
     * @param svgFilePath SVG文件路径
     * @param url 文件URL
     * @param mapMain 要填充的MapMain对象
     * @throws IllegalArgumentException 当传入的参数无效时
     * @throws ParseException 当解析SVG文件失败时
     * @throws IOException 当读取文件失败时
     */
    public static void parseSvgFile(String svgFilePath, String url, MapMain mapMain) throws ParseException, IOException {
        if (svgFilePath == null || svgFilePath.isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        if (mapMain == null) {
            throw new IllegalArgumentException("传入的MapMain对象为空");
        }

        parseSvgFile(svgFilePath, mapMain);
        if (url != null && !url.isEmpty()) {
            mapMain.setCadUrl(url);
        }
    }

    /**
     * 解析SVG文件并填充MapMain对象
     *
     * @param svgFilePath SVG文件路径
     * @param mapMain 要填充的MapMain对象
     * @throws IllegalArgumentException 当传入的参数无效时
     * @throws ParseException 当解析SVG文件失败时
     * @throws IOException 当读取文件失败时
     */
    public static void parseSvgFile(String svgFilePath, MapMain mapMain) throws ParseException, IOException {
        try {
            if (svgFilePath == null || svgFilePath.isEmpty()) {
                throw new IllegalArgumentException("文件路径不能为空");
            }
            if (mapMain == null) {
                throw new IllegalArgumentException("传入的MapMain对象为空");
            }

            // 创建DOM解析器
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new File(svgFilePath));

            // 获取根元素（svg元素）
            Element svgElement = document.getDocumentElement();

            // 如果地图名称为空，从文件名获取
            if (mapMain.getMapName() == null || mapMain.getMapName().isEmpty()) {
                String fileName = Paths.get(svgFilePath).getFileName().toString();
                String mapName = fileName.substring(0, fileName.lastIndexOf('.'));
                mapMain.setMapName(mapName);
            }

            // 设置CAD图层宽度和高度
            String widthStr = svgElement.getAttribute("width");
            String heightStr = svgElement.getAttribute("height");

            // 提取宽度和高度的数值部分（去掉单位）
            Double width = extractNumericValue(widthStr);
            Double height = extractNumericValue(heightStr);

            if (width != null && height != null) {
                mapMain.setCadWidth(width);
                mapMain.setCadHeight(height);
                log.info("解析SVG文件成功，宽度: {}pt, 高度: {}pt", width, height);
            } else {
                // 尝试分析viewBox来给出宽高
                String viewBox = svgElement.getAttribute("viewBox");
                if (StringUtils.isNotEmpty(viewBox)) {
                    String[] parts = viewBox.split("\\s+");
                    if (parts.length == 4) {
                        // viewBox格式为：min-x min-y width height
                        try {
                            width = Double.parseDouble(parts[2]);
                            height = Double.parseDouble(parts[3]);
                            mapMain.setCadWidth(width);
                            mapMain.setCadHeight(height);
                            log.info("通过viewBox解析SVG文件成功，宽度: {}pt, 高度: {}pt", width, height);
                        } catch (NumberFormatException e) {
                            throw new ParseException("无法解析viewBox值: " + viewBox, 0);
                        }
                    } else {
                        throw new ParseException("viewBox格式不正确，期望4个值，实际获得" + parts.length + "个值: " + viewBox, 0);
                    }
                } else {
                    throw new ParseException("SVG文件缺少width、height和viewBox属性，无法确定图像尺寸", 0);
                }
            }

            // 设置默认比例尺（可以根据实际情况调整）
            // 比例尺表示1个单位在图上对应实际多少米
            // 例如1:280表示图上1cm对应实际280cm
            if (mapMain.getScale() == null) {
                // 代码实际走不到这里，因为创建地图的时候scale是必选项
                mapMain.setScale(0.01); // 默认值，实际应根据具体图纸确定
            }
        } catch (ParserConfigurationException | SAXException e) {
            throw new ParseException("解析SVG文件失败: " + e.getMessage(), 0);
        }
    }

    /**
     * 从带单位的尺寸字符串中提取数值部分
     * 例如从"841.89001pt"中提取841.89001
     *
     * @param valueWithUnit 带单位的尺寸字符串
     * @return 提取的数值，解析失败返回null
     */
    private static Double extractNumericValue(String valueWithUnit) throws NumberFormatException {
        if (valueWithUnit == null || valueWithUnit.isEmpty()) {
            return null;
        }

        // 使用正则表达式提取数值部分
        Pattern pattern = Pattern.compile("([\\d.]+)\\s*[a-zA-Z]*");
        Matcher matcher = pattern.matcher(valueWithUnit);

        if (matcher.find()) {
            try {
                return Double.parseDouble(matcher.group(1));
            } catch (NumberFormatException e) {
                throw new NumberFormatException("无法解析数值: " + matcher.group(1));
            }
        }

        return null;
    }

}
