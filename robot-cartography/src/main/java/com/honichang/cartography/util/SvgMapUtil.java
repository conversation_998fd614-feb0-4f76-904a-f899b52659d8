package com.honichang.cartography.util;

import org.slf4j.LoggerFactory;
import org.w3c.dom.*;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SVG地图图像工具类
 *
 * <AUTHOR>
 */
public class SvgMapUtil {

    private static final org.slf4j.Logger log = LoggerFactory.getLogger(SvgMapUtil.class);

    // 文档缓存，使用ConcurrentHashMap保证线程安全
    private static final Map<String, Document> documentCache = new ConcurrentHashMap<>();

    public static void preloadSvg(String svgPath, String key) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document svgDoc = builder.parse(new File(svgPath));
        documentCache.put(key, svgDoc);
    }

    /**
     * 将两个SVG文件整合到一起，生成一个新的SVG文件
     * 请保证两个地图svg文件的scale是一致的，宽高的单位都是一致的
     * 新的文件将会取两者宽高的最大值
     *
     * @param svg1Path   第一个SVG文件路径
     * @param svg2Path   第二个SVG文件路径
     * @param outputPath 输出文件路径
     * @param offsetX    第二个SVG文件的偏移量（x方向）
     * @param offsetY    第二个SVG文件的偏移量（y方向）
     */
    public static void mergeSvgFiles(String svg1Path, String svg2Path, String outputPath, double offsetX, double offsetY, String key1, String key2) throws Exception {
        log.info("开始合并SVG文件...");

        try {
            Document svg1Doc = documentCache.get(key1);
            Document svg2Doc = documentCache.get(key2);

            // 创建DOM解析器
            long parserStartTime = System.currentTimeMillis();
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true); // 启用命名空间支持
            DocumentBuilder builder = factory.newDocumentBuilder();
            log.info("初始化解析器耗时: {}ms", System.currentTimeMillis() - parserStartTime);

            if (svg1Doc == null) {
                // 解析SVG文件
                log.info("开始解析第一个SVG文件: {}", svg1Path);
                svg1Doc = builder.parse(new File(svg1Path));
                documentCache.put(key1, svg1Doc);
            }
            Element svg1Root = svg1Doc.getDocumentElement();

            if (svg2Doc == null) {
                svg2Doc = builder.parse(new File(svg2Path));
                documentCache.put(key2, svg2Doc);
            }
            Element svg2Root = svg2Doc.getDocumentElement();

            // 获取SVG尺寸
            double width1 = getSvgWidth(svg1Root);
            double height1 = getSvgHeight(svg1Root);
            double width2 = getSvgWidth(svg2Root);
            double height2 = getSvgHeight(svg2Root);

            // 计算合并后的SVG尺寸，考虑偏移量
            double mergedWidth = Math.max(width1, width2 + offsetX);
            double mergedHeight = Math.max(height1, height2 + offsetY);

            // 检查是否包含Inkscape和Sodipodi相关内容
            Element svgWithInkscape = null;
            if (hasInkscapeContent(svg1Root)) {
                svgWithInkscape = svg1Root;
            } else if (hasInkscapeContent(svg2Root)) {
                svgWithInkscape = svg2Root;
            }

            // 创建合并后的SVG文档
            Document mergedDoc = builder.newDocument();
            Element mergedRoot;

            // 如果有Inkscape内容，复制该SVG的根元素作为模板
            if (svgWithInkscape != null) {
                mergedRoot = (Element) mergedDoc.importNode(svgWithInkscape, false);
                mergedDoc.appendChild(mergedRoot);

                // 复制所有命名空间声明
                copyNamespaceDeclarations(svgWithInkscape, mergedRoot);

                // 更新尺寸属性
                mergedRoot.setAttribute("width", mergedWidth + "pt");
                mergedRoot.setAttribute("height", mergedHeight + "pt");
                mergedRoot.setAttribute("viewBox", "0 0 " + mergedWidth + " " + mergedHeight);

                // 复制defs和sodipodi:namedview元素
                copySpecialElements(mergedDoc, svgWithInkscape, mergedRoot);
            } else {
                // 如果没有Inkscape内容，创建标准SVG
                mergedRoot = mergedDoc.createElementNS("http://www.w3.org/2000/svg", "svg");
                mergedDoc.appendChild(mergedRoot);

                // 设置SVG属性
                mergedRoot.setAttribute("width", String.valueOf(mergedWidth));
                mergedRoot.setAttribute("height", String.valueOf(mergedHeight));
                mergedRoot.setAttribute("viewBox", "0 0 " + mergedWidth + " " + mergedHeight);
                mergedRoot.setAttribute("xmlns", "http://www.w3.org/2000/svg");
            }

            // 创建组元素并复制内容
            Element g1 = createGroupWithContent(mergedDoc, svg1Root, "svg1-content");
            Element g2 = createGroupWithContentWithOffset(mergedDoc, svg2Root, "svg2-content", offsetX, offsetY);

            // 将组添加到合并后的SVG
            mergedRoot.appendChild(g1);
            mergedRoot.appendChild(g2);

            // 保存合并后的SVG
            saveSvgToFile(mergedDoc, outputPath);
            log.info("SVG文件合并成功，保存至: {}", outputPath);

        } catch (Exception e) {
            log.error("合并SVG文件时发生错误: {}", e.getMessage(), e);
            throw new Exception("合并SVG文件时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 检查SVG是否包含Inkscape和Sodipodi相关内容
     *
     * @param svgRoot SVG根元素
     * @return 是否包含Inkscape内容
     */
    private static boolean hasInkscapeContent(Element svgRoot) {
        // 检查命名空间声明
        NamedNodeMap attributes = svgRoot.getAttributes();
        for (int i = 0; i < attributes.getLength(); i++) {
            Node attr = attributes.item(i);
            String name = attr.getNodeName();
            String value = attr.getNodeValue();

            if (name.startsWith("xmlns:inkscape") || name.startsWith("xmlns:sodipodi") ||
                    value.contains("inkscape") || value.contains("sodipodi")) {
                return true;
            }
        }

        // 检查是否有sodipodi:namedview元素
        NodeList children = svgRoot.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = child.getNodeName();
                if (nodeName.contains("sodipodi:namedview") || nodeName.contains("inkscape:")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 复制命名空间声明
     *
     * @param source 源元素
     * @param target 目标元素
     */
    private static void copyNamespaceDeclarations(Element source, Element target) {
        NamedNodeMap attributes = source.getAttributes();
        for (int i = 0; i < attributes.getLength(); i++) {
            Node attr = attributes.item(i);
            String name = attr.getNodeName();
            String value = attr.getNodeValue();

            // 复制所有命名空间声明
            if (name.startsWith("xmlns:") || name.equals("xmlns")) {
                target.setAttribute(name, value);
            }
        }
    }

    /**
     * 复制特殊元素（defs和sodipodi:namedview）
     *
     * @param targetDoc  目标文档
     * @param sourceRoot 源SVG根元素
     * @param targetRoot 目标SVG根元素
     */
    private static void copySpecialElements(Document targetDoc, Element sourceRoot, Element targetRoot) {
        NodeList children = sourceRoot.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = child.getNodeName();
                // 复制defs和sodipodi:namedview元素
                if (nodeName.equals("defs") || nodeName.contains("sodipodi:namedview") ||
                        nodeName.contains("inkscape:grid")) {
                    Node importedNode = targetDoc.importNode(child, true);
                    targetRoot.appendChild(importedNode);
                }
            }
        }
    }

    /**
     * 获取SVG的宽度
     *
     * @param svgRoot SVG根元素
     * @return 宽度值
     */
    private static double getSvgWidth(Element svgRoot) {
        return getSvgDimension(svgRoot, "width", 2);
    }

    /**
     * 获取SVG的高度
     *
     * @param svgRoot SVG根元素
     * @return 高度值
     */
    private static double getSvgHeight(Element svgRoot) {
        return getSvgDimension(svgRoot, "height", 3);
    }

    /**
     * 获取SVG的尺寸（宽度或高度）
     *
     * @param svgRoot       SVG根元素
     * @param attributeName 属性名称（"width"或"height"）
     * @param viewBoxIndex  viewBox中的索引位置
     * @return 尺寸值
     */
    private static double getSvgDimension(Element svgRoot, String attributeName, int viewBoxIndex) {
        String dimensionStr = svgRoot.getAttribute(attributeName);

        if (dimensionStr.isEmpty()) {
            // 如果属性不存在，尝试从viewBox获取
            String viewBox = svgRoot.getAttribute("viewBox");
            if (!viewBox.isEmpty()) {
                String[] parts = viewBox.split("\\s+");
                if (parts.length >= 4) {
                    return Double.parseDouble(parts[viewBoxIndex]);
                }
            }
            return 0;
        } else {
            // 移除单位（如px, pt等）
            return Double.parseDouble(dimensionStr.replaceAll("[^\\d.]", ""));
        }
    }

    /**
     * 创建组元素并复制源SVG的内容
     *
     * @param targetDoc  目标文档
     * @param sourceRoot 源SVG根元素
     * @param groupId    组ID
     * @return 包含源内容的组元素
     */
    private static Element createGroupWithContent(Document targetDoc, Element sourceRoot, String groupId) {
        Element group = targetDoc.createElementNS("http://www.w3.org/2000/svg", "g");
        group.setAttribute("id", groupId);

        // 复制源SVG的所有子节点，但排除defs和sodipodi:namedview元素
        NodeList children = sourceRoot.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = child.getNodeName();
                // 排除特殊元素
                if (!nodeName.equals("defs") && !nodeName.contains("sodipodi:namedview") &&
                        !nodeName.contains("inkscape:grid")) {
                    Node importedNode = targetDoc.importNode(child, true);
                    group.appendChild(importedNode);
                }
            }
        }

        return group;
    }

    /**
     * 创建组元素并复制源SVG的内容，同时应用偏移量
     *
     * @param targetDoc  目标文档
     * @param sourceRoot 源SVG根元素
     * @param groupId    组ID
     * @param offsetX    X方向偏移量
     * @param offsetY    Y方向偏移量
     * @return 包含源内容的组元素
     */
    private static Element createGroupWithContentWithOffset(Document targetDoc, Element sourceRoot, String groupId,
                                                            double offsetX, double offsetY) {
        Element group = targetDoc.createElementNS("http://www.w3.org/2000/svg", "g");
        group.setAttribute("id", groupId);

        // 应用偏移量到组元素
        group.setAttribute("transform", "translate(" + offsetX + "," + offsetY + ")");

        // 复制源SVG的所有子节点，但排除defs和sodipodi:namedview元素
        NodeList children = sourceRoot.getChildNodes();
        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = child.getNodeName();
                // 排除特殊元素
                if (!nodeName.equals("defs") && !nodeName.contains("sodipodi:namedview") &&
                        !nodeName.contains("inkscape:grid")) {
                    Node importedNode = targetDoc.importNode(child, true);
                    group.appendChild(importedNode);
                }
            }
        }

        return group;
    }

    /**
     * 将SVG文档保存到文件
     *
     * @param doc        SVG文档
     * @param outputPath 输出文件路径
     * @throws Exception 保存过程中的异常
     */
    private static void saveSvgToFile(Document doc, String outputPath) throws Exception {
        // 判断outputPath文件夹是否存在，不存在则创建文件夹
        File file = new File(outputPath);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
        DOMSource source = new DOMSource(doc);
        StreamResult result = new StreamResult(new File(outputPath));
        transformer.transform(source, result);
    }

}
