package com.honichang.cartography.util;

import com.honichang.cartography.model.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * .xmap文件解析工具类
 */
public class XmapParser {

    /**
     * 解析.xmap文件
     *
     * @param filePath 文件路径
     * @return 地图数据
     * @throws Exception 解析失败
     */
    public static MapData parseXmap(String filePath) throws Exception {
        MapData mapData = new MapData();
        try {
            SAXReader reader = new SAXReader();
            // 尝试使用不同的编码方式读取文件
            File file = new File(filePath);
            Document document = null;

            try {
                // 首先尝试使用 GBK 编码
                InputStreamReader isr = new InputStreamReader(new FileInputStream(file), Charset.forName("GBK"));
                document = reader.read(isr);
            } catch (Exception e) {
                try {
                    // 如果 GBK 失败，尝试 UTF-8
                    InputStreamReader isr = new InputStreamReader(new FileInputStream(file), "UTF-8");
                    document = reader.read(isr);
                } catch (Exception e2) {
                    // 最后尝试系统默认编码
                    document = reader.read(file);
                }
            }

            if (document == null) {
                throw new Exception("无法解析地图文件，请检查文件编码");
            }

            Element rootElement = document.getRootElement();

            // 解析header
            Element headerElement = rootElement.element("header");
            MapHeader header = parseHeader(headerElement);
            mapData.setHeader(header);

            // 解析point
            List<Element> pointElements = rootElement.elements("point");
            List<Point> points = new ArrayList<>();
            for (Element pointElement : pointElements) {
                Point point = parsePoint(pointElement);
                points.add(point);
            }
            mapData.setPoints(points);

            // 解析路径点
            List<Element> advancedPoints = rootElement.elements("advanced_point");
            List<AdvancedPoint> advancedPointList = new ArrayList<>();
            for (Element advancedPoint : advancedPoints) {
                AdvancedPoint ap = parseAdvancedPoint(advancedPoint);
                advancedPointList.add(ap);
            }
            mapData.setAdvancedPoints(advancedPointList);

            // 解析路径
            List<Element> pathElements = rootElement.elements("advanced_curve");
            List<AdvancedCurve> advancedCurveList = new ArrayList<>();
            for (Element pathElement : pathElements) {
                AdvancedCurve ac = parseAdvancedCurve(pathElement, advancedPointList);
                advancedCurveList.add(ac);
            }

            mapData.setAdvancedCurves(advancedCurveList);

        } catch (DocumentException e) {
            throw new Exception("地图文件解析失败", e);
        } catch (Exception e) {
            throw new Exception("地图文件解析过程中发生错误", e);
        }
        return mapData;
    }

    private static AdvancedPoint parseAdvancedPoint(Element advancedPoint) {
        AdvancedPoint ap = new AdvancedPoint();
        ap.setClassName(advancedPoint.attributeValue("class_name"));
        ap.setInstanceName(advancedPoint.attributeValue("instance_name"));
        ap.setAngle(Double.parseDouble(advancedPoint.attributeValue("angle")));
        ap.setXmapId(advancedPoint.attributeValue("id"));
        ap.setAllowRevolve(advancedPoint.attributeValue("allow_revolve"));

        Element element = advancedPoint.element("pos");
        ap.setX(Double.parseDouble(element.attributeValue("x")));
        ap.setY(Double.parseDouble(element.attributeValue("y")));

        ap.setIsWorkPoint(Integer.parseInt(advancedPoint.attributeValue("is_work_point")));

        // 查找 is_station 属性
        List<Element> properties = advancedPoint.elements("property");
        for (Element property : properties) {
            if ("is_station".equals(property.attributeValue("name"))) {
                ap.setIsStation(Integer.parseInt(property.attributeValue("enable")));
                break;
            }
        }

        ap.setWorkDesc(advancedPoint.attributeValue("work_desc"));
        return ap;
    }

    /**
     * 解析header元素
     */
    private static MapHeader parseHeader(Element headerElement) {
        MapHeader header = new MapHeader();
        header.setType(headerElement.attributeValue("type"));
        header.setName(headerElement.attributeValue("name"));
        header.setColum(Integer.parseInt(headerElement.attributeValue("colum")));
        header.setRow(Integer.parseInt(headerElement.attributeValue("row")));
        header.setResolution(Double.parseDouble(headerElement.attributeValue("resolution")));
        header.setDistance(Double.parseDouble(headerElement.attributeValue("distance")));
        header.setMapVersion(Double.parseDouble(headerElement.attributeValue("map_version")));

        // 解析min_pos和max_pos
        Element minPosElement = headerElement.element("min_pos");
        Position minPos = new Position();
        minPos.setX(Double.parseDouble(minPosElement.attributeValue("x")));
        minPos.setY(Double.parseDouble(minPosElement.attributeValue("y")));
        header.setMinPos(minPos);

        Element maxPosElement = headerElement.element("max_pos");
        Position maxPos = new Position();
        maxPos.setX(Double.parseDouble(maxPosElement.attributeValue("x")));
        maxPos.setY(Double.parseDouble(maxPosElement.attributeValue("y")));
        header.setMaxPos(maxPos);

        return header;
    }

    /**
     * 解析point元素
     */
    private static Point parsePoint(Element pointElement) {
        Point point = new Point();
        point.setX(Double.parseDouble(pointElement.attributeValue("x")));
        point.setY(Double.parseDouble(pointElement.attributeValue("y")));
        point.setZ(Double.parseDouble(pointElement.attributeValue("z")));
        point.setT(pointElement.attributeValue("t"));
        return point;
    }

    /**
     * 解析advanced_curve元素
     *
     * @param pathElement       路径元素
     * @param advancedPointList 高级点位列表
     * @return 高级曲线对象
     * @throws Exception 当找不到引用的点位时抛出异常
     */
    private static AdvancedCurve parseAdvancedCurve(Element pathElement, List<AdvancedPoint> advancedPointList) throws Exception {
        AdvancedCurve ac = new AdvancedCurve();
        ac.setClassName(pathElement.attributeValue("class_name"));
        ac.setInstanceName(pathElement.attributeValue("instance_name"));
        ac.setIsForbidden(NumberUtils.toInt(pathElement.attributeValue("is_forbidden")));
        ac.setCurveType(pathElement.attributeValue("curve_type"));
        ac.setRadian(Double.parseDouble(pathElement.attributeValue("radian")));
        ac.setRoadWidth(Double.parseDouble(pathElement.attributeValue("RoadWidth")));
        ac.setDirection(pathElement.attributeValue("Direction"));
        ac.setXmapId(pathElement.attributeValue("id"));
        // start_pos
        {
            Element posElement = pathElement.element("start_pos");
            String posId = posElement.attributeValue("id");
            AdvancedPoint startPos = advancedPointList.stream().filter(ap -> ap.getXmapId().equals(posId)).findFirst().orElse(null);
            if (startPos == null) {
                throw new Exception(String.format("start_pos id=%s not found", posId));
            }
            ac.setStartPos(new Position(startPos.getXmapId(), startPos.getX(), startPos.getY(), startPos.getAngle()));
        }
        // end_pos
        {
            Element posElement = pathElement.element("end_pos");
            String posId = posElement.attributeValue("id");
            AdvancedPoint endPos = advancedPointList.stream().filter(ap -> ap.getXmapId().equals(posId)).findFirst().orElse(null);
            if (endPos == null) {
                throw new Exception(String.format("end_pos id=%s not found", posId));
            }
            ac.setEndPos(new Position(endPos.getXmapId(), endPos.getX(), endPos.getY(), endPos.getAngle()));
        }
        // control1_pos
        Element posElement = pathElement.element("control1_pos");
        Position p = new Position(Double.parseDouble(posElement.attributeValue("x")), Double.parseDouble(posElement.attributeValue("y")));
        ac.setCtrlPos1(p);
        // control2_pos
        posElement = pathElement.element("control2_pos");
        p = new Position(Double.parseDouble(posElement.attributeValue("x")), Double.parseDouble(posElement.attributeValue("y")));
        ac.setCtrlPos2(p);


        List<Element> properties = pathElement.elements("property");
        for (Element property : properties) {
            if ("angle_compensation".equals(property.attributeValue("name"))) {
                ac.setAngleCompensation(NumberUtils.toDouble(property.attributeValue("value")));
                break;
            }
        }
        return ac;
    }
}