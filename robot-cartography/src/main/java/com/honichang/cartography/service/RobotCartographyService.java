package com.honichang.cartography.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.cartography.config.RobotCartographyConfig;
import com.honichang.cartography.model.AdvancedCurve;
import com.honichang.cartography.model.AdvancedPoint;
import com.honichang.cartography.model.MapData;
import com.honichang.cartography.plugin.KcMapRenderer;
import com.honichang.cartography.util.SvgMapParser;
import com.honichang.cartography.util.SvgMapUtil;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.common.utils.file.FileUploadUtils;
import com.honichang.common.utils.file.FileUtils;
import com.honichang.common.utils.file.MimeTypeUtils;
import com.honichang.dispactch.model.IPath;
import com.honichang.dispactch.model.IPathPoint;
import com.honichang.dispactch.util.GeoCalcUtil;
import com.honichang.framework.config.ServerConfig;
import com.honichang.point.domain.MapMain;
import com.honichang.point.domain.MapPath;
import com.honichang.point.domain.MapPoint;
import com.honichang.point.service.MapMainService;
import com.honichang.point.service.MapPathService;
import com.honichang.point.service.MapPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 机器人制图服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RobotCartographyService {

    @Resource
    private ServerConfig serverConfig;

    @Resource
    private MapMainService mapMainService;

    @Resource
    private MapPointService mapPointService;

    @Resource
    private MapPathService mapPathService;

    @Resource
    private RobotCartographyConfig robotCartographyConfig;


    /**
     * 系统启动时检查是否有默认地图，没有则初始化
     */
    @PostConstruct
    public void init() {
        // 检查是否有地图
        if (mapMainService.exists(new LambdaQueryWrapper<MapMain>()
                .eq(MapMain::getDelFlag, "0"))) {
            return;
        }
        // 新建默认地图
        MapMain mapMain = new MapMain();
        mapMain.setMapName("默认地图");
        mapMain.setScale(1.0);
        mapMain.setStatus("1"); // 暂时停用，因为还没有XMAP图层
        mapMain.setDelFlag("0");
        mapMain.setCreateBy("system");
        mapMain.setCreateTime(DateUtils.getNowDate());
        mapMain.setUpdateBy("system");
        mapMain.setUpdateTime(DateUtils.getNowDate());
        mapMain.setRemark("默认地图");
        mapMainService.save(mapMain);
    }


    /**
     * 上传图层
     *
     * @param file  CAD图层或XMAP图层
     * @param mapId 地图ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void importLayer(MultipartFile file, Long mapId) {
        try {
            // 获取文件扩展名
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            boolean isXmap = "xmap".equalsIgnoreCase(extension);

            // 获取地图实体
            MapMain mapMain = mapMainService.getById(mapId);
            if (mapMain == null) {
                throw new ServiceException("地图不存在");
            }

            // 检查==========
            // 1.CAD图层不存在不允许上传其他图层
            // 2.CAD图层存在不允许重复上传
            if (isXmap && mapMain.getCadUrl() == null) {
                throw new ServiceException("必须先上传CAD图层，再上传XMAP图层");
            }
            if (!isXmap && mapMain.getCadUrl() != null) {
                throw new ServiceException("CAD图层已存在，不允许重复上传");
            }

            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            Map<String, String> fullInfo = FileUploadUtils.upload2(filePath, file, MimeTypeUtils.MAP_EXTENSION);
            String fileName = fullInfo.get("filename");
            String url = serverConfig.getUrl() + fileName;
            String newFilename = FileUtils.getName(fileName);

            // 如果是xmap文件，执行转换
            if (isXmap) {
                try {
                    String absFileName = fullInfo.get("absFilename");
                    mapMain.setXmapInitPath(absFileName);
                    // 获取上传文件的完整路径
                    String fullPath = absFileName.replace("/", File.separator);

                    // 生成SVG文件路径（与xmap文件在同一目录，但扩展名为.svg）
                    String svgFileName = FilenameUtils.getBaseName(fileName) + ".svg";
                    String svgFullPath =
                            fullPath.substring(0, fullPath.lastIndexOf(File.separator) + 1) +
                                    FileUtils.getName(svgFileName);
                    mapMain.setXmapSvgPath(svgFullPath);

                    // 使用KC插件进行转换（假设使用KC插件，您可以根据需要修改）
                    MapData mapData = __renderToImage(fullPath, svgFullPath, mapMain.getScale());

                    // 赋值给mapMain
                    mapMain.setXmapWidth(mapData.getHeader().getWidth());
                    mapMain.setXmapHeight(mapData.getHeader().getHeight());
                    mapMain.setXmapSvgWidth(mapData.getHeader().getImageWidth());
                    mapMain.setXmapSvgHeight(mapData.getHeader().getImageHeight());
                    mapMain.setXmapVersion(mapData.getHeader().getMapVersion());
                    mapMain.setMinPosX(mapData.getHeader().getMinPos().getX());
                    mapMain.setMinPosY(mapData.getHeader().getMinPos().getY());
                    mapMain.setMaxPosX(mapData.getHeader().getMaxPos().getX());
                    mapMain.setMaxPosY(mapData.getHeader().getMaxPos().getY());
                    mapMain.setXmapFilename(file.getOriginalFilename());
                    mapMain.setOffsetX(0.0);
                    mapMain.setOffsetY(0.0);

                    // 刷新地图的xmap地图点位和路径
                    List<MapPoint> mapPoints = __saveMapPoint(mapMain, mapData);
                    __saveMapPath(mapMain, mapData, mapPoints);

                } catch (Exception e) {
                    log.error("转换xmap文件失败", e);
                    throw new ServiceException("转换xmap文件失败");
                }
            } else { // CAD图层
                String absFileName = fullInfo.get("absFilename");
                mapMain.setCadPath(absFileName);
                // 使用SvgMapParser解析SVG文件，传入已获取的MapMain实体
                SvgMapParser.parseSvgFile(absFileName, url, mapMain);
            }

            // DB: Update MapMain
            mapMain.setUpdateBy(SecurityUtils.getUserId().toString());
            mapMain.setUpdateTime(DateUtils.getNowDate());
            mapMainService.updateById(mapMain);

            // 重要：预缓存svg文件到内存中，否则无法维护地图的平移操作
            if (isXmap) {
                SvgMapUtil.preloadSvg(mapMain.getXmapSvgPath(), "xmap_" + mapMain.getId());
                // http://localhost:8080/profile/map/merge_1.svg
                SvgMapUtil.mergeSvgFiles(
                        mapMain.getCadPath(),
                        mapMain.getXmapSvgPath(),
                        RuoYiConfig.getProfile() + "/map/merge_" + mapMain.getId() + ".svg",
                        mapMain.getOffsetX(),
                        mapMain.getOffsetY(),
                        "cad_" + mapMain.getId(),
                        "xmap_" + mapMain.getId()
                );
            } else {
                SvgMapUtil.preloadSvg(mapMain.getCadPath(), "cad_" + mapMain.getId());
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    /**
     * 保存偏移量
     *
     * @param mapId   地图ID
     * @param offsetX X轴偏移量
     * @param offsetY Y轴偏移量
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOffset(Long mapId, Double offsetX, Double offsetY) {
        try {
            // 获取地图实体
            MapMain mapMain = mapMainService.getById(mapId);
            if (mapMain == null) {
                throw new ServiceException("地图不存在");
            }

            // 保存偏移量
            mapMain.setOffsetX(offsetX);
            mapMain.setOffsetY(offsetY);

            // DB: Update MapMain
            mapMain.setUpdateBy(SecurityUtils.getUserId().toString());
            mapMain.setUpdateTime(DateUtils.getNowDate());
            mapMainService.updateById(mapMain);

            // 重新合并平移
            SvgMapUtil.mergeSvgFiles(
                    mapMain.getCadPath(),
                    mapMain.getXmapSvgPath(),
                    RuoYiConfig.getProfile() + "/map/merge_" + mapId + ".svg",
                    mapMain.getOffsetX(),
                    mapMain.getOffsetY(),
                    "cad_" + mapMain.getId(),
                    "xmap_" + mapMain.getId()
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 上传Xmap图层后，重新保存地图点位信息
     *
     * @return 保存后带ID的点位信息
     */
    private List<MapPoint> __saveMapPoint(MapMain mapMain, MapData mapData) {
        // 1. 删除所有点位
        mapPointService.removeByMapId(mapMain.getId());

        // 2. 写入新点位信息
        String userId = SecurityUtils.getUserId().toString();
        String remark = StringUtils.format("来自Xmap文件解析，文件名：{}", mapMain.getXmapFilename());
        String zero = "0";

        List<MapPoint> mapPoints = new ArrayList<>();
        for (IPathPoint point : mapData.getAdvancedPoints()) {
            AdvancedPoint ap = (AdvancedPoint) point;

            MapPoint mapPoint = new MapPoint();
            mapPoint.setXmapId(ap.getXmapId());
            mapPoint.setClassName(ap.getClassName());
            mapPoint.setInstanceName(ap.getInstanceName());
            mapPoint.setPosX(ap.getX());
            mapPoint.setPosY(ap.getY());
            mapPoint.setAllowRevolve(ap.getAllowRevolve());
            mapPoint.setAngle(ap.getAngle());
            mapPoint.setMapId(mapMain.getId());
            mapPoint.setStatus(zero);
            mapPoint.setDelFlag(zero);
            mapPoint.setCreateBy(userId);
            mapPoint.setCreateTime(DateUtils.getNowDate());
            mapPoint.setUpdateBy(userId);
            mapPoint.setUpdateTime(DateUtils.getNowDate());
            mapPoint.setRemark(remark);
            mapPoints.add(mapPoint);
        }

        // 3. 保存点位
        mapPointService.saveBatch(mapPoints);
        return mapPoints;
    }


    /**
     * 上传Xmap图层后，重新保存地图路径信息
     */
    private void __saveMapPath(MapMain mapMain, MapData mapData, List<MapPoint> mapPoints) {
        // 1. 删除所有路径
        mapPathService.removeByMapId(mapMain.getId());

        // 2. 写入新路径信息
        String userId = SecurityUtils.getUserId().toString();
        String remark = StringUtils.format("来自Xmap文件解析，文件名：{}", mapMain.getXmapFilename());
        String zero = "0";

        List<MapPath> mapPaths = new ArrayList<>();
        for (IPath path : mapData.getAdvancedCurves()) {

            MapPoint ps = mapPoints.stream().filter(mp -> mp.getXmapId().equals(path.getStartPos().getXmapId())).findFirst().orElse(null);
            MapPoint pe = mapPoints.stream().filter(mp -> mp.getXmapId().equals(path.getEndPos().getXmapId())).findFirst().orElse(null);
            if (ps == null) {
                throw new ServiceException("路径起点未找到对应点位：" + path.getStartPos().getXmapId());
            }
            if (pe == null) {
                throw new ServiceException("路径终点未找到对应点位：" + path.getEndPos().getXmapId());
            }


            AdvancedCurve ac = (AdvancedCurve) path;

            MapPath mapPath = new MapPath();
            mapPath.setXmapId(ac.getXmapId());
            mapPath.setInstanceName(ac.getInstanceName());
            mapPath.setRouteType(ac.getRouteType());
            mapPath.setRadian(ac.getRadian());
            mapPath.setXmapStartPosId(ac.getStartPos().getXmapId());
            mapPath.setXmapEndPosId(ac.getEndPos().getXmapId());
            mapPath.setStartPosId(ps.getId());
            mapPath.setEndPosId(pe.getId());
            mapPath.setStartX(ac.getStartPos().getX());
            mapPath.setStartY(ac.getStartPos().getY());
            mapPath.setEndX(ac.getEndPos().getX());
            mapPath.setEndY(ac.getEndPos().getY());
            mapPath.setControlX1(ac.getCtrlPos1().getX());
            mapPath.setControlY1(ac.getCtrlPos1().getY());
            mapPath.setControlX2(ac.getCtrlPos2().getX());
            mapPath.setControlY2(ac.getCtrlPos2().getY());
            mapPath.setAngleCompensation(ac.getAngleCompensation());
            mapPath.setDirection(ac.getDirection());
            mapPath.setRealLength(GeoCalcUtil.calculateLength(ac.getRouteType(), ac.getStartPos(), ac.getCtrlPos1(), ac.getCtrlPos2(), ac.getEndPos(), ac.getRadian()));
            mapPath.setMapId(mapMain.getId());
            mapPath.setStatus(zero);
            mapPath.setDelFlag(zero);
            mapPath.setCreateBy(userId);
            mapPath.setCreateTime(DateUtils.getNowDate());
            mapPath.setUpdateBy(userId);
            mapPath.setUpdateTime(DateUtils.getNowDate());
            mapPath.setRemark(remark);
            mapPaths.add(mapPath);
        }

        // 3. 保存路径
        mapPathService.saveBatch(mapPaths);
    }


    //---------------------------------------------------------------------------------------
    // 内部方法
    private MapData __renderToImage(String fullPath, String svgFullPath, Double scale) {
        // 科聪：
        try {
            return KcMapRenderer.renderToImage(fullPath, svgFullPath, robotCartographyConfig, scale);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
