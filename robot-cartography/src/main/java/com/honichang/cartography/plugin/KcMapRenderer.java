package com.honichang.cartography.plugin;

import com.honichang.cartography.config.RobotCartographyConfig;
import com.honichang.cartography.model.MapData;
import com.honichang.cartography.util.VectorMapRenderer;
import com.honichang.cartography.util.XmapParser;
import lombok.extern.slf4j.Slf4j;

/**
 * 科聪地图渲染工具
 */
@Slf4j
public class KcMapRenderer {

    public static MapData renderToImage(String xmapPath, String outputPath, RobotCartographyConfig config, Double scale) throws Exception {
        MapData mapData = XmapParser.parseXmap(xmapPath);
        log.info("地图解析完成：共有点位{}个，路径{}个，路径点{}个", mapData.getPoints().size(), mapData.getAdvancedCurves().size(), mapData.getAdvancedPoints().size());

        VectorMapRenderer.renderToImage(mapData, outputPath, config, scale);
        log.info("地图渲染完成，保存至：{}", outputPath);
        return mapData;
    }

}
