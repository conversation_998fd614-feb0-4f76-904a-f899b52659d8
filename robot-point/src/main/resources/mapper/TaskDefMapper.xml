<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.TaskDefMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.TaskDef">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
            <result property="priorty" column="priorty" jdbcType="DOUBLE"/>
            <result property="planType" column="plan_type" jdbcType="INTEGER"/>
            <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="cycleType" column="cycle_type" jdbcType="INTEGER"/>
            <result property="cycleTrigger" column="cycle_trigger" jdbcType="VARCHAR"/>
            <result property="cycleScope" column="cycle_scope" jdbcType="INTEGER"/>
            <result property="cycleInterval" column="cycle_interval" jdbcType="INTEGER"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="latestExecTime" column="latest_exec_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_name,priorty,
        plan_type,begin_time,end_time,
        cycle_type,cycle_trigger,cycle_scope,
        cycle_interval,dept_id,latest_exec_time,
        status,del_flag,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
    <select id="selectList" resultType="com.honichang.point.domain.TaskDef">

        select
            *,
            b.dept_name,
            (select count(1) from task_robot c where a.id = c.task_id) as robot_num
        from
            task_def a
            left join sys_dept b on a.dept_id = b.dept_id
        where a.del_flag = '0' and a.status = '0'
        <if test="taskName != null and taskName != ''">
            AND a.task_name  like '%'||#{taskName}||'%'
        </if>
        <if test="planType != null and planType != ''">
            AND a.plan_type  = #{planType}
        </if>
        order by a.id desc
    </select>
</mapper>
