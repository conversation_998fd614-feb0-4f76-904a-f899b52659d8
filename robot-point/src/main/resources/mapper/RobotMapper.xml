<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.RobotMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.Robot">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="robotName" column="robot_name" jdbcType="VARCHAR"/>
            <result property="robotModel" column="robot_model" jdbcType="VARCHAR"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="beginUseTime" column="begin_use_time" jdbcType="TIMESTAMP"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="port" column="port" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="appCode" column="app_code" jdbcType="VARCHAR"/>
            <result property="priority" column="priority" jdbcType="DOUBLE"/>
            <result property="mapId" column="map_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="workStatus" column="work_status" jdbcType="INTEGER"/>
            <result property="workMode" column="work_mode" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>

            <collection property="robotDynamicAttr" ofType="com.honichang.point.domain.RobotDynamicAttr">
                <id property="id" column="id" jdbcType="BIGINT"/>
                <result property="posX" column="pos_x" jdbcType="DOUBLE"/>
                <result property="posY" column="pos_y" jdbcType="DOUBLE"/>
                <result property="theta" column="theta" jdbcType="DOUBLE"/>
                <result property="reliability" column="reliability" jdbcType="DOUBLE"/>
                <result property="xmapPointId" column="xmap_point_id" jdbcType="VARCHAR"/>
                <result property="xmapPathId" column="xmap_path_id" jdbcType="VARCHAR"/>
                <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
                <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
                <result property="compassQuality" column="compass_quality" jdbcType="CHAR"/>
                <result property="compass" column="compass" jdbcType="DOUBLE"/>
                <result property="latestTaskClass" column="latest_task_class" jdbcType="INTEGER"/>
                <result property="latestTaskId" column="latest_task_id" jdbcType="BIGINT"/>
                <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
                <result property="totalRunning" column="total_running" jdbcType="BIGINT"/>
                <result property="totalOdom" column="total_odom" jdbcType="DOUBLE"/>
                <result property="status" column="status" jdbcType="VARCHAR"/>
                <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="remark" column="remark" jdbcType="VARCHAR"/>
            </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,robot_name,robot_model,
        sn,begin_use_time,ip,
        port,version,app_code,
        priority,map_id,status,work_status,work_mode,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
    <select id="getRobotList" resultMap="BaseResultMap">
      select
          robot.id,robot.robot_name,robot.robot_model,robot.
          sn,robot.begin_use_time,robot.ip,robot.
          port,robot.version,robot.app_code,robot.
          priority,robot.map_id,robot.status,robot.work_status,robot.work_mode,robot.
          del_flag,robot.create_by,robot.create_time,robot.
          update_by,robot.update_time,robot.remark,
       robotDynamicAttr.id,robotDynamicAttr.pos_x,robotDynamicAttr.pos_y,robotDynamicAttr.
       theta,robotDynamicAttr.reliability,robotDynamicAttr.xmap_point_id,robotDynamicAttr.
       xmap_path_id,robotDynamicAttr.longitude,robotDynamicAttr.latitude,robotDynamicAttr.
       compass_quality,robotDynamicAttr.compass,robotDynamicAttr.latest_task_class,robotDynamicAttr.latest_task_id,robotDynamicAttr.
       robot_id,robotDynamicAttr.total_running,robotDynamicAttr.total_odom,robotDynamicAttr.status,robotDynamicAttr.del_flag,robotDynamicAttr.
       create_by,robotDynamicAttr.create_time,robotDynamicAttr.update_by,robotDynamicAttr.
       update_time,robotDynamicAttr.remark
       from robot as robot
      left join robot_dynamic_attr as robotDynamicAttr on robot.id = robotDynamicAttr.robot_id
      where 1=1
      and robot.del_flag='0'
      order by robot.id desc

    </select>
</mapper>
