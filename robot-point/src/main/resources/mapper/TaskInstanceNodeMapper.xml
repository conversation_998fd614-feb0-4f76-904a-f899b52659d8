<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.TaskInstanceNodeMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.TaskInstanceNode">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="arriveTime" column="arrive_time" jdbcType="TIMESTAMP"/>
            <result property="leaveTime" column="leave_time" jdbcType="TIMESTAMP"/>
            <result property="idenTime" column="iden_time" jdbcType="TIMESTAMP"/>
            <result property="planNextId" column="plan_next_id" jdbcType="BIGINT"/>
            <result property="actualNextId" column="actual_next_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="toolGroupId" column="tool_group_id" jdbcType="BIGINT"/>
            <result property="toolId" column="tool_id" jdbcType="VARCHAR"/>
            <result property="pointName" column="point_name" jdbcType="VARCHAR"/>
            <result property="idenModelId" column="iden_model_id" jdbcType="BIGINT"/>
            <result property="idenModelName" column="iden_model_name" jdbcType="VARCHAR"/>
            <result property="bizPointId" column="biz_point_id" jdbcType="BIGINT"/>
            <result property="taskId" column="task_id" jdbcType="BIGINT"/>
            <result property="taskInstanceId" column="task_instance_id" jdbcType="BIGINT"/>
            <result property="ruleResult" column="rule_result" jdbcType="VARCHAR"/>
            <result property="ruleExpressionText" column="rule_expression_text" jdbcType="OTHER"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,arrive_time,leave_time,iden_time,
        plan_next_id,actual_next_id,dept_id,
        tool_group_id,tool_id,point_name,
        iden_model_id,iden_model_name,biz_point_id,
        task_id,task_instance_id,rule_result,
        rule_expression_text,robot_id,auditor,
        audit_status,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
    <select id="getHistory" parameterType="com.honichang.machine.domain.HistoryDto" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            task_instance_node A
        WHERE
            iden_time IS NOT NULL
        <if test="startTime!=null and startTime!=''">
            and A.iden_time >= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            and A.iden_time >= #{endTime}
        </if>
        ORDER BY
            iden_time DESC
    </select>

    <select id="selectAlarmPageList" resultType="com.honichang.point.model.vo.TaskInstanceNodeVo">

        select
        a.*,
        c.task_name,
        g.device_name as dept_name,
        e.robot_name
        from
        task_instance_node a
        left join task_instance b on a.task_instance_id = b.id
        left join task_def c on a.task_id = c.id
        left join biz_point d on a.biz_point_id = d.id
        left join robot e on a.robot_id = e.id
        left join iden_model f on a.iden_model_id = f.id
        left join biz_device_tree g on a.dept_id = g.id
        where 1=1
        and a.status = '9'
        and his_flag = #{hisFlag}
        <if test="taskName != null and taskName != ''">
            AND c.task_name  like '%'||#{taskName}||'%'
        </if>
        <if test="pointName != null and pointName != ''">
            AND a.point_name  like '%'||#{pointName}||'%'
        </if>
        <if test="robotName != null and robotName != ''">
            AND e.robot_name  like '%'||#{robotName}||'%'
        </if>
        ORDER BY
        a.iden_time DESC
    </select>
</mapper>
