<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.TaskInstanceNodeMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.TaskInstanceNode">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="arriveTime" column="arrive_time" jdbcType="TIMESTAMP"/>
            <result property="leaveTime" column="leave_time" jdbcType="TIMESTAMP"/>
            <result property="idenTime" column="iden_time" jdbcType="TIMESTAMP"/>
            <result property="planNextId" column="plan_next_id" jdbcType="BIGINT"/>
            <result property="actualNextId" column="actual_next_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="toolGroupId" column="tool_group_id" jdbcType="BIGINT"/>
            <result property="toolId" column="tool_id" jdbcType="VARCHAR"/>
            <result property="pointName" column="point_name" jdbcType="VARCHAR"/>
            <result property="idenModelId" column="iden_model_id" jdbcType="BIGINT"/>
            <result property="idenModelName" column="iden_model_name" jdbcType="VARCHAR"/>
            <result property="bizPointId" column="biz_point_id" jdbcType="BIGINT"/>
            <result property="taskId" column="task_id" jdbcType="BIGINT"/>
            <result property="taskInstanceId" column="task_instance_id" jdbcType="BIGINT"/>
            <result property="ruleResult" column="rule_result" jdbcType="VARCHAR"/>
            <result property="ruleExpressionText" column="rule_expression_text" jdbcType="OTHER"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,arrive_time,leave_time,iden_time,
        plan_next_id,actual_next_id,dept_id,
        tool_group_id,tool_id,point_name,
        iden_model_id,iden_model_name,biz_point_id,
        task_id,task_instance_id,rule_result,
        rule_expression_text,robot_id,auditor,
        audit_status,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
