<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.RobotChargingPileMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.RobotChargingPile">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="port" column="port" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="xmapId" column="xmap_id" jdbcType="VARCHAR"/>
            <result property="mapId" column="map_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ip,port,
        location,xmap_point_id,
        map_id,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
    <select id="selectPageList" resultType="com.honichang.point.domain.RobotChargingPile">

        select
        a.*
        from
        robot_charging_pile a
        where a.del_flag = '0' and A.status = '0'
        <if test="location != null and location != ''">
            AND a.location  like '%'||#{location}||'%'
        </if>
        <if test="ip != null and ip != ''">
            AND a.ip  like '%'||#{ip}||'%'
        </if>
        order by a.id desc

    </select>
</mapper>
