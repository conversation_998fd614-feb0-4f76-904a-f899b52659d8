<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.RobotHardwareMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.RobotHardware">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="hardwareName" column="hardware_name" jdbcType="VARCHAR"/>
            <result property="sn" column="sn" jdbcType="VARCHAR"/>
            <result property="hardwareType" column="hardware_type" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="port" column="port" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="pwd" column="pwd" jdbcType="VARCHAR"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,hardware_name,sn,
        hardware_type,position,ip,
        port,account,pwd,
        robot_id,status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
</mapper>
