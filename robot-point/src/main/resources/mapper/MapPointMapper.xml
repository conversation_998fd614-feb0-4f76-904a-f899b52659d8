<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.MapPointMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.MapPoint">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="xmapId" column="xmap_id" jdbcType="VARCHAR"/>
            <result property="className" column="class_name" jdbcType="VARCHAR"/>
            <result property="instanceName" column="instance_name" jdbcType="VARCHAR"/>
            <result property="posX" column="pos_x" jdbcType="DOUBLE"/>
            <result property="posY" column="pos_y" jdbcType="DOUBLE"/>
            <result property="allowRevolve" column="allow_revolve" jdbcType="CHAR"/>
            <result property="angle" column="angle" jdbcType="DOUBLE"/>
            <result property="mapId" column="map_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,xmap_id,class_name,
        instance_name,pos_x,pos_y,
        allow_revolve,angle,
        map_id,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
