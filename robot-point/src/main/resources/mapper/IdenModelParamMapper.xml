<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.IdenModelParamMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.IdenModelParam">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="paramName" column="param_name" jdbcType="VARCHAR"/>
            <result property="idenTypeName" column="iden_type_name" jdbcType="VARCHAR"/>
            <result property="dataType" column="data_type" jdbcType="INTEGER"/>
            <result property="seq" column="seq" jdbcType="INTEGER"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="offsetValue" column="offset_value" jdbcType="DOUBLE"/>
            <result property="precisionNum" column="precision_num" jdbcType="INTEGER"/>
            <result property="scaling" column="scaling" jdbcType="DOUBLE"/>
            <result property="minLimit" column="min_limit" jdbcType="DOUBLE"/>
            <result property="maxLimit" column="max_limit" jdbcType="DOUBLE"/>
            <result property="idenTypeId" column="iden_type_id" jdbcType="BIGINT"/>
            <result property="idenModelId" column="iden_model_id" jdbcType="BIGINT"/>
            <result property="paramGroupId" column="param_group_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,param_name,iden_type_name,
        data_type,seq,unit,
        offset_value,precision_num,scaling,
        min_limit,max_limit,iden_type_id,
        iden_model_id,param_group_id,status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
    <select id="selectParamList" resultType="com.honichang.point.domain.IdenModelParam">

        select
        a.*,
        c.iden_type_name as iden_name
        from
        iden_model_param a
        left join iden_model b on a.iden_model_id = b.id
        left join iden_type c on a.iden_type_id = c.id
        where a.del_flag = '0' and b.status = '0' and b.del_flag = '0'
        <if test="idenModelId != null">
            AND a.iden_model_id = #{idenModelId}
        </if>
        <if test="idenModelId == null">
            AND 1 = 2
        </if>
        <if test="paramName != null and paramName != ''">
            AND a.param_name  like '%'||#{paramName}||'%'
        </if>
        order by a.id desc

    </select>
</mapper>
