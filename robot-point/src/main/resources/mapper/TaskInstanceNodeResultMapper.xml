<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.TaskInstanceNodeResultMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.TaskInstanceNodeResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="idenModelParamName" column="iden_model_param_name" jdbcType="VARCHAR"/>
            <result property="dataType" column="data_type" jdbcType="INTEGER"/>
            <result property="floatValue" column="float_value" jdbcType="DOUBLE"/>
            <result property="enumKey" column="enum_key" jdbcType="VARCHAR"/>
            <result property="enumValue" column="enum_value" jdbcType="VARCHAR"/>
            <result property="correctFlag" column="correct_flag" jdbcType="CHAR"/>
            <result property="actualFloatValue" column="actual_float_value" jdbcType="DOUBLE"/>
            <result property="taskId" column="task_id" jdbcType="BIGINT"/>
            <result property="taskInstanceId" column="task_instance_id" jdbcType="BIGINT"/>
            <result property="taskInstanceNodeId" column="task_instance_node_id" jdbcType="BIGINT"/>
            <result property="idenModelParamId" column="iden_model_param_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="idenTime" column="iden_time" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,iden_model_param_name,data_type,
        float_value,enum_key,enum_value,
        correct_flag,actual_float_value,task_id,
        task_instance_id,task_instance_node_id,iden_model_param_id,
        status,del_flag,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
    <select id="getHistoryCurve" parameterType="com.honichang.machine.domain.HistoryCurveDto" resultMap="BaseResultMap">
        SELECT
            a.* ,
            b.iden_time
        FROM
            task_instance_node_result A,
            task_instance_node b
        WHERE
            A.task_instance_node_id = b.id
            AND b.iden_time IS NOT NULL
            AND b.biz_point_id = #{bizPointId}
            AND a.iden_model_param_id = #{idenModelParamId}
            ORDER BY b.iden_time ASC
    </select>
</mapper>
