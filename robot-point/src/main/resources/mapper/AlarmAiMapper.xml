<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.AlarmAiMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.AlarmAi">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="alarmName" column="alarm_name" jdbcType="VARCHAR"/>
            <result property="idenType" column="iden_type" jdbcType="INTEGER"/>
            <result property="signType" column="sign_type" jdbcType="INTEGER"/>
            <result property="nearPointId" column="near_point_id" jdbcType="BIGINT"/>
            <result property="posX" column="pos_x" jdbcType="DOUBLE"/>
            <result property="posY" column="pos_y" jdbcType="DOUBLE"/>
            <result property="expressionText" column="expression_text" jdbcType="VARCHAR"/>
            <result property="idenResult" column="iden_result" jdbcType="VARCHAR"/>
            <result property="alarmLevel" column="alarm_level" jdbcType="VARCHAR"/>
            <result property="triggerTime" column="trigger_time" jdbcType="TIMESTAMP"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="idenRuleId" column="iden_rule_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="taskInstanceId" column="task_instance_id" jdbcType="BIGINT"/>
            <result property="confirmStatus" column="confirm_status" jdbcType="CHAR"/>
            <result property="confirmPerson" column="confirm_person" jdbcType="CHAR"/>
            <result property="confirmTime" column="confirm_time" jdbcType="CHAR"/>
            <result property="confirmContent" column="confirm_content" jdbcType="CHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,alarm_name,iden_type,
        sign_type,near_point_id,pos_x,
        pos_y,expression_text,iden_result,
        alarm_level,trigger_time,robot_id,
        iden_rule_id,dept_id,task_instance_id,
        confirm_status,confirm_person,confirm_time,confirm_content
        status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
