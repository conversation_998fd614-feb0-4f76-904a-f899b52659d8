<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.IdenModelParamGroupMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.IdenModelParamGroup">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="dataType" column="data_type" jdbcType="INTEGER"/>
            <result property="idenTypeNum" column="iden_type_num" jdbcType="INTEGER"/>
            <result property="idenTypeId" column="iden_type_id" jdbcType="BIGINT"/>
            <result property="idenModelId" column="iden_model_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,data_type,param_num,
        iden_id,iden_model_id,status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
    <select id="selectParamGroupList" resultType="com.honichang.point.domain.IdenModelParamGroup">


        select
        a.*,
        c.iden_type_name
        from
        iden_model_param_group a
        left join iden_model b on a.iden_model_id = b.id
        left join iden_type c on a.iden_type_id = c.id
        where a.del_flag = '0'
            and b.status = '0'
            and b.del_flag = '0'
            and c.status = '0'
            and c.del_flag = '0'
        <if test="idenModelId != null">
            AND a.iden_model_id = #{idenModelId}
        </if>
        <if test="idenTypeId != null">
            AND a.iden_type_id = #{idenTypeId}
        </if>
        order by a.id desc

    </select>
</mapper>
