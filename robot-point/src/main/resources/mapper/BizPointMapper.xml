<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.BizPointMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.BizPoint">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="className" column="class_name" jdbcType="VARCHAR"/>
            <result property="instanceName" column="instance_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="toolGroupId" column="tool_group_id" jdbcType="BIGINT"/>
            <result property="toolId" column="tool_id" jdbcType="VARCHAR"/>
            <result property="ptzAngleTb" column="ptz_angle_tb" jdbcType="DOUBLE"/>
            <result property="ptzAngleLr" column="ptz_angle_lr" jdbcType="DOUBLE"/>
            <result property="cameraFocus" column="camera_focus" jdbcType="DOUBLE"/>
            <result property="cameraZoom" column="camera_zoom" jdbcType="DOUBLE"/>
            <result property="telescopicRodH" column="telescopic_rod_h" jdbcType="DOUBLE"/>
            <result property="idenRuleId" column="iden_rule_id" jdbcType="BIGINT"/>
            <result property="deviceClass" column="device_class" jdbcType="VARCHAR"/>
            <result property="onSitePhotoUrl" column="on_site_photo_url" jdbcType="VARCHAR"/>
            <result property="onSitePhotoPath" column="on_site_photo_path" jdbcType="VARCHAR"/>
            <result property="markedPhotoUrl" column="marked_photo_url" jdbcType="VARCHAR"/>
            <result property="markedPhotoPath" column="marked_photo_path" jdbcType="VARCHAR"/>
            <result property="idenModelId" column="iden_model_id" jdbcType="BIGINT"/>
            <result property="xmapId" column="xmap_id" jdbcType="VARCHAR"/>
            <result property="mapPointId" column="map_point_id" jdbcType="BIGINT"/>
            <result property="editStatus" column="edit_status" jdbcType="CHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>

        <result property="taskInstanceNodeId" column="task_instance_node_id" jdbcType="VARCHAR"/>
        <result property="taskInstanceNodeStatus" column="task_instance_node_status" jdbcType="VARCHAR"/>
        <result property="taskInstanceNodeIdenTime" column="task_instance_node_iden_time" jdbcType="VARCHAR"/>
        <result property="taskInstanceNodeAuditStatus" column="task_instance_node_audit_status" jdbcType="VARCHAR"/>
        <result property="taskInstanceNodeRuleExpressionText" column="task_instance_rule_expression_text" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,class_name,instance_name,
        dept_id,tool_group,tool_id,
        ptz_angle_tb,ptz_angle_lr,camera_focus,
        camera_zoom,telescopic_rod_h,iden_rule_id,
        device_class,on_site_photo_url,on_site_photo_path,
        marked_photo_url,marked_photo_path,iden_model_id,
        xmap_id,map_point_id,edit_status,
        status,del_flag,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
    <select id="getBizPointList" parameterType="com.honichang.point.domain.BizPoint" resultMap="BaseResultMap">
        SELECT A
                   .* ,
               b.task_instance_node_id,
               b.task_instance_node_status,
               b.task_instance_node_iden_time,
               b.task_instance_node_audit_status
        FROM
            biz_point
                A LEFT JOIN (
                SELECT DISTINCT ON
                    ( task_instance_node.biz_point_id ) biz_point_id,
                    task_instance_node.ID task_instance_node_id,
                    task_instance_node.status task_instance_node_status,
                    task_instance_node.iden_time task_instance_node_iden_time,
                    task_instance_node.rule_expression_text task_instance_rule_expression_text,
                    task_instance_node.audit_status task_instance_node_audit_status
                FROM
                    task_instance_node
                WHERE
                    task_instance_node.iden_time IS NOT NULL
                ORDER BY
                    task_instance_node.biz_point_id,
                    task_instance_node.iden_time DESC
            ) b ON A.ID = b.biz_point_id
        WHERE
            1 = 1
          AND A.del_flag = '0'
          AND A.class_name in ('IdenPoint','InfraredDetection')
        <if test="instanceName!=null and instanceName!=''">
            AND  A.instance_name  like '%'||#{instanceName}||'%'
        </if>

    </select>
</mapper>
