<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.AlarmEscortMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.AlarmEscort">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="alarmType" column="alarm_type" jdbcType="INTEGER"/>
            <result property="alarmContent" column="alarm_content" jdbcType="VARCHAR"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="taskEscortId" column="task_escort_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,alarm_type,alarm_content,
        robot_id,task_escort_id,status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
    <select id="selectPageList" resultType="com.honichang.point.domain.AlarmEscort">

        select
        a.*,
        b.robot_name,
        c.task_name
        from
        alarm_escort a
        left join robot b on a.robot_id = b.id
        left join task_escort c on a.task_escort_id = c.id
        where a.del_flag = '0'
        <if test="alarmContent != null and alarmContent != ''">
            AND a.alarm_content  like '%'||#{alarmContent}||'%'
        </if>
        <if test="alarmType != null and alarmType != ''">
            AND a.alarm_type = #{alarmType}
        </if>
        <if test="robotId != null">
            AND b.id = #{robotId}
        </if>
        <if test="robotName != null and robotName != ''">
            AND b.robot_name  like '%'||#{robotName}||'%'
        </if>
        <if test="beginTime != null and endTime != ''"><!-- 结束时间检索 -->
            and a.create_time between #{beginTime} and #{endTime}
        </if>
        order by a.id desc
    </select>
</mapper>
