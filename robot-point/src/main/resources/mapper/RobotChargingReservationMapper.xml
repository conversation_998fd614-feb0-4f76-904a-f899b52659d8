<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.RobotChargingReservationMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.RobotChargingReservation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="seq" column="seq" jdbcType="BIGINT"/>
            <result property="pileId" column="pile_id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,seq,pile_id,
        robot_id,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
