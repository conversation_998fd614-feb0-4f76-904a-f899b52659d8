<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.RobotDynamicAttrMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.RobotDynamicAttr">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="posX" column="pos_x" jdbcType="DOUBLE"/>
            <result property="posY" column="pos_y" jdbcType="DOUBLE"/>
            <result property="theta" column="theta" jdbcType="DOUBLE"/>
            <result property="reliability" column="reliability" jdbcType="DOUBLE"/>
            <result property="xmapPointId" column="xmap_point_id" jdbcType="VARCHAR"/>
            <result property="xmapPathId" column="xmap_path_id" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
            <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
            <result property="compassQuality" column="compass_quality" jdbcType="CHAR"/>
            <result property="compass" column="compass" jdbcType="DOUBLE"/>
            <result property="latestTaskClass" column="latest_task_class" jdbcType="INTEGER"/>
            <result property="latestTaskId" column="latest_task_id" jdbcType="BIGINT"/>
            <result property="robotId" column="robot_id" jdbcType="BIGINT"/>
            <result property="totalRunning" column="total_running" jdbcType="BIGINT"/>
            <result property="totalOdom" column="total_odom" jdbcType="DOUBLE"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pos_x,pos_y,
        theta,reliability,xmap_point_id,
        xmap_path_id,longitude,latitude,
        compass_quality,compass,latest_task_class,latest_task_id,
        robot_id,total_running,total_odom,status,del_flag,
        create_by,create_time,update_by,
        update_time,remark
    </sql>
</mapper>
