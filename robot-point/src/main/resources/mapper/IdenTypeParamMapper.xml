<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.IdenTypeParamMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.IdenTypeParam">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="paramName" column="param_name" jdbcType="VARCHAR"/>
            <result property="dataType" column="data_type" jdbcType="INTEGER"/>
            <result property="valueScopeJson" column="value_scope_json" jdbcType="OTHER"/>
            <result property="idenTypeId" column="iden_type_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,param_name,data_type,
        value_scope_json,iden_type_id,status,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
    <select id="selectParamList" resultType="com.honichang.point.domain.IdenTypeParam">

        select
            a.*
        from
            iden_type_param a
            left join iden_type b on a.iden_type_id = b.id
        where a.del_flag = '0' and b.status = '0' and b.del_flag = '0'
        <if test="idenTypeId != null">
            AND a.iden_type_id = #{idenTypeId}
        </if>
        <if test="idenTypeId == null">
            AND 1 = 2
        </if>
        <if test="paramName != null and paramName != ''">
            AND a.param_name  like '%'||#{paramName}||'%'
        </if>
        <if test="dataType != null and dataType != ''">
            AND a.data_type = #{dataType}
        </if>
        order by a.id desc
    </select>
</mapper>
