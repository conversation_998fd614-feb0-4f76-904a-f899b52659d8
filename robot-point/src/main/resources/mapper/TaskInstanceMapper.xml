<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.honichang.point.mapper.TaskInstanceMapper">

    <resultMap id="BaseResultMap" type="com.honichang.point.domain.TaskInstance">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
            <result property="envReport" column="env_report" jdbcType="VARCHAR"/>
            <result property="pointCount" column="point_count" jdbcType="INTEGER"/>
            <result property="aiAlarmNum" column="ai_alarm_num" jdbcType="INTEGER"/>
            <result property="pointAlarmNum" column="point_alarm_num" jdbcType="INTEGER"/>
            <result property="pointHealthNum" column="point_health_num" jdbcType="INTEGER"/>
            <result property="unreachableNum" column="unreachable_num" jdbcType="INTEGER"/>
            <result property="abnormalNum" column="abnormal_num" jdbcType="INTEGER"/>
            <result property="taskId" column="task_id" jdbcType="BIGINT"/>
            <result property="mapId" column="map_id" jdbcType="BIGINT"/>
            <result property="terminationReason" column="termination_reason" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="hisFlag" column="his_flag" jdbcType="CHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,start_time,finish_time,
        env_report,point_count,ai_alarm_num,
        point_alarm_num,point_health_num,unreachable_num,abnormal_num,
        task_id,map_id,termination_reason,
        audit_status,status,his_flag,
        del_flag,create_by,create_time,
        update_by,update_time,remark
    </sql>
</mapper>
