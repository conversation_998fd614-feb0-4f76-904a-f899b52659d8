package com.honichang.point.mapper;

import com.honichang.machine.domain.HistoryDto;
import com.honichang.point.domain.TaskInstanceNode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honichang.point.model.dto.TaskInstanceNodeDto;
import com.honichang.point.model.vo.TaskInstanceNodeVo;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.TaskInstanceNode
 */
public interface TaskInstanceNodeMapper extends BaseMapper<TaskInstanceNode> {

    List<TaskInstanceNode> getHistory(HistoryDto historyDto);

    List<TaskInstanceNodeVo> selectAlarmPageList(TaskInstanceNodeDto taskInstanceNodeDto);
}




