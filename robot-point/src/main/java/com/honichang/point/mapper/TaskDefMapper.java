package com.honichang.point.mapper;

import com.honichang.point.domain.TaskDef;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * @Entity com.honichang.point.domain.TaskDef
 */
@Mapper
public interface TaskDefMapper extends BaseMapper<TaskDef> {

    List<TaskDef> selectList(TaskDef taskDef);

    /**
     * 查询所有循环任务，按优先级排序，计划开始时间在当前时间之前，计划结束时间在当前时间之后
     */
    @Select("select * from task_def where del_flag = '0'" +
            " and plan_type = 0 and begin_time <= #{date} and end_time >= #{date}" +
            " order by priorty desc")
    List<TaskDef> selectAllCycleTask(@Param("date") Date now);

    /**
     * 查询所有周期任务，按优先级排序
     */
    @Select("select * from task_def where del_flag = '0' and plan_type = 1 order by priorty desc")
    List<TaskDef> selectAllPeriodTask();

    /**
     * 查询指定机器人的所有周期任务，按优先级排序
     */
    @Select("select a.*, b.robot_id from task_def a, task_robot b" +
            " where a.id = b.task_id and a.del_flag = '0' and a.plan_type = 1 and b.robot_id = #{robotId}" +
            " order by priorty desc")
    List<TaskDef> selectAllPeriodTask(long robotId);

    /**
     * 查询所有周期任务，且在有效期内
     * cycle_scope=无限制，或从create_time算起30天内
     */
    @Select("select * from task_def where del_flag = '0' and plan_type = 1" +
            " and (cycle_scope = 0 or (cycle_scope = 1 and create_time >= CURRENT_DATE - INTERVAL '30 days'))" +
            " order by priorty desc")
    List<TaskDef> selectAllPeriodTaskInValidScope();
}




