package com.honichang.point.mapper;

import com.honichang.common.core.domain.entity.SysRole;
import com.honichang.point.domain.IdenTypeParam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.IdenTypeParam
 */
public interface IdenTypeParamMapper extends BaseMapper<IdenTypeParam> {


    /**
     * 根据条件分页查询识别类型参数数据
     *
     * @param idenTypeParam 识别类型参数信息
     * @return 参数数据集合信息
     */
    List<IdenTypeParam> selectParamList(IdenTypeParam idenTypeParam);


}




