package com.honichang.point.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honichang.point.domain.RobotHardware;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.RobotHardware
 */
public interface RobotHardwareMapper extends BaseMapper<RobotHardware> {

    /**
     * 根据硬件ip和端口获取机器人id
     */
    @Select("select * from robot_hardware where ip = #{ip} and port = #{port} and del_flag = '0'")
    List<RobotHardware> getHardwareByIpAndPort(String ip, String port);
}




