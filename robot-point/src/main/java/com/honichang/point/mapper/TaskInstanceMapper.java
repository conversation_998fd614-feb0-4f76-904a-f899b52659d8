package com.honichang.point.mapper;

import com.honichang.dispactch.context.RobotContext;
import com.honichang.point.domain.TaskInstance;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.TaskInstance
 */
public interface TaskInstanceMapper extends BaseMapper<TaskInstance> {

    List<TaskInstance> getList(TaskInstance taskInstance);

    /**
     * 获取所有未完成的任务
     * 按计划类型排序，周期任务在前，普通任务在后
     * @return 任务列表
     */
    @Select("select a.*, b.plan_type from task_instance a, task_def b" +
            " where a.task_id = b.id and a.del_flag = '0' and a.status in ('1', '4') and a.his_flag = '0'" +
            " order by b.plan_type desc")
    List<TaskInstance> getUnfinishedTasks();

    /**
     * 获取机器人负责的未完任务
     * ①按计划类型排序，周期任务在前，普通任务在后；②按优先级排序，优先级高的在前
     * @param robotId 机器人ID
     * @return 任务列表
     */
    @Select("select a.*, b.plan_type, b.task_name from task_instance a, task_def b" +
            " where a.task_id = b.id and a.del_flag = '0' and a.status in ('1', '4') and a.his_flag = '0' and a.robot_id = #{robotId}" +
            " order by b.plan_type desc, priorty desc")
    List<TaskInstance> getUnfinishedTasks(long robotId);
}

