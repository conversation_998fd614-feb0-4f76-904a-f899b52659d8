package com.honichang.point.mapper;

import com.honichang.point.domain.IdenModelParamGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.IdenModelParamGroup
 */
public interface IdenModelParamGroupMapper extends BaseMapper<IdenModelParamGroup> {

    /**
     * 根据条件分页查询识别模型参数组数据
     *
     * @param idenModelParamGroup 识别模型参数组信息
     * @return 参数组数据集合信息
     */
    List<IdenModelParamGroup> selectParamGroupList(IdenModelParamGroup idenModelParamGroup);
}




