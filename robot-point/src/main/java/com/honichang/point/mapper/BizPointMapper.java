package com.honichang.point.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honichang.point.domain.BizPoint;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.BizPoint
 */
public interface BizPointMapper extends BaseMapper<BizPoint> {

    /**
     * 根据xmapId和mapId查询点位信息
     * @param xmapId xmap地图id REF[map_point.xmap_id]
     * @param mapId 地图id REF[map_point.map_id]
     * @return bizPoint
     */
    @Select("select distinct a.* from biz_point a, map_point b" +
            " where a.map_point_id = b.id and b.xmap_id = #{xmapId} and b.map_id = #{mapId}" +
            " and a.del_flag = '0' and b.del_flag = '0'")
    List<BizPoint> selectByXmapId(String xmapId, Long mapId);

    public List<BizPoint> getBizPointList(BizPoint bizPoint);

    /**
     * 根据任务定义id获取点位信息
     *
     * @param taskDefId 任务定义id
     * @return 点位信息
     */
    @Select("select a.* from biz_point a, task_def_node b where a.id = b.biz_point_id and b.task_id = #{taskDefId} and b.del_flag = '0'")
    List<BizPoint> getPointsByTaskDef(long taskDefId);
}
