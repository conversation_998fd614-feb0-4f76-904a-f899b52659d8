package com.honichang.point.mapper;

import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.domain.TaskInstanceNodeResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.TaskInstanceNodeResult
 */
public interface TaskInstanceNodeResultMapper extends BaseMapper<TaskInstanceNodeResult> {
    List<TaskInstanceNodeResult> getHistoryCurve(HistoryCurveDto historyCurveDto);
}




