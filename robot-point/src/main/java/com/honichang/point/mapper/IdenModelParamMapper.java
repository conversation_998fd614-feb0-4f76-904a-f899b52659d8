package com.honichang.point.mapper;

import com.honichang.point.domain.IdenModelParam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honichang.point.domain.IdenTypeParam;

import java.util.List;

/**
 * @Entity com.honichang.point.domain.IdenModelParam
 */
public interface IdenModelParamMapper extends BaseMapper<IdenModelParam> {


    /**
     * 根据条件分页查询识别模型参数数据
     *
     * @param idenModelParam 识别模型参数信息
     * @return 参数数据集合信息
     */
    List<IdenModelParam> selectParamList(IdenModelParam idenModelParam);
}




