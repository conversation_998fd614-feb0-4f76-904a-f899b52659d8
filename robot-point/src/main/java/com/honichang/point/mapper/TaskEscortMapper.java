package com.honichang.point.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.honichang.point.domain.TaskEscort;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * @Entity com.honichang.point.domain.TaskEscort
 */
public interface TaskEscortMapper extends BaseMapper<TaskEscort> {

    /**
     * 获取时间范围内正在执行的陪同任务
     * 按执行时间先后排序
     */
    @Select("select * from task_escort where del_flag = '0'" +
            " and status <> '001' and status <> '501' and status not like '__9'" +
            " and (exec_time between #{date1} and #{date2})" +
            " order by exec_time")
    List<TaskEscort> getRunningEscortTasks(@Param("date1") Date date1, @Param("date2") Date date2);

    /**
     * 获取时间范围内未开始的陪同任务
     * 按执行时间先后排序
     */
    @Select("select * from task_escort where del_flag = '0'" +
            " and status = '001'" +
            " and (exec_time between #{date1} and #{date2})" +
            " order by exec_time")
    List<TaskEscort> getPendingEscortTasks(Date d1, Date d2);

    /**
     * 获取机器人正在执行或未开始的陪同任务
     */
    @Select("select * from task_escort where del_flag = '0'" +
            " and status <> '501' and status not like '__9' and robot_id = #{robotId}" +
            " and (exec_time between #{date1} and #{date2})" +
            " order by exec_time")
    List<TaskEscort> getPendingOrRunningEscortTasks(@Param("robotId") long robotId, @Param("date1") Date date1, @Param("date2") Date date2);

    List<TaskEscort> getRunTask(TaskEscort taskEscort);

}




