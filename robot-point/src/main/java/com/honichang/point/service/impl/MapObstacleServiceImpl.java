package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.MapObstacle;
import com.honichang.point.service.MapObstacleService;
import com.honichang.point.mapper.MapObstacleMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class MapObstacleServiceImpl extends ServiceImpl<MapObstacleMapper, MapObstacle>
    implements MapObstacleService{

    @Override
    public List<MapObstacle> getObstaclesByMapId(Long mapId) {
         return list(new LambdaQueryWrapper<MapObstacle>()
                .eq(MapObstacle::getMapId, mapId)
                .eq(MapObstacle::getDelFlag, "0"));
    }
}




