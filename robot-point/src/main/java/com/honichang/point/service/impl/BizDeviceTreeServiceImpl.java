package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.poi.ExcelUtil;
import com.honichang.point.domain.BizDeviceTree;
import com.honichang.point.service.BizDeviceTreeService;
import com.honichang.point.mapper.BizDeviceTreeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 *
 */
@Service
public class BizDeviceTreeServiceImpl extends ServiceImpl<BizDeviceTreeMapper, BizDeviceTree>
    implements BizDeviceTreeService{

    @Autowired
    private BizDeviceTreeMapper bizDeviceTreeMapper;

    @Override
    public List<BizDeviceTree> selectDeviceTreeList(BizDeviceTree bizDeviceTree) {
        return bizDeviceTreeMapper.selectDeviceTreeList(bizDeviceTree);
    }

    @Override
    public void insertDeviceTree(BizDeviceTree bizDeviceTree) {

        Long parentId = bizDeviceTree.getParentId();
        if(parentId != null){
            BizDeviceTree parent = getById(parentId);
            if(parent == null) {
                throw new ServiceException("上级设备树查询为空！", HttpStatus.ERROR);
            }
        }
        if(parentId != null){//0部门 | 1toolgroup

            LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizDeviceTree::getNodeType, 1);
            queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
            List<BizDeviceTree> list = this.list(queryWrapper);
            if (list.stream().filter(b -> b.getDeviceName().equals(bizDeviceTree.getDeviceName())).findAny().orElse(null) != null) {
                throw new ServiceException("设备树名已存在！", HttpStatus.ERROR);
            }

            bizDeviceTree.setNodeType(1);
        }else {

            LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizDeviceTree::getNodeType, 0);
            queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
            List<BizDeviceTree> list = this.list(queryWrapper);
            if (list.stream().filter(b -> b.getDeviceName().equals(bizDeviceTree.getDeviceName())).findAny().orElse(null) != null) {
                throw new ServiceException("设备树名已存在！", HttpStatus.ERROR);
            }

            bizDeviceTree.setNodeType(0);
            bizDeviceTree.setParentId(new Long(0));
        }
        bizDeviceTree.setStatus("0");
        bizDeviceTree.setDelFlag("0");
        bizDeviceTree.setCreateTime(DateUtils.getNowDate());
        bizDeviceTree.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(bizDeviceTree);
    }

    @Override
    public void updateDeviceTree(BizDeviceTree bizDeviceTree) {

        if (bizDeviceTree == null) {
            throw new ServiceException("设备树为空！", HttpStatus.ERROR);
        }

        BizDeviceTree bizDeviceTreeEntity = this.getById(bizDeviceTree.getId());
        if (bizDeviceTreeEntity == null) {
            throw new ServiceException("设备树id为空！", HttpStatus.ERROR);
        }

        Long parentId = bizDeviceTree.getParentId();
        if(parentId != null){//0部门 | 1toolgroup

            LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizDeviceTree::getNodeType, 1);
            queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
            List<BizDeviceTree> list = this.list(queryWrapper);
            if (list.stream().filter(b -> b.getDeviceName().equals(bizDeviceTree.getDeviceName()) && b.getId().intValue() != bizDeviceTree.getId().intValue()).findAny().orElse(null) != null) {
                throw new ServiceException("设备树名已存在！", HttpStatus.ERROR);
            }

        }else {

            LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizDeviceTree::getNodeType, 0);
            queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
            List<BizDeviceTree> list = this.list(queryWrapper);
            if (list.stream().filter(b -> b.getDeviceName().equals(bizDeviceTree.getDeviceName()) && b.getId().intValue() != bizDeviceTree.getId().intValue()).findAny().orElse(null) != null) {
                throw new ServiceException("设备树名已存在！", HttpStatus.ERROR);
            }

        }

        this.lambdaUpdate()
                .set(BizDeviceTree::getDeviceName, bizDeviceTree.getDeviceName())
                .set(BizDeviceTree::getSeq, bizDeviceTree.getSeq())
                .set(BizDeviceTree::getStatus, bizDeviceTree.getStatus())
                .set(BizDeviceTree::getUpdateTime, DateUtils.getNowDate())
                .set(BizDeviceTree::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(BizDeviceTree::getId, bizDeviceTree.getId())
                .update();

    }

    @Override
    public void deleteDeviceTreeById(Long id) {

        BizDeviceTree bizDeviceTree = this.getById(id);
        if (bizDeviceTree == null) {
            throw new ServiceException("设备树id为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizDeviceTree::getParentId, bizDeviceTree.getId());
        queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
        List<BizDeviceTree> list = this.list(queryWrapper);

        if(list != null && list.size() > 0){
            throw new ServiceException("存在子节点不能删除！", HttpStatus.ERROR);
        }


        this.lambdaUpdate()
                .set(BizDeviceTree::getDelFlag, "2")
                .set(BizDeviceTree::getUpdateTime, DateUtils.getNowDate())
                .set(BizDeviceTree::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(BizDeviceTree::getId, id)
                .update();
    }

    @Override
    public void importFile(MultipartFile file) {

        if (file != null) {

            ExcelUtil util = new ExcelUtil<>(ArrayList.class);
            try {
                List<String> sheetNames = util.getSheetNames(file.getInputStream());

                List<List<String>> deviceTreeDataList = util.getSheetData(sheetNames.get(0), file.getInputStream(), 1, 3);//获取每个sheet的数据


                List<BizDeviceTree> parentList = new ArrayList<>();
                List<BizDeviceTree> childrenList = new ArrayList<>();

                if (deviceTreeDataList.size() > 0) {

                    List<String> parentNameList = new ArrayList<>();
                    List<String> parentNameSet = new ArrayList<>();


                    for (int i = 0; i < deviceTreeDataList.size(); i++) {
                        String name = deviceTreeDataList.get(i).get(0);
                        String parentName = deviceTreeDataList.get(i).get(1);
                        if (parentName == null || parentName.equals("")) {//属于根级
                            parentNameList.add(name);
                            parentNameSet.add(name);
                        }
                    }
                    if(parentNameList.size() != parentNameSet.size()){
                        throw new ServiceException("父级存在重复，请检查后重新传！", HttpStatus.ERROR);
                    }

                    for (int i = 0; i < deviceTreeDataList.size(); i++) {
                        String parentName = deviceTreeDataList.get(i).get(1);
                        if (parentName != null && !parentName.equals("")) {//

                            if (!parentNameList.contains(parentName)) {//如果父级在本excle里没有，去数据库里验证是否有

                                LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
                                queryWrapper.eq(BizDeviceTree::getDeviceName, parentName);
                                queryWrapper.eq(BizDeviceTree::getNodeType, 0);
                                queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
                                List<BizDeviceTree> deviceTreeList = this.list(queryWrapper);
                                if (deviceTreeList == null || deviceTreeList.size() < 1) {
                                    throw new ServiceException("第" + (i + 2) + "行父级未查询到或者设备树层级错误，请检查后重新传！", HttpStatus.ERROR);
                                }
                            }

                        }
                    }


                    for (int i = 0; i < deviceTreeDataList.size(); i++) {


                        String name = deviceTreeDataList.get(i).get(0);
                        String parentName = deviceTreeDataList.get(i).get(1);
                        String orderNum = deviceTreeDataList.get(i).get(2);

                        if (name == null || name.equals("")) {
                            throw new ServiceException("第" + (i + 2) + "行名称不能为空，请检查后重新传！", HttpStatus.ERROR);

                        }

                        if (orderNum != null && !orderNum.equals("") && !Pattern.matches("^[0-9]*$", orderNum)) {

                            throw new ServiceException("第" + (i + 2) + "行排序格式不正常，请检查后重新传！", HttpStatus.ERROR);

                        }


                        BizDeviceTree dt = new BizDeviceTree();
                        dt.setDeviceName(name);
                        if (parentName == null || parentName.equals("")) {//属于根级
                            dt.setParentId(new Long(0));
                        } else {
                            dt.setParentName(parentName);
                        }
                        if (orderNum != null && !orderNum.equals("")) {
                            dt.setSeq(Integer.parseInt(orderNum));
                        }

                        if (parentName == null || parentName.equals("")) {
                            parentList.add(dt);
                        } else {
                            childrenList.add(dt);
                        }

                    }

                }

                for (BizDeviceTree dt : parentList) {


                    LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BizDeviceTree::getDeviceName, dt.getDeviceName());
                    queryWrapper.eq(BizDeviceTree::getNodeType, 0);
                    queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
                    List<BizDeviceTree> deviceTreeList = this.list(queryWrapper);

                    if (deviceTreeList == null || deviceTreeList.size() < 1) {//如果这个节点在库里不存在，则新增，如果存在不做处理

                        dt.setNodeType(0);
                        dt.setParentId(new Long(0));
                        dt.setCreateTime(DateUtils.getNowDate());
                        dt.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
                        dt.setDelFlag("0");
                        dt.setStatus("0");
                        this.save(dt);

                    }

                }

                for (BizDeviceTree dt : childrenList) {

                    dt.setNodeType(1);
                    dt.setCreateTime(DateUtils.getNowDate());
                    dt.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
                    dt.setDelFlag("0");
                    dt.setStatus("0");



                    LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BizDeviceTree::getDeviceName, dt.getParentName());
                    queryWrapper.eq(BizDeviceTree::getNodeType, 0);
                    queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
                    List<BizDeviceTree> deviceTreeList = this.list(queryWrapper);

                    if (deviceTreeList != null && deviceTreeList.size() > 0) {//先查询当前孩子节点的父节点
                        dt.setParentId(deviceTreeList.get(0).getId());

                        //在验证一下当前孩子节点，是否已经在数据库里存在了

                        LambdaQueryWrapper<BizDeviceTree> queryWrapper1 = new LambdaQueryWrapper<>();
                        queryWrapper1.eq(BizDeviceTree::getDeviceName, dt.getDeviceName());
                        queryWrapper1.eq(BizDeviceTree::getParentId, dt.getParentId());
                        queryWrapper1.eq(BizDeviceTree::getNodeType, 1);
                        queryWrapper1.eq(BizDeviceTree::getDelFlag, "0");
                        List<BizDeviceTree> deviceTreeList1 = this.list(queryWrapper1);

                        if (deviceTreeList1 == null || deviceTreeList1.size() < 1) {

                            this.save(dt);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e.getMessage(), HttpStatus.ERROR);
            }
        } else {
            throw new ServiceException("请选择上传文件", HttpStatus.ERROR);
        }
    }
}




