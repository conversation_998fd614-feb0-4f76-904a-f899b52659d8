package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.BizPointIdenConf;
import com.honichang.point.service.BizPointIdenConfService;
import com.honichang.point.mapper.BizPointIdenConfMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *
 */
@Service
public class BizPointIdenConfServiceImpl extends ServiceImpl<BizPointIdenConfMapper, BizPointIdenConf>
    implements BizPointIdenConfService{

    @Resource
    private BizPointIdenConfMapper bizPointIdenConfMapper;

    @Override
    public List<BizPointIdenConf> getListByPointId(Long bizPointId) {
        return bizPointIdenConfMapper.selectList(new LambdaQueryWrapper<BizPointIdenConf>()
                .eq(BizPointIdenConf::getPointId, bizPointId)
                .eq(BizPointIdenConf::getDelFlag, "0"));
    }
}




