package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.core.redis.RedisCache;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.StringUtils;
import com.honichang.common.utils.bean.BeanValidators;
import com.honichang.dispactch.model.Point;
import com.honichang.point.domain.BizDeviceTree;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.BizPointAiParam;
import com.honichang.point.service.BizDeviceTreeService;
import com.honichang.point.service.BizPointAiParamService;
import com.honichang.point.service.BizPointService;
import com.honichang.point.mapper.BizPointMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 *
 */
@Service
public class BizPointServiceImpl extends ServiceImpl<BizPointMapper, BizPoint>
        implements BizPointService {

    @Resource
    private BizPointMapper bizPointMapper;
    @Resource
    private BizDeviceTreeService bizDeviceTreeService;
    @Resource
    private BizPointAiParamService bizPointAiParamService;

    @Resource
    private RedisCache redisCache;


    // TODO: 在插入、更新、删除bizPoint时，都需要清空这个key的redis缓存
    @NonNull
    public List<BizPoint> getByXmapId(String xmapId, Long mapId) {
        String key = "bizPoint_xmap_" + xmapId + "_map_" + mapId;
        if (redisCache.hasKey(key)) {
            return redisCache.getCacheObject(key);
        }

        List<BizPoint> points = bizPointMapper.selectByXmapId(xmapId, mapId);
        redisCache.setCacheObject(key, points);
        return points;
    }

    @Override
    public String importBizPoint(List<BizPoint> bizPointList, boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(bizPointList) || bizPointList.size() == 0) {
            throw new ServiceException("导入点位数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BizPoint bizPoint : bizPointList) {
            try {

                if(bizPoint.getClassName() == null || bizPoint.getClassName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位类型不能为空";
                    failureMsg.append(msg);
                    continue;
                }
                if(bizPoint.getInstanceName() == null || bizPoint.getInstanceName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位名称不能为空";
                    failureMsg.append(msg);
                    continue;
                }
                if(bizPoint.getDeptName() == null || bizPoint.getDeptName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 所属部门不能为空";
                    failureMsg.append(msg);
                    continue;
                }
                if(bizPoint.getXmapId() == null || bizPoint.getXmapId().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位ID不能为空";
                    failureMsg.append(msg);
                    continue;
                }

                LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BizDeviceTree::getDeviceName, bizPoint.getDeptName());
                queryWrapper.eq(BizDeviceTree::getParentId, 0);
                List<BizDeviceTree> treeList = bizDeviceTreeService.list(queryWrapper);
                if (treeList.size() == 0) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到部门";
                    failureMsg.append(msg);
                    continue;
                }
                bizPoint.setDeptId(treeList.get(0).getId());

                if (bizPoint.getToolGroupName() != null && !bizPoint.getToolGroupName().equals("")) {
                    LambdaQueryWrapper<BizDeviceTree> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.eq(BizDeviceTree::getParentId, bizPoint.getDeptId());
                    queryWrapper1.eq(BizDeviceTree::getDeviceName, bizPoint.getToolGroupName());
                    List<BizDeviceTree> treeList1 = bizDeviceTreeService.list(queryWrapper1);
                    if (treeList1.size() == 0) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到toolGroup";
                        failureMsg.append(msg);
                        continue;
                    }
                    bizPoint.setToolGroupId(treeList1.get(0).getId());
                }

                // 验证是否存在
                LambdaQueryWrapper<BizPoint> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BizPoint::getInstanceName, bizPoint.getInstanceName());
                BizPoint u = bizPointMapper.selectOne(wrapper);


                if (StringUtils.isNull(u)) {
                    bizPoint.setEditStatus("0");
                    bizPointMapper.insert(bizPoint);
                    BizPointAiParam bizPointAiParam = new BizPointAiParam();
                    bizPointAiParam.setBizPointId(bizPoint.getId());
                    bizPointAiParam.setParamKey("");
                    bizPointAiParamService.save(bizPointAiParam);

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、点位 " + bizPoint.getInstanceName() + " 导入成功");
                } else if (isUpdateSupport) {
                    bizPointMapper.updateById(bizPoint);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、点位 " + bizPoint.getInstanceName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    public List<BizPoint> getBizPointList(BizPoint bizPoint) {
        return bizPointMapper.getBizPointList(bizPoint);
    }

    @Override
    @Transactional
    public boolean add(BizPoint bizPoint) {
        bizPoint.setEditStatus("1");
        boolean save = save(bizPoint);
        BizPointAiParam bizPointAiParam = new BizPointAiParam();
        bizPointAiParam.setBizPointId(bizPoint.getId());
        bizPointAiParam.setParamKey("");
        bizPointAiParamService.save(bizPointAiParam);
        return save;
    }

    @Override
    @Transactional
    public boolean edit(BizPoint bizPoint) {
        bizPoint.setEditStatus("1");
        LambdaQueryWrapper<BizPointAiParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPointAiParam::getBizPointId, bizPoint.getId());
        bizPointAiParamService.remove(wrapper);
        BizPointAiParam bizPointAiParam = new BizPointAiParam();
        bizPointAiParam.setBizPointId(bizPoint.getId());
        bizPointAiParam.setParamKey("");
        bizPointAiParamService.save(bizPointAiParam);
        return updateById(bizPoint);
    }

    @Override
    public List<BizPoint> getPointsByTaskDef(long taskDefId) {
        return bizPointMapper.getPointsByTaskDef(taskDefId);
    }
}




