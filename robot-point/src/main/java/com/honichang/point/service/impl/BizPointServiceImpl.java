package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.common.utils.bean.BeanValidators;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.service.BizPointService;
import com.honichang.point.mapper.BizPointMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class BizPointServiceImpl extends ServiceImpl<BizPointMapper, BizPoint>
        implements BizPointService {

    @Resource
    private BizPointMapper bizPointMapper;

    @Override
    public String importBizPoint(List<BizPoint> bizPointList, boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(bizPointList) || bizPointList.size() == 0) {
            throw new ServiceException("导入点位数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BizPoint bizPoint : bizPointList) {
            try {
                // 验证是否存在
                LambdaQueryWrapper<BizPoint> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BizPoint::getInstanceName, bizPoint.getInstanceName());
                BizPoint u = bizPointMapper.selectOne(wrapper);
                if (StringUtils.isNull(u)) {
                    bizPointMapper.insert(bizPoint);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + bizPoint.getInstanceName() + " 导入成功");
                } else if (isUpdateSupport) {
                    bizPointMapper.updateById(bizPoint);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + bizPoint.getInstanceName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + bizPoint.getInstanceName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + bizPoint.getInstanceName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}




