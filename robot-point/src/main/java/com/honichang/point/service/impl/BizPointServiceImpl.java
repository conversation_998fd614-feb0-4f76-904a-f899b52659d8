package com.honichang.point.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.honichang.common.annotation.DataScope;
import com.honichang.common.annotation.DataSource;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.redis.RedisCache;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.StringUtils;
import com.honichang.common.utils.bean.BeanValidators;
import com.honichang.common.utils.http.HttpUtils;
import com.honichang.dispactch.model.Point;
import com.honichang.machine.domain.CaDto;
import com.honichang.point.domain.*;
import com.honichang.point.model.dto.AIRecognitionParam;
import com.honichang.point.service.*;
import com.honichang.point.mapper.BizPointMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class BizPointServiceImpl extends ServiceImpl<BizPointMapper, BizPoint>
        implements BizPointService {

    @Resource
    private BizPointMapper bizPointMapper;
    @Resource
    private BizDeviceTreeService bizDeviceTreeService;
    @Resource
    private BizPointAiParamService bizPointAiParamService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private BizPointIdenConfService bizPointIdenConfService;
    @Resource
    private IdenModelParamService idenModelParamService;
    @Resource
    private IdenModelService idenModelService;
    @Resource
    private IdenRuleService idenRuleService;
    @Resource
    private IdenTypeService idenTypeService;
    @Resource
    private IdenTypeParamService idenTypeParamService;


    @Value("${honichang.AIaddress}")
    private String AIaddress;


    // TODO: 在插入、更新、删除bizPoint时，都需要清空这个key的redis缓存
    @NonNull
    public List<BizPoint> getByXmapId(String xmapId, Long mapId) {
        String key = "bizPoint_xmap_" + xmapId + "_map_" + mapId;
        if (redisCache.hasKey(key)) {
            return redisCache.getCacheObject(key);
        }

        List<BizPoint> points = bizPointMapper.selectByXmapId(xmapId, mapId);
        redisCache.setCacheObject(key, points);
        return points;
    }

    @Override
    public String importBizPoint(List<BizPoint> bizPointList, boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(bizPointList) || bizPointList.size() == 0) {
            throw new ServiceException("导入点位数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BizPoint bizPoint : bizPointList) {
            try {

                if (bizPoint.getClassName() == null || bizPoint.getClassName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位类型不能为空";
                    failureMsg.append(msg);
                    continue;
                } else {
                    if (bizPoint.getClassName().equals("IdenPoint") && (bizPoint.getIdenModelName() == null || bizPoint.getIdenModelName().equals(""))) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 类型为识别时点位模型不可为空";
                        failureMsg.append(msg);
                        continue;
                    }
                    if (bizPoint.getClassName().equals("IdenPoint") || bizPoint.getClassName().equals("InfraredDetection")) {
                        if (bizPoint.getIdenRuleName() == null || bizPoint.getIdenRuleName().equals("")) {
                            failureNum++;
                            String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 类型为识别或红外测温时规则状态不可为空";
                            failureMsg.append(msg);
                            continue;
                        }
                    }
                    if (bizPoint.getClassName().equals("IdenPoint") || bizPoint.getClassName().equals("InfraredDetection")) {
                        if (bizPoint.getToolGroupName() == null || bizPoint.getToolGroupName().equals("")) {
                            failureNum++;
                            String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 类型为识别或红外测温时toolGroup不可为空";
                            failureMsg.append(msg);
                            continue;
                        }
                    }

                }
                if (bizPoint.getInstanceName() == null || bizPoint.getInstanceName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位名称不能为空";
                    failureMsg.append(msg);
                    continue;
                }
                if (bizPoint.getDeptName() == null || bizPoint.getDeptName().equals("")) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 所属部门不能为空";
                    failureMsg.append(msg);
                    continue;
                }
//                if(bizPoint.getXmapId() == null || bizPoint.getXmapId().equals("")) {
//                    failureNum++;
//                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 点位ID不能为空";
//                    failureMsg.append(msg);
//                    continue;
//                }


                LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BizDeviceTree::getDeviceName, bizPoint.getDeptName());
                queryWrapper.eq(BizDeviceTree::getParentId, 0);
                List<BizDeviceTree> treeList = bizDeviceTreeService.list(queryWrapper);
                if (treeList.size() == 0) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到部门";
                    failureMsg.append(msg);
                    continue;
                }
                bizPoint.setDeptId(treeList.get(0).getId());

                if (bizPoint.getToolGroupName() != null && !bizPoint.getToolGroupName().equals("")) {
                    LambdaQueryWrapper<BizDeviceTree> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.eq(BizDeviceTree::getParentId, bizPoint.getDeptId());
                    queryWrapper1.eq(BizDeviceTree::getDeviceName, bizPoint.getToolGroupName());
                    List<BizDeviceTree> treeList1 = bizDeviceTreeService.list(queryWrapper1);
                    if (treeList1.size() == 0) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到toolGroup";
                        failureMsg.append(msg);
                        continue;
                    }
                    bizPoint.setToolGroupId(treeList1.get(0).getId());
                }

                if (bizPoint.getIdenModelName() != null && !bizPoint.getIdenModelName().equals("")) {
                    LambdaQueryWrapper<IdenModel> imw = new LambdaQueryWrapper<>();
                    imw.eq(IdenModel::getIdenModelName, bizPoint.getIdenModelName());
                    imw.eq(IdenModel::getDelFlag, "0");
                    List<IdenModel> idenModelList = idenModelService.list(imw);
                    if (idenModelList.size() == 0) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到点位模型";
                        failureMsg.append(msg);
                        continue;
                    }
                    bizPoint.setIdenModelId(idenModelList.get(0).getId());
                }

                if (bizPoint.getIdenRuleName() != null && !bizPoint.getIdenRuleName().equals("")) {
                    LambdaQueryWrapper<IdenRule> wrapper = new LambdaQueryWrapper<>();
                    if (bizPoint.getIdenModelId() != null) {
                        wrapper.eq(IdenRule::getIdenModelId, bizPoint.getIdenModelId());
                    }
                    wrapper.eq(IdenRule::getIdenRuleName, bizPoint.getIdenRuleName());
                    wrapper.eq(IdenRule::getDelFlag, "0");
                    List<IdenRule> ruleList = idenRuleService.list(wrapper);
                    if (ruleList.size() == 0) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 未匹配到规则状态（检查规则是否为模型所属）";
                        failureMsg.append(msg);
                        continue;
                    }
                    bizPoint.setIdenRuleId(ruleList.get(0).getId());
                }

                // 验证是否存在
                LambdaQueryWrapper<BizPoint> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BizPoint::getInstanceName, bizPoint.getInstanceName());
                wrapper.eq(BizPoint::getDelFlag, "0");
                BizPoint u = bizPointMapper.selectOne(wrapper);


                if (StringUtils.isNull(u)) {
                    bizPoint.setEditStatus("0");
                    bizPointMapper.insert(bizPoint);
                    BizPointAiParam bizPointAiParam = new BizPointAiParam();
                    bizPointAiParam.setBizPointId(bizPoint.getId());
                    bizPointAiParam.setParamKey("");
                    bizPointAiParamService.save(bizPointAiParam);

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、点位 " + bizPoint.getInstanceName() + " 导入成功");
                } else if (isUpdateSupport) {
                    bizPointMapper.updateById(bizPoint);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、点位 " + bizPoint.getInstanceName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、点位 " + bizPoint.getInstanceName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @DataScope(deviceAlias = "d")
    public List<BizPoint> getBizPointList(BizPoint bizPoint) {
        return bizPointMapper.getBizPointList(bizPoint);
    }

    @Override
    @Transactional
    public boolean add(BizPoint bizPoint) {
        bizPoint.setEditStatus("1");
        boolean save = save(bizPoint);

        BizPointAiParam bizPointAiParam = new BizPointAiParam();
        bizPointAiParam.setBizPointId(bizPoint.getId());
        bizPointAiParam.setParamKey("");
        bizPointAiParamService.save(bizPointAiParam);

        List<IdenModelParam> modelParamList = null;
        if (bizPoint.getIdenModelId() != null) {
            LambdaQueryWrapper<IdenModelParam> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
            modelParamList = idenModelParamService.list(wrapper);
        }
        if (bizPoint.getBizPointIdenConfList() != null && bizPoint.getBizPointIdenConfList().size() > 0) {
            for (BizPointIdenConf bizPointIdenConf : bizPoint.getBizPointIdenConfList()) {
                IdenModelParam idenModelParam = null;
                try {
                    idenModelParam = modelParamList.stream().filter(f -> f.getParamName().equals(bizPointIdenConf.getIdenName())).findAny().get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                bizPointIdenConf.setPointId(bizPoint.getId());
                bizPointIdenConf.setModelParamId(idenModelParam != null ? idenModelParam.getId() : 0);
            }
            bizPointIdenConfService.saveBatch(bizPoint.getBizPointIdenConfList());
        }

        if (bizPoint.getLastIdenConfInfo() != null && !bizPoint.getLastIdenConfInfo().equals("")) {
            IdenModel idenModel = idenModelService.getById(bizPoint.getIdenModelId());
            idenModel.setLastIdenConfInfo(bizPoint.getLastIdenConfInfo());
            idenModelService.updateById(idenModel);
        }
        return save;
    }

    @Override
    @Transactional
    public boolean edit(BizPoint bizPoint) {
        bizPoint.setEditStatus("1");

        List<IdenModelParam> modelParamList = null;
        if (bizPoint.getIdenModelId() != null) {
            LambdaQueryWrapper<IdenModelParam> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(IdenModelParam::getIdenModelId, bizPoint.getIdenModelId());
            modelParamList = idenModelParamService.list(wrapper);
        }
        if (bizPoint.getBizPointIdenConfList() != null && bizPoint.getBizPointIdenConfList().size() > 0) {
            LambdaQueryWrapper<BizPointIdenConf> w2 = new LambdaQueryWrapper<>();
            w2.eq(BizPointIdenConf::getPointId, bizPoint.getId());
            bizPointIdenConfService.remove(w2);

            for (BizPointIdenConf bizPointIdenConf : bizPoint.getBizPointIdenConfList()) {
                IdenModelParam idenModelParam = null;
                try {
                    idenModelParam = modelParamList.stream().filter(f -> f.getParamName().equals(bizPointIdenConf.getIdenName())).findAny().get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                bizPointIdenConf.setPointId(bizPoint.getId());
                bizPointIdenConf.setModelParamId(idenModelParam != null ? idenModelParam.getId() : 0);
            }
            bizPointIdenConfService.saveBatch(bizPoint.getBizPointIdenConfList());
        }

        if (bizPoint.getLastIdenConfInfo() != null && !bizPoint.getLastIdenConfInfo().equals("")) {
            IdenModel idenModel = idenModelService.getById(bizPoint.getIdenModelId());
            idenModel.setLastIdenConfInfo(bizPoint.getLastIdenConfInfo());
            idenModelService.updateById(idenModel);
        }

        return updateById(bizPoint);
    }

    @Override
    public List<BizPoint> getPointsByTaskDef(long taskDefId) {
        return bizPointMapper.getPointsByTaskDef(taskDefId);
    }


    @DataScope(deviceTree = "d")
    @Override
    public List<BizPoint> getPoints(CaDto dto) {
        return bizPointMapper.getPoints(dto);
    }

    @Override
    public List<BizPoint> getList(BizPoint bizPoint) {
        return bizPointMapper.getList(bizPoint);
    }

    @Override
    public BizPoint getOne(Long id) {
        return bizPointMapper.getOne(id);
    }

    @Override
    public JSONObject AIRecognition(AIRecognitionParam aiRecognitionParam) {
        HashMap<String, Object> map = new HashMap<>();

        String base64Str = "";
        if (aiRecognitionParam.getPhotoBase64Str() != null && !aiRecognitionParam.getPhotoBase64Str().equals("")) {
            base64Str = aiRecognitionParam.getPhotoBase64Str();
        } else {
            byte[] fileContent = new byte[0];
            try {
                fileContent = Files.readAllBytes(Paths.get(RuoYiConfig.getProfile() + aiRecognitionParam.getPhotoUrl().replace("/profile", "")));
            } catch (IOException e) {
                e.printStackTrace();
            }
            Base64.Encoder encoder = Base64.getEncoder();
            base64Str = encoder.encodeToString(fileContent);
        }
        map.put("picture", base64Str);

        String base64Str2 = "";
        if (aiRecognitionParam.getPhotoOriginalBase64Str() != null && !aiRecognitionParam.getPhotoOriginalBase64Str().equals("")) {
            base64Str2 = aiRecognitionParam.getPhotoOriginalBase64Str();
        } else {
            byte[] fileContent2 = new byte[0];
            try {
                fileContent2 = Files.readAllBytes(Paths.get(RuoYiConfig.getProfile() + aiRecognitionParam.getPhotoOriginalUrl().replace("/profile", "")));
            } catch (IOException e) {
                e.printStackTrace();
            }
            Base64.Encoder encoder2 = Base64.getEncoder();
            base64Str2 = encoder2.encodeToString(fileContent2);
        }
        map.put("picture", base64Str2);

        IdenModelParam idenModelParam = new IdenModelParam();
        idenModelParam.setIdenModelId(Long.valueOf(aiRecognitionParam.getIdenModelId()));
        List<IdenModelParam> modelParams = idenModelParamService.getList(idenModelParam);

        ArrayList<Map<String, Object>> marks = new ArrayList<>();

        ArrayList<Map<String, Object>> debugs = new ArrayList<>();

        if (aiRecognitionParam.getBizPointIdenConfList() != null) {
            for (BizPointIdenConf bizPointIdenConf : aiRecognitionParam.getBizPointIdenConfList()) {

                IdenModelParam imp = modelParams.stream().filter(f -> f.getIdenTypeName().equals(bizPointIdenConf.getIdenName())).findAny().get();
                IdenType idenType = imp.getIdenType();

                int typeStatusCode = 0;
                if (idenType.getIdenTypeName().contains("双圈表")) {
                    typeStatusCode = 1;
                } else if (idenType.getIdenTypeName().contains("四行电子表")) {
                    typeStatusCode = 2;
                } else if (idenType.getIdenTypeName().contains("两行电子表")) {
                    typeStatusCode = 3;
                } else if (idenType.getIdenTypeName().contains("灯塔")) {
                    typeStatusCode = 4;
                } else if (idenType.getIdenTypeName().contains("流量计")) {
                    typeStatusCode = 5;
                } else if (idenType.getIdenTypeName().contains("异常指示灯")) {
                    typeStatusCode = 6;
                } else if (idenType.getIdenTypeName().contains("开关")) {
                    typeStatusCode = 7;
                }

                HashMap<String, Object> mark = new HashMap<>();
                mark.put("iden_name", bizPointIdenConf.getIdenName());
                mark.put("iden_type", typeStatusCode);
                mark.put("min_pos_x", bizPointIdenConf.getMinPosX());
                mark.put("min_pos_y", bizPointIdenConf.getMinPosY());
                mark.put("max_pos_x", bizPointIdenConf.getMaxPosX());
                mark.put("max_pos_y", bizPointIdenConf.getMaxPosY());
                if (bizPointIdenConf.getBeginScalarX() != null) {
                    mark.put("begin_scalar_x", bizPointIdenConf.getBeginScalarX());
                    mark.put("begin_scalar_y", bizPointIdenConf.getBeginScalarY());
                }
                if (bizPointIdenConf.getEndScalarX() != null) {
                    mark.put("end_scalar_x", bizPointIdenConf.getEndScalarX());
                    mark.put("end_scalar_y", bizPointIdenConf.getEndScalarY());
                }
                if (bizPointIdenConf.getLineStartX() != null) {
                    mark.put("line_start_x", bizPointIdenConf.getLineStartX());
                    mark.put("line_start_y", bizPointIdenConf.getLineStartY());
                    mark.put("line_end_x", bizPointIdenConf.getLineEndX());
                    mark.put("line_end_y", bizPointIdenConf.getLineEndY());
                }

                ArrayList<Map<String, Object>> params = new ArrayList<>();
                List<IdenModelParam> idenModelParamList = modelParams.stream().filter(f -> f.getIdenTypeName().equals(bizPointIdenConf.getIdenName())).collect(Collectors.toList());
                for (IdenModelParam modelParam : idenModelParamList) {
                    HashMap<String, Object> param = new HashMap<>();
                    param.put("key", modelParam.getIdenName());
                    param.put("index", modelParam.getSeq() != null ? modelParam.getSeq() : 0);
                    param.put("data_type", modelParam.getDataType().equals("1") ? "Float" : "String");
                    param.put("min_limit", modelParam.getMinLimit());
                    param.put("max_limit", modelParam.getMaxLimit());
                    params.add(param);
                }
                mark.put("params", params);
                marks.add(mark);
            }
        }

        if (aiRecognitionParam.getBizPointAiParamList() != null) {
            for (BizPointAiParam bizPointAiParam : aiRecognitionParam.getBizPointAiParamList()) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put(bizPointAiParam.getParamKey(), bizPointAiParam.getParamValue());
                debugs.add(hashMap);
            }
        }

        map.put("marks", marks);
        map.put("debugs", debugs);

        ObjectMapper objectMapper = new ObjectMapper();
        String json = null;
        try {
            json = objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        String sendPost = HttpUtils.sendPost(AIaddress, json);

        return JSONObject.parseObject(sendPost);
    }
}




