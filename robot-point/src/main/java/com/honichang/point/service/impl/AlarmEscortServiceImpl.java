package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.AlarmAi;
import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.service.AlarmEscortService;
import com.honichang.point.mapper.AlarmEscortMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class AlarmEscortServiceImpl extends ServiceImpl<AlarmEscortMapper, AlarmEscort>
    implements AlarmEscortService{

    @Autowired
    private AlarmEscortMapper alarmEscortMapper;

    @Override
    public List<AlarmEscort> selectPageList(AlarmEscort alarmEscort) {
        return alarmEscortMapper.selectPageList(alarmEscort);
    }
}




