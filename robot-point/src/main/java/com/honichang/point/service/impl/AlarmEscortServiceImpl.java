package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum;
import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.domain.TaskEscort;
import com.honichang.point.mapper.AlarmEscortMapper;
import com.honichang.point.service.AlarmEscortService;
import com.honichang.push.service.PushMessageService;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum.*;

/**
 *
 */
@Service
public class AlarmEscortServiceImpl extends ServiceImpl<AlarmEscortMapper, AlarmEscort>
    implements AlarmEscortService{

    @Resource
    private AlarmEscortMapper alarmEscortMapper;

    @Resource
    private PushMessageService pushMessageService;

    @Override
    public List<AlarmEscort> selectPageList(AlarmEscort alarmEscort) {
        return alarmEscortMapper.selectPageList(alarmEscort);
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void add(@NonNull TaskEscort taskEscort, @NonNull String reason, TaskEscortPushTemplateEnum pushTemplate) {
        if (pushTemplate == ESCORT_TEMP_COMPLETED) {
            return;
        }

        int alarmType = 9; // 其他
        if (pushTemplate == ESCORT_TEMP_WAIT_TIMEOUT) {
            alarmType = 1; // 等待超时
        } else if (pushTemplate == ESCORT_TEMP_LEAVE) {
            alarmType = 2; // 提前离开
        }

        AlarmEscort ae = new AlarmEscort();
        ae.setAlarmType(alarmType);
        ae.setAlarmContent(reason);
        ae.setRobotId(taskEscort.getRobotId());
        ae.setTaskEscortId(taskEscort.getId());
        ae.setTaskEscort(taskEscort);
        ae.setCreateBy(SecurityUtils.getUserIdOrSystem());
        ae.setCreateTime(new Date());
        ae.setUpdateBy(ae.getCreateBy());
        ae.setUpdateTime(ae.getCreateTime());
        ae.setDelFlag("0");
        ae.setStatus("0");
        save(ae);

        try {
            pushMessageService.sendAlarmEscortMessage(ae);
        } catch (Exception e) {
            // ignore
        }
    }
}




