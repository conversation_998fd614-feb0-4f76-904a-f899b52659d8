package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.BizPointIdenConf;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.domain.IdenModel;
import com.honichang.point.domain.IdenModelParamGroup;
import com.honichang.point.service.IdenModelParamGroupService;
import com.honichang.point.service.IdenModelParamService;
import com.honichang.point.mapper.IdenModelParamMapper;
import com.honichang.point.service.IdenModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 *
 */
@Service
public class IdenModelParamServiceImpl extends ServiceImpl<IdenModelParamMapper, IdenModelParam>
    implements IdenModelParamService{


    @Autowired
    private IdenModelParamMapper idenModelParamMapper;

    @Autowired
    private IdenModelService idenModelService;

    @Autowired
    private IdenModelParamGroupService idenModelParamGroupService;

    @Override
    public List<IdenModelParam> selectParamList(IdenModelParam idenModelParam) {
        return idenModelParamMapper.selectParamList(idenModelParam);

    }

    @Override
    public void add(IdenModelParam idenModelParam) {

        IdenModel idenModel = idenModelService.getById(idenModelParam.getIdenModelId());
        if(idenModel == null) {
            throw new ServiceException("识别模型为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<IdenModelParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParam::getIdenModelId, idenModelParam.getIdenModelId());
        queryWrapper.eq(IdenModelParam::getDelFlag, "0");
        List<IdenModelParam> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getParamName().equals(idenModelParam.getParamName())).findAny().orElse(null) != null) {
            throw new ServiceException("参数名已存在！", HttpStatus.ERROR);
        }

        idenModelParam.setStatus("0");
        idenModelParam.setDelFlag("0");
        idenModelParam.setCreateTime(DateUtils.getNowDate());
        idenModelParam.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(idenModelParam);

    }

    @Override
    public void update(IdenModelParam idenModelParam) {

        if (idenModelParam == null) {
            throw new ServiceException("参数为空！", HttpStatus.ERROR);
        }

        IdenModelParam idenTypeParamEntity = this.getById(idenModelParam.getId());
        if (idenTypeParamEntity == null) {
            throw new ServiceException("参数id为空！", HttpStatus.ERROR);
        }

        IdenModel idenModel = idenModelService.getById(idenModelParam.getIdenModelId());
        if(idenModel == null) {
            throw new ServiceException("识别模型为空！", HttpStatus.ERROR);
        }



        LambdaQueryWrapper<IdenModelParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParam::getIdenModelId, idenModelParam.getIdenModelId());
        queryWrapper.eq(IdenModelParam::getDelFlag, "0");
        queryWrapper.isNotNull(IdenModelParam::getUpdateTime);
        List<IdenModelParam> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getParamName().equals(idenModelParam.getParamName()) && b.getId().intValue() != idenModelParam.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("参数名已存在！", HttpStatus.ERROR);
        }

        if (list.stream().filter(b -> b.getIdenTypeName().equals(idenModelParam.getIdenTypeName()) && b.getId().intValue() != idenModelParam.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("识别名称已存在！", HttpStatus.ERROR);
        }



        this.lambdaUpdate()
                .set(IdenModelParam::getParamName, idenModelParam.getParamName())
                .set(IdenModelParam::getIdenTypeName, idenModelParam.getIdenTypeName())
                .set(IdenModelParam::getSeq, idenModelParam.getSeq())
                .set(IdenModelParam::getUnit, idenModelParam.getUnit())
                .set(IdenModelParam::getOffsetValue, idenModelParam.getOffsetValue())
                .set(IdenModelParam::getPrecisionNum, idenModelParam.getPrecisionNum())
                .set(IdenModelParam::getScaling, idenModelParam.getScaling())
                .set(IdenModelParam::getMinLimit, idenModelParam.getMinLimit())
                .set(IdenModelParam::getMaxLimit, idenModelParam.getMaxLimit())
                .set(IdenModelParam::getRemark, idenModelParam.getRemark())
                .set(IdenModelParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParam::getId, idenModelParam.getId())
                .update();

    }

    @Override
    public void delete(Long id) {

        IdenModelParam idenTypeParamEntity = this.getById(id);
        if (idenTypeParamEntity == null) {
            throw new ServiceException("参数id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParam::getDelFlag, "2")
                .set(IdenModelParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParam::getId, id)
                .update();

    }

    @Override
    public void deleteByIdenModelId(Long idenModelId) {

        IdenModel idenModel = idenModelService.getById(idenModelId);
        if(idenModel == null) {
            throw new ServiceException("识别模型为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParam::getDelFlag, "2")
                .set(IdenModelParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParam::getIdenModelId, idenModelId)
                .update();
    }

    @Override
    public void deleteByParamGroupId(Long idenModelParamGroupId) {

        IdenModelParamGroup idenModelParamGroup = idenModelParamGroupService.getById(idenModelParamGroupId);
        if(idenModelParamGroup == null) {
            throw new ServiceException("识别模型参数组id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParam::getDelFlag, "2")
                .set(IdenModelParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParam::getParamGroupId, idenModelParamGroupId)
                .update();

    }

    @Override
    public void updateParamStatus(IdenModelParam idenModelParam) {

        if (idenModelParam == null) {
            throw new ServiceException("参数为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParam::getStatus, idenModelParam.getStatus()!=null && !idenModelParam.getStatus().equals("") ? idenModelParam.getStatus() : null)
                .set(IdenModelParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParam::getId, idenModelParam.getId())
                .update();
    }

    @Override
    public List<IdenModelParam> selectParamListByIdenConf(BizPointIdenConf conf) {
        IdenModelParam first = this.getById(conf.getModelParamId());
        if(first == null) {
            return Collections.emptyList();
        }

        if (first.getSeq() != null) {
            return this.lambdaQuery()
                    .eq(IdenModelParam::getIdenModelId, first.getIdenModelId())
                    .eq(IdenModelParam::getParamGroupId, first.getParamGroupId())
                    .eq(IdenModelParam::getDelFlag, "0")
                    .list();
        } else {
            return Collections.singletonList(first);
        }
    }

}




