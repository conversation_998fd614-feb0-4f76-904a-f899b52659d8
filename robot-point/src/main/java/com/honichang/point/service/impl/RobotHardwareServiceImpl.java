package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.RobotHardware;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.mapper.RobotHardwareMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class RobotHardwareServiceImpl extends ServiceImpl<RobotHardwareMapper, RobotHardware>
        implements RobotHardwareService {

    @Resource
    private RobotHardwareMapper robotHardwareMapper;

    // region 👇根据硬件ip和端口获取机器人id

    @Override
    @Nullable
    public RobotHardware getRobotIdByIpAndPort(String ip, String port) {
        List<RobotHardware> result = robotHardwareMapper.getHardwareByIpAndPort(ip, port);
        return result.isEmpty() ? null : result.get(0);
    }

    // endregion
}




