package com.honichang.point.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.IdenType;
import com.honichang.point.domain.IdenTypeParam;
import com.honichang.point.service.IdenTypeParamService;
import com.honichang.point.mapper.IdenTypeParamMapper;
import com.honichang.point.service.IdenTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class IdenTypeParamServiceImpl extends ServiceImpl<IdenTypeParamMapper, IdenTypeParam>
    implements IdenTypeParamService{

    @Autowired
    private IdenTypeParamMapper idenTypeParamMapper;

    @Autowired
    private IdenTypeService idenTypeService;

    @Override
    public List<IdenTypeParam> selectParamList(IdenTypeParam idenTypeParam) {
        return idenTypeParamMapper.selectParamList(idenTypeParam);

    }

    @Override
    public void add(IdenTypeParam idenTypeParam) {

        IdenType idenType = idenTypeService.getById(idenTypeParam.getIdenTypeId());
        if(idenType == null) {
            throw new ServiceException("识别类型为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<IdenTypeParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenTypeParam::getIdenTypeId, idenTypeParam.getIdenTypeId());
        queryWrapper.eq(IdenTypeParam::getDelFlag, "0");
        List<IdenTypeParam> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getParamName().equals(idenTypeParam.getParamName())).findAny().orElse(null) != null) {
            throw new ServiceException("参数名已存在！", HttpStatus.ERROR);
        }

        if(idenTypeParam.getDataType().equals("1")){//1-Float | 2-枚举

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("min", idenTypeParam.getMinValue());
            jsonObject.put("max", idenTypeParam.getMaxValue());
            idenTypeParam.setValueScopeJson(jsonObject.toJSONString());
        }
        if(idenTypeParam.getDataType().equals("2")){//1-Float | 2-枚举

            String[] str = idenTypeParam.getEnumValue().split(",");
            idenTypeParam.setValueScopeJson(JSONArray.toJSONString(str));
        }

        idenTypeParam.setStatus("0");
        idenTypeParam.setDelFlag("0");
        idenTypeParam.setCreateTime(DateUtils.getNowDate());
        idenTypeParam.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(idenTypeParam);

    }

    @Override
    public void update(IdenTypeParam idenTypeParam) {

        if (idenTypeParam == null) {
            throw new ServiceException("参数为空！", HttpStatus.ERROR);
        }

        IdenTypeParam idenTypeParamEntity = this.getById(idenTypeParam.getId());
        if (idenTypeParamEntity == null) {
            throw new ServiceException("参数id为空！", HttpStatus.ERROR);
        }

        IdenType idenType = idenTypeService.getById(idenTypeParam.getIdenTypeId());
        if(idenType == null) {
            throw new ServiceException("识别类型为空！", HttpStatus.ERROR);
        }

        if(!idenTypeParam.getDataType().equals(idenTypeParamEntity.getDataType())){
            throw new ServiceException("识别类型参数数据类型不允许修改！", HttpStatus.ERROR);
        }


        LambdaQueryWrapper<IdenTypeParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenTypeParam::getIdenTypeId, idenTypeParam.getIdenTypeId());
        queryWrapper.eq(IdenTypeParam::getDelFlag, "0");
        List<IdenTypeParam> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getParamName().equals(idenTypeParam.getParamName()) && b.getId().intValue() != idenTypeParam.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("参数名已存在！", HttpStatus.ERROR);
        }

        if(idenTypeParam.getDataType().equals("1")){//1-Float | 2-枚举

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("min", idenTypeParam.getMinValue());
            jsonObject.put("max", idenTypeParam.getMaxValue());
            idenTypeParam.setValueScopeJson(jsonObject.toJSONString());
        }
        if(idenTypeParam.getDataType().equals("2")){//1-Float | 2-枚举

            String[] str = idenTypeParam.getEnumValue().split(",");
            idenTypeParam.setValueScopeJson(JSONArray.toJSONString(str));
        }

        this.lambdaUpdate()
                .set(IdenTypeParam::getParamName, idenTypeParam.getParamName()!=null && !idenTypeParam.getParamName().equals("") ? idenTypeParam.getParamName() : null)
                .set(IdenTypeParam::getDataType, idenTypeParam.getDataType())
                .set(IdenTypeParam::getValueScopeJson, idenTypeParam.getValueScopeJson())
                .set(IdenTypeParam::getRemark, idenTypeParam.getRemark())
                .set(IdenTypeParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenTypeParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenTypeParam::getId, idenTypeParam.getId())
                .update();

    }

    @Override
    public void delete(Long id) {

        IdenTypeParam idenTypeParamEntity = this.getById(id);
        if (idenTypeParamEntity == null) {
            throw new ServiceException("参数id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenTypeParam::getDelFlag, "2")
                .set(IdenTypeParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenTypeParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenTypeParam::getId, id)
                .update();

    }

    @Override
    public void deleteByIdenTypeId(Long idenTypeId) {

        IdenType idenType = idenTypeService.getById(idenTypeId);
        if(idenType == null) {
            throw new ServiceException("识别类型为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenTypeParam::getDelFlag, "2")
                .set(IdenTypeParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenTypeParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenTypeParam::getIdenTypeId, idenTypeId)
                .update();
    }

    @Override
    public void updateParamStatus(IdenTypeParam idenTypeParam) {

        if (idenTypeParam == null) {
            throw new ServiceException("参数为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenTypeParam::getStatus, idenTypeParam.getStatus()!=null && !idenTypeParam.getStatus().equals("") ? idenTypeParam.getStatus() : null)
                .set(IdenTypeParam::getUpdateTime, DateUtils.getNowDate())
                .set(IdenTypeParam::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenTypeParam::getId, idenTypeParam.getId())
                .update();
    }
}




