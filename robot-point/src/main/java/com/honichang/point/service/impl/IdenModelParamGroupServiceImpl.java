package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.*;
import com.honichang.point.mapper.IdenModelParamMapper;
import com.honichang.point.service.*;
import com.honichang.point.mapper.IdenModelParamGroupMapper;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
@Service
public class IdenModelParamGroupServiceImpl extends ServiceImpl<IdenModelParamGroupMapper, IdenModelParamGroup>
    implements IdenModelParamGroupService{

    @Autowired
    private IdenModelParamGroupMapper idenModelParamGroupMapper;

    @Autowired
    private IdenTypeService idenTypeService;

    @Autowired
    private IdenTypeParamService idenTypeParamService;

    @Autowired
    private IdenModelService idenModelService;

    @Autowired
    private IdenModelParamService idenModelParamService;

    @Override
    public List<IdenModelParamGroup> selectParamGroupList(IdenModelParamGroup idenModelParamGroup) {
        return idenModelParamGroupMapper.selectParamGroupList(idenModelParamGroup);
    }

    @Override
    public void add(IdenModelParamGroup idenModelParamGroup) {

        IdenType idenType = idenTypeService.getById(idenModelParamGroup.getIdenTypeId());
        if (idenType == null) {
            throw new ServiceException("识别类型id为空！", HttpStatus.ERROR);
        }

        IdenModel idenModel = idenModelService.getById(idenModelParamGroup.getIdenModelId());
        if (idenModel == null) {
            throw new ServiceException("识别模型id为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<IdenModelParamGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParamGroup::getIdenModelId, idenModelParamGroup.getIdenModelId());
        queryWrapper.eq(IdenModelParamGroup::getIdenTypeId, idenModelParamGroup.getIdenTypeId());
        queryWrapper.eq(IdenModelParamGroup::getDelFlag, "0");
        List<IdenModelParamGroup> list = this.list(queryWrapper);
        if (list != null && list.size() > 0) {
            throw new ServiceException("识别类型已存在！", HttpStatus.ERROR);
        }

        idenModelParamGroup.setStatus("0");
        idenModelParamGroup.setDelFlag("0");
        idenModelParamGroup.setCreateTime(DateUtils.getNowDate());
        idenModelParamGroup.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(idenModelParamGroup);


        IdenTypeParam idenTypeParamDto = new IdenTypeParam();
        idenTypeParamDto.setIdenTypeId(idenType.getId());
        List<IdenTypeParam> idenTypeParamList = idenTypeParamService.selectParamList(idenTypeParamDto);

        for(int i=0; i<idenModelParamGroup.getIdenTypeNum().intValue(); i++){

            if(idenTypeParamList != null && idenTypeParamList.size() > 0){

                idenTypeParamList.stream().forEach(b->{

                    IdenModelParam idenModelParam = new IdenModelParam();
                    idenModelParam.setParamName(b.getParamName());
                    idenModelParam.setIdenTypeName(idenType.getIdenTypeName());
                    idenModelParam.setIdenTypeId(idenType.getId());
                    idenModelParam.setDataType(b.getDataType());
                    idenModelParam.setIdenModelId(idenModel.getId());
                    idenModelParam.setParamGroupId(idenModelParamGroup.getId());
                    idenModelParam.setStatus("0");
                    idenModelParam.setDelFlag("0");
                    idenModelParam.setCreateTime(DateUtils.getNowDate());
                    idenModelParam.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
                    idenModelParamService.save(idenModelParam);

                });

            }

        }
    }

    @Transactional
    @Override
    public void delete(Long id) {

        IdenModelParamGroup idenModelParamGroup = this.getById(id);
        if (idenModelParamGroup == null) {
            throw new ServiceException("识别参数组id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParamGroup::getDelFlag, "2")
                .set(IdenModelParamGroup::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParamGroup::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParamGroup::getId, id)
                .update();

        idenModelParamService.deleteByParamGroupId(id);

    }

    @Override
    public void deleteByIdenModelId(Long idenModelId) {

        IdenModel idenModel = idenModelService.getById(idenModelId);
        if(idenModel == null) {
            throw new ServiceException("识别模型为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModelParamGroup::getDelFlag, "2")
                .set(IdenModelParamGroup::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModelParamGroup::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModelParamGroup::getIdenModelId, idenModelId)
                .update();

    }
}




