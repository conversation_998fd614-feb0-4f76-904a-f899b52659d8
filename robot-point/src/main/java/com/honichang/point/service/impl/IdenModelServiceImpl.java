package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.IdenModel;
import com.honichang.point.service.IdenModelService;
import com.honichang.point.mapper.IdenModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class IdenModelServiceImpl extends ServiceImpl<IdenModelMapper, IdenModel>
    implements IdenModelService{


    @Override
    public List<IdenModel> getIdenModelTree() {

        List<IdenModel> treeList = new ArrayList<IdenModel>();

        IdenModel parent = new IdenModel();
        parent.setId(new Long(0));
        parent.setIdenModelName("点位模型");

        LambdaQueryWrapper<IdenModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModel::getStatus, "0");//0正常| 1停用
        queryWrapper.eq(IdenModel::getDelFlag,"0");//0代表存在 | 2代表删除
        queryWrapper.orderByAsc(IdenModel::getCreateTime);
        List<IdenModel> list = this.list(queryWrapper);
        parent.setChildren(list);

        treeList.add(parent);

        return treeList;
    }

    @Override
    public void add(IdenModel idenModel) {

        LambdaQueryWrapper<IdenModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModel::getDelFlag, "0");
        List<IdenModel> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getIdenModelName().equals(idenModel.getIdenModelName())).findAny().orElse(null) != null) {
            throw new ServiceException("识别模型名已存在！", HttpStatus.ERROR);
        }

        idenModel.setStatus("0");
        idenModel.setDelFlag("0");
        idenModel.setCreateTime(DateUtils.getNowDate());
        idenModel.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(idenModel);

    }

    @Override
    public void update(IdenModel idenModel) {

        if (idenModel == null) {
            throw new ServiceException("识别模型为空！", HttpStatus.ERROR);
        }

        IdenModel idenModelEntity = this.getById(idenModel.getId());
        if (idenModelEntity == null) {
            throw new ServiceException("识别模型id为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<IdenModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModel::getDelFlag, "0");
        List<IdenModel> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getIdenModelName().equals(idenModel.getIdenModelName()) && b.getId().intValue() != idenModel.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("识别模型名已存在！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModel::getIdenModelName, idenModel.getIdenModelName()!=null && !idenModel.getIdenModelName().equals("") ? idenModel.getIdenModelName() : null)
                .set(IdenModel::getSamplePhotoUrl, idenModel.getSamplePhotoUrl())
                .set(IdenModel::getSamplePhotoPhysical, idenModel.getSamplePhotoPhysical())
                .set(IdenModel::getRemark, idenModel.getRemark())
                .set(IdenModel::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModel::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModel::getId, idenModel.getId())
                .update();

    }

    @Transactional
    @Override
    public void delete(Long id) {

        IdenModel idenModelEntity = this.getById(id);
        if (idenModelEntity == null) {
            throw new ServiceException("识别模型id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenModel::getDelFlag, "2")
                .set(IdenModel::getUpdateTime, DateUtils.getNowDate())
                .set(IdenModel::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenModel::getId, id)
                .update();

//        idenTypeParamService.deleteByIdenTypeId(id);
    }

}




