package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.AlarmHardware;
import com.honichang.point.mapper.AlarmEscortMapper;
import com.honichang.point.service.AlarmHardwareService;
import com.honichang.point.mapper.AlarmHardwareMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class AlarmHardwareServiceImpl extends ServiceImpl<AlarmHardwareMapper, AlarmHardware>
    implements AlarmHardwareService{

    @Autowired
    private AlarmHardwareMapper alarmHardwareMapper;

    @Override
    public List<AlarmHardware> selectPageList(AlarmHardware alarmHardware) {
        return alarmHardwareMapper.selectPageList(alarmHardware);
    }
}




