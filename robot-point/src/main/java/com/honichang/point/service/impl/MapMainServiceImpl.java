package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.dispactch.model.Obstacle;
import com.honichang.dispactch.model.Path;
import com.honichang.dispactch.model.Position;
import com.honichang.framework.config.ServerConfig;
import com.honichang.point.domain.MapMain;
import com.honichang.point.domain.MapObstacle;
import com.honichang.point.domain.MapPath;
import com.honichang.point.domain.MapPoint;
import com.honichang.point.mapper.MapMainMapper;
import com.honichang.point.model.vo.MapResponseBase;
import com.honichang.point.service.MapMainService;
import com.honichang.point.service.MapObstacleService;
import com.honichang.point.service.MapPathService;
import com.honichang.point.service.MapPointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class MapMainServiceImpl extends ServiceImpl<MapMainMapper, MapMain>
        implements MapMainService {

    @Resource
    private ServerConfig serverConfig;

    @Resource
    private MapPathService mapPathService;

    @Resource
    private MapPointService mapPointService;

    @Resource
    private MapObstacleService mapObstacleService;


    /**
     * 更新地图名称和比例尺
     *
     * @param id      地图id
     * @param mapName 地图名称
     * @param scale   比例尺
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNameAndScale(Long id, String mapName, Double scale) {
        this.lambdaUpdate()
                .set(MapMain::getMapName, mapName)
                .set(MapMain::getScale, scale)
                .eq(MapMain::getId, id)
                .update();
    }


    @Override
    public MapMain getLatest() {
        return getOne(new LambdaQueryWrapper<MapMain>()
                .eq(MapMain::getDelFlag, "0")
                .orderByDesc(MapMain::getUpdateTime)
                .last("limit 1"));
    }


    /**
     * 生成合并后的SVG文件路径，带随机数防止缓存
     *
     * @param mapId 地图ID
     * @return 合并后的SVG文件路径<br />
     * <code>http://[host]:[port]/profile/map/merge_[mapId].svg?v=[tm]</code>
     */
    @Override
    public String generateMergeSvgPathWithRandom(long mapId) {
        return serverConfig.getUrl() + "/profile/map/merge_" + mapId + ".svg?v=" + System.currentTimeMillis();
    }


    @Override
    public <T extends MapResponseBase> void loadPointsAndPaths(T response) {
        // ↓点位
        List<MapPoint> points = mapPointService.getPointsByMapId(response.getMapId());
        List<Position> positions = new ArrayList<>(points.size());

        points.forEach(_p -> {
            positions.add(new Position(_p.getXmapId(), _p.getPosX(), _p.getPosY(), _p.getAngle(), _p.getInstanceName()));
        });

        response.setAdvancedPoints(positions);

        // ↓路径
        List<MapPath> paths = mapPathService.getPathsByMapId(response.getMapId());
        response.setAdvancedPaths(Path.convertFrom(paths));
    }


    @Override
    public <T extends MapResponseBase> void loadObstacles(T response) {
        List<MapObstacle> obstacles = mapObstacleService.getObstaclesByMapId(response.getMapId());
        response.setObstacles(Obstacle.convertFrom(obstacles));
    }

}




