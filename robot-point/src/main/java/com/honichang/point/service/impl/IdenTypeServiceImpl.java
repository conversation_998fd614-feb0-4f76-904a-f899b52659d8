package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.IdenType;
import com.honichang.point.service.IdenTypeParamService;
import com.honichang.point.service.IdenTypeService;
import com.honichang.point.mapper.IdenTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class IdenTypeServiceImpl extends ServiceImpl<IdenTypeMapper, IdenType>
    implements IdenTypeService{

    @Autowired
    private IdenTypeParamService idenTypeParamService;

    @Override
    public List<IdenType> getIdenTypeTree() {

        List<IdenType> treeList = new ArrayList<IdenType>();

        IdenType parent = new IdenType();
        parent.setId(new Long(0));
        parent.setIdenTypeName("识别类型");

        LambdaQueryWrapper<IdenType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenType::getStatus, "0");//0正常| 1停用
        queryWrapper.eq(IdenType::getDelFlag,"0");//0代表存在 | 2代表删除
        queryWrapper.orderByAsc(IdenType::getCreateTime);
        List<IdenType> list = this.list(queryWrapper);
        parent.setChildren(list);

        treeList.add(parent);

        return treeList;
    }

    @Override
    public void add(IdenType idenType) {

        LambdaQueryWrapper<IdenType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenType::getDelFlag, "0");
        List<IdenType> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getIdenTypeName().equals(idenType.getIdenTypeName())).findAny().orElse(null) != null) {
            throw new ServiceException("识别类型名已存在！", HttpStatus.ERROR);
        }

        idenType.setStatus("0");
        idenType.setDelFlag("0");
        idenType.setCreateTime(DateUtils.getNowDate());
        idenType.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(idenType);

    }

    @Override
    public void update(IdenType idenType) {

        if (idenType == null) {
            throw new ServiceException("识别类型为空！", HttpStatus.ERROR);
        }

        IdenType idenTypeEntity = this.getById(idenType.getId());
        if (idenTypeEntity == null) {
            throw new ServiceException("识别类型id为空！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<IdenType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenType::getDelFlag, "0");
        List<IdenType> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getIdenTypeName().equals(idenType.getIdenTypeName()) && b.getId().intValue() != idenType.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("识别类型名已存在！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenType::getIdenTypeName, idenType.getIdenTypeName()!=null && !idenType.getIdenTypeName().equals("") ? idenType.getIdenTypeName() : null)
                .set(IdenType::getSamplePhotoUrl, idenType.getSamplePhotoUrl())
                .set(IdenType::getSamplePhotoPhysical, idenType.getSamplePhotoPhysical())
                .set(IdenType::getHaveMinMax, idenType.getHaveMinMax())
                .set(IdenType::getRemark, idenType.getRemark())
                .set(IdenType::getUpdateTime, DateUtils.getNowDate())
                .set(IdenType::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenType::getId, idenType.getId())
                .update();

    }

    @Transactional
    @Override
    public void delete(Long id) {

        IdenType idenTypeEntity = this.getById(id);
        if (idenTypeEntity == null) {
            throw new ServiceException("识别类型id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(IdenType::getDelFlag, "2")
                .set(IdenType::getUpdateTime, DateUtils.getNowDate())
                .set(IdenType::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(IdenType::getId, id)
                .update();

        idenTypeParamService.deleteByIdenTypeId(id);
    }
}




