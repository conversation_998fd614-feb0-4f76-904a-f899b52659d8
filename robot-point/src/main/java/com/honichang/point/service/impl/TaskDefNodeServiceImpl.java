package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.service.TaskDefNodeService;
import com.honichang.point.mapper.TaskDefNodeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TaskDefNodeServiceImpl extends ServiceImpl<TaskDefNodeMapper, TaskDefNode>
    implements TaskDefNodeService{

    @Override
    public List<TaskDefNode> getByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskDefNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskDefNode::getTaskId, taskId)
                .eq(TaskDefNode::getDelFlag, "0");
        return list(queryWrapper);
    }

    @Override
    public void deleteByTaskId(Long taskId) {

        LambdaQueryWrapper<TaskDefNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskDefNode::getTaskId, taskId);
        this.remove(queryWrapper);

    }
}




