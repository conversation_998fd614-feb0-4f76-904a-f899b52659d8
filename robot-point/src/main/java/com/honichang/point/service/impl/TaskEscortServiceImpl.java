package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.context.TaskEscortContext;
import com.honichang.dispactch.model.*;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.bo.ChargingPilePathPlan;
import com.honichang.dispactch.model.enums.*;
import com.honichang.dispactch.service.DispatchCommandService;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispactch.service.RobotDrivenService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispactch.util.LogUtil;
import com.honichang.point.domain.TaskEscort;
import com.honichang.point.mapper.TaskEscortMapper;
import com.honichang.point.service.AlarmEscortService;
import com.honichang.point.service.MapPointService;
import com.honichang.point.service.TaskEscortService;
import com.honichang.point.service.TaskInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.*;
import static com.honichang.dispactch.model.enums.TaskEscortPushTemplateEnum.ESCORT_TEMP_ABORT;
import static com.honichang.dispactch.model.enums.TaskEscortStatusEnum.*;

/**
 *
 */
@Slf4j
@Service
public class TaskEscortServiceImpl extends ServiceImpl<TaskEscortMapper, TaskEscort>
        implements TaskEscortService {

    @Resource
    private TaskEscortMapper taskEscortMapper;

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    @Lazy
    private TaskService taskService;

    @Resource
    private RobotDrivenService robotDrivenService;

    @Resource
    private DispatchService dispatchService;

    @Resource
    private MapPointService mapPointService;

    @Resource
    private DispatchCommandService dispatchCommandService;

    @Resource
    private AlarmEscortService alarmEscortService;

    /**
     * 检查机器人在N小时内是否有陪同任务
     *
     * @param robotId 机器人id
     * @return true-有陪同任务，false-没有陪同任务
     */
    @Override
    public boolean checkRobotHasEscortInHours(@NonNull Long robotId, int hours) {
        if (hours < 0) {
            throw new IllegalArgumentException("hours must be positive");
        }
        // N小时后的Date
        Date after3Hours = DateUtils.addHours(DateUtils.getNowDate(), hours);

        return lambdaQuery().eq(TaskEscort::getRobotId, robotId)
                .eq(TaskEscort::getDelFlag, "0")
                .eq(TaskEscort::getStatus, TaskEscortStatusEnum.NOT_STARTED.getCode())
                .le(TaskEscort::getExecTime, after3Hours) // 执行时间 <= N小时后
                .exists();
    }

    @Override
    public List<TaskEscort> getRunningEscortTasks() {
        // 当日的开始和次日的结束时间
        try {
            Date d1 = DateUtils.parseDate(DateUtils.getDate(), DateUtils.YYYY_MM_DD);
            Date d2 = DateUtils.addDays(d1, 1);
            d1 = DateUtils.addDays(d1, -1);
            return taskEscortMapper.getRunningEscortTasks(d1, d2);
        } catch (ParseException e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<TaskEscort> getPendingEscortTasks() {
        // 当日的开始和结束时间
        try {
            Date d1 = DateUtils.parseDate(DateUtils.getDate(), DateUtils.YYYY_MM_DD);
            Date d2 = DateUtils.addDays(d1, 1);
            return taskEscortMapper.getPendingEscortTasks(d1, d2);
        } catch (ParseException e) {
            throw new ServiceException(e);
        }
    }

    /**
     * 获取机器人正在执行或未开始的陪同任务（当天只能有一个，完成的不算）
     *
     * @param robotId 机器人id
     * @param date    yyyy-MM-dd
     * @return 正在执行或未开始的陪同任务
     */
    @Override
    public TaskEscort getPendingOrRunningEscortTasks(long robotId, String date) {
        // 当日的开始和结束时间
        try {
            Date d1 = DateUtils.parseDate(date, DateUtils.YYYY_MM_DD);
            Date d2 = DateUtils.addDays(d1, 1);
            List<TaskEscort> tasks = taskEscortMapper.getPendingOrRunningEscortTasks(robotId, d1, d2);
            if (tasks.isEmpty()) {
                return null;
            }
            return tasks.get(0);
        } catch (ParseException e) {
            throw new ServiceException(e);
        }
    }

    /**
     * 检查当天是否有时间冲突
     *
     * @param taskEscort 条件：新建时id=null，修改时需要排除自身
     * @return true-有时间冲突，false-没有时间冲突
     */
    @Override
    public boolean checkTimeConflict(TaskEscort taskEscort) {
        String execDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, taskEscort.getExecTime());

        TaskEscort entity = getPendingOrRunningEscortTasks(taskEscort.getRobotId(), execDate);
        boolean exist = entity != null;

        if (taskEscort.getId() == null) {
            // 有冲突
            return exist;
        } else {
            // 已经存在，且不是自己
            return exist && !taskEscort.getId().equals(entity.getId());
        }
    }


    // region 👇删除陪同任务

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTaskEscort(TaskEscort taskEscort) {
        TaskEscort entity = getById(taskEscort.getId());
        if (entity == null) {
            throw new ServiceException("陪同任务不存在", HttpStatus.ERROR);
        }
        // 已经开始的任务不允许删除，只能终止
        if (entity.getStatus().compareTo("001") > 0) {
            throw new ServiceException("已经开始的任务不允许删除，只能终止", HttpStatus.ERROR);
        }

        // 如果已经出发去充电或在停车位等待或在接人的路上
        // 则终止导航，并终止任务
        __abort(entity, "删除陪同任务", ESCORT_TEMP_ABORT, true);
    }

    // endregion


    // region 👇修改陪同任务：可能会更改接客点和目的地

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskEscort(TaskEscort te) {
        TaskEscort entity = getById(te.getId());
        if (entity == null) {
            throw new ServiceException("陪同任务不存在", HttpStatus.ERROR);
        }
        if (entity.getStatus().endsWith("9")) {
            throw new ServiceException("陪同任务已经终止，不允许修改", HttpStatus.ERROR);
        }
        if (entity.getStatus().compareTo("401") >= 0) {
            throw new ServiceException("陪同任务已经完成或快要完成，不允许修改", HttpStatus.ERROR);
        }

        RobotContext rc = RobotContext.get(te.getRobotId());
        if (entity.getStatus().equals("001")) {
            if (te.getExecType() == 1) {
                if (rc.isOnline() == false) {
                    throw new ServiceException("当前机器人已离线无法执行任务", HttpStatus.ERROR);
                }
                if (Double.compare(rc.getBattery(), 0.8) < 0) {
                    throw new ServiceException("当前机器人电量不足80%无法执行任务", HttpStatus.ERROR);
                }
            }
        }

        // 需要一系列检查（不分先后）
        // 1.已经接到访客，不允许更改等待点
        // 2.访客完成任务，送访客走，不允许修改目的地
        // 3.访客已经UWB验证完成，已经在陪同了，不允许修改UWB卡
        // 4.陪同任务已经开始，不允许修改机器人、执行类型
        TaskEscortContext tec = null;
        if (TaskEscortContext.exists(te.getId())) {
            tec = TaskEscortContext.get(te.getId());
        }

        boolean changeWaitPoint = !Objects.equals(te.getWaitPointId(), entity.getWaitPointId());
        if (ESCORTING.getCode().compareTo(te.getStatus()) >= 0 && changeWaitPoint) {
            throw new ServiceException("已经接到访客，正在陪同中，不允许更改等待点", HttpStatus.ERROR);
        }
        if (ESCORTING.getCode().compareTo(te.getStatus()) >= 0 && !Objects.equals(te.getUwbCardId(), entity.getUwbCardId())) {
            throw new ServiceException("访客已经完成签到，正在陪同中，不允许修改UWB卡", HttpStatus.ERROR);
        }
        boolean changeArrivePoint = !Objects.equals(te.getArrivePointId(), entity.getArrivePointId());
        if (GOTO_EXIT.getCode().compareTo(te.getStatus()) >= 0 && changeArrivePoint) {
            throw new ServiceException("访客已经完成任务，正在送访客走，不允许修改目的地", HttpStatus.ERROR);
        }
        if ((tec != null || !NOT_STARTED.getCode().equals(te.getStatus()))
                && (!Objects.equals(te.getRobotId(), entity.getRobotId()) || !Objects.equals(te.getExecType(), entity.getExecType()))) {
            throw new ServiceException("陪同任务已经开始，不允许修改机器人和执行类型", HttpStatus.ERROR);
        }


        entity.setTaskName(te.getTaskName());
        entity.setRobotId(te.getRobotId());
        entity.setWaitPointId(te.getWaitPointId());
        entity.setArrivePointId(te.getArrivePointId());
        entity.setExecType(te.getExecType());
        if (entity.getStatus().equals("001")) {
            if (te.getExecType() == 1) {
                entity.setExecTime(DateUtils.getNowDate());
            } else {
                entity.setExecTime(te.getExecTime());
            }
        }
        entity.setTimeoutMin(te.getTimeoutMin());
        entity.setUwbCardId(te.getUwbCardId());
        entity.setSuperintendent(te.getSuperintendent());
        entity.setRemark(te.getRemark());
        entity.setUpdateBy(SecurityUtils.getUserIdOrSystem());
        entity.setUpdateTime(new Date());
        updateById(entity);

        if (tec != null) {
            // 【重要】更新关联的上下文
            tec.setTaskEscort(entity);

            boolean entityChangeAgain = false;
            // 已经出发去接客或已经到达的，更新waitPosition，需要取消当前导航，重新规划去接客
            if (GOTO_VISITOR.getCode().equals(entity.getStatus()) || WAIT_VISITOR_ARRIVE.getCode().equals(entity.getStatus())) {
                if (changeWaitPoint) {
                    Position p = __getXmapPosition(te.getWaitPointId(), rc);
                    // 还有预分配路径没走完，先取消导航
                    if (rc.getPreAllocatedPaths().isNotEmpty()) {
                        dispatchService.abortNavigation(rc.getRobotId());
                    }
                    // 规划新路径并导航
                    if (planPathAndNavigation(te.getRobotId(), te.getId(), p, false, false)) {
                        // 如果是等待访客状态
                        // 需要改变waitArriveTime以重新计算等待超时和状态
                        if (WAIT_VISITOR_ARRIVE.getCode().equals(entity.getStatus())) {
                            entity.setStatus(GOTO_VISITOR.getCode());
                            tec.setWaitArriveTime(null);
                            entityChangeAgain = true;
                            // 停止播放语音
                            try {
                                dispatchCommandService.pauseVoice(rc.getRobotId());
                            } catch (Exception e) {
                                // ignore
                            }
                        }
                    }
                    // 写日志
                    LogUtil.addEscortLog(rc.getRobotId(), te.getId(),
                            StringUtils.format("修改等待点位，前往新等待点({},{})", p.getX(), p.getY()));
                }
            }

            // 已经陪同中或已经到达目的地的，更新arrivePosition，需要取消当前导航，重新规划去目的地
            if (ESCORTING.getCode().equals(entity.getStatus())
                    || ARRIVED_DESTINATION.getCode().equals(entity.getStatus())) {
                if (changeArrivePoint) {
                    Position p = __getXmapPosition(te.getArrivePointId(), rc);
                    // 还有预分配路径没走完，先取消导航
                    if (rc.getPreAllocatedPaths().isNotEmpty()) {
                        dispatchService.abortNavigation(rc.getRobotId());
                    }
                    // 规划新路径并导航
                    // 要求避开身后的访客！！
                    if (planPathAndNavigation(te.getRobotId(), te.getId(), p, true, true)) {
                        // 如果是已经到达目的地，需要改变状态为 Escorting
                        // 如果现在是暂停报警，也需要取消，因为目的地都换了！
                        if (ARRIVED_DESTINATION.getCode().equals(entity.getStatus())) {
                            entity.setStatus(ESCORTING.getCode());
                            tec.setOutOfRangeTime(null);
                            entity.setPauseMin(null);
                            entity.setRecoveryTime(null);
                            entityChangeAgain = true;
                            // 继续播放语音：7请跟随
                            try {
                                dispatchCommandService.playVoice(rc.getRobotId(), 7);
                            } catch (Exception e) {
                                // ignore
                            }
                        }
                    }
                    // 写日志
                    LogUtil.addEscortLog(rc.getRobotId(), te.getId(),
                            StringUtils.format("修改目的地，前往新目的地({},{})", p.getX(), p.getY()));
                }
            }
            // 【重要】重新保存
            if (entityChangeAgain) {
                updateById(entity);
            }
        }
    }

    // endregion


    // region 👇终止&完成陪同任务

    /**
     * 由调度触发的终止陪同任务
     * 1.UWB距离过远且超时
     * 2.等待访客超时
     * 3.人工切换手动模式
     *
     * @param robotId      机器人id
     * @param reason       终止原因
     * @param pushTemplate 推送消息模板，即终止原因
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abortBySchedule(long robotId, String reason, TaskEscortPushTemplateEnum pushTemplate) {
        TaskEscortContext tc = TaskEscortContext.getByRobot(robotId);
        if (tc == null
                || TaskEscortStatusEnum.COMPLETED.getCode().equals(tc.getTaskEscort().getStatus())
                || tc.getTaskEscort().getStatus().endsWith("9")) {
            return;
        }
        __abort(tc.getTaskEscort(), reason, pushTemplate, false);
    }

    /**
     * 手动终止陪同任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abortByTaskEscortId(long taskEscortId, String reason) {
        TaskEscort taskEscort = getById(taskEscortId);
        if (taskEscort == null) {
            throw new ServiceException("陪同任务不存在", HttpStatus.ERROR);
        }
        __abort(taskEscort, reason, ESCORT_TEMP_ABORT, false);
    }

    private void __abort(@NonNull TaskEscort taskEscort, String reason, TaskEscortPushTemplateEnum pushTemplate, boolean remove) {

        // 状态由：xxx => xx9
        taskEscort.setStatus(taskEscort.getStatus().substring(0, 2) + "9");
        taskEscort.setDelFlag(remove ? "2" : "0");
        taskEscort.setEndTime(taskEscort.getStartTime() == null ? null : new Date());
        taskEscort.setUpdateBy(SecurityUtils.getUserIdOrSystem());
        taskEscort.setUpdateTime(new Date());
        taskEscortMapper.updateById(taskEscort);

        if (TaskEscortContext.exists(taskEscort.getId())) {
            RobotContext rc = RobotContext.get(taskEscort.getRobotId());
            // ！！这里先不能删除，必须无方向导航到十字路口才可以
            // ！！否则容易出现在巷道内调头的危险事件
//            TaskEscortContext.remove(taskEscort.getId());

            if (rc.isEscorting()) {
                if (rc.getPreAllocatedPaths().isNotEmpty()) {
                    // 取消当前导航
                    try {
                        dispatchService.abortNavigation(rc.getRobotId());
                    } catch (Exception e) {
                        // ignore
                    }
                }

                rc.setWorkStatus(ESCORT_RETURN);
                rc.clearPathChain();
                // 停止播放音乐
                try {
                    dispatchCommandService.pauseVoice(taskEscort.getRobotId());
                } catch (Exception e) {
                    // ignore: 不可打断下面的状态转换
                }

                // 导航到最近的十字路口，才算安全，可以随意调头了
                boolean moved = dispatchService.navigateToNearestCrossPoint(rc.getRobotId());
                if (!moved) {
                    // 就在脚下
                    rc.setWorkStatus(STANDBY);
                    if (rc.getStatus() == RobotStatusEnum.WORKING) {
                        rc.setStatus(RobotStatusEnum.STANDBY);
                    }
                }

                // 陪同报警
                alarmEscortService.add(taskEscort, reason, pushTemplate);
            }
        }

        // 异步写陪同日志
        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), reason);
    }

    @Override
    public void completeLeave(TaskEscort taskEscort) {
        taskEscort.setStatus(COMPLETED.getCode());
        taskEscort.setEndTime(new Date());
        taskEscort.setUpdateBy(SecurityUtils.getUserIdOrSystem());
        taskEscort.setUpdateTime(new Date());
        taskEscortMapper.updateById(taskEscort);

        RobotContext rc = RobotContext.get(taskEscort.getRobotId());
        // ！！这里先不能删除，必须无方向导航到十字路口才可以
        // ！！否则容易出现在巷道内调头的危险事件
//            TaskEscortContext.remove(taskEscort.getId());

        rc.setWorkStatus(ESCORT_RETURN);
        rc.clearPathChain();
        // 停止播放音乐
        try {
            dispatchCommandService.pauseVoice(taskEscort.getRobotId());
        } catch (Exception e) {
            // ignore: 不可打断下面的状态转换
        }

        // 导航到最近的十字路口，才算安全，可以随意调头了
        boolean moved = dispatchService.navigateToNearestCrossPoint(rc.getRobotId());
        if (!moved) {
            // 就在脚下
            rc.setWorkStatus(STANDBY);
            if (rc.getStatus() == RobotStatusEnum.WORKING) {
                rc.setStatus(RobotStatusEnum.STANDBY);
            }
        }

        // 异步写陪同日志
        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), "陪同任务完成");
    }

    // endregion


    // region 👇访客完成任务

    @Override
    public boolean visitorComplete(TaskEscort taskEscort) {
        // 更新状态
        taskEscort.setStatus(TaskEscortStatusEnum.GOTO_EXIT.getCode());
        taskEscort.setPauseMin(null);
        taskEscort.setRecoveryTime(null);

        TaskEscortContext tec = TaskEscortContext.get(taskEscort.getId());
        taskEscortMapper.updateById(taskEscort);
        // 导航到出口
        // ！不能回头，必须继续往前引导访客绕一下路
        boolean hasPath = planPathAndNavigation(taskEscort.getRobotId(), taskEscort.getId(), tec.getWaitPosition(), true, true);

        // 播放语音：7请跟随
        try {
            dispatchCommandService.playVoice(taskEscort.getRobotId(), 7);
        } catch (Exception e) {
            // ignore
        }

        // 写日志
        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), "访客完成任务，送访客到出口");
        return hasPath;
    }

    // endregion


    // region 👇陪同任务执行调度

    /**
     * 手工触发立即执行
     * 开始陪同任务，前往接人点
     * 操作：
     * 1. 检查机器人状态
     * 2. 如果处于充电中，且电量大于50%，则停止充电
     * 3. 停止机器人当前普通任务
     * 4. 清空RobotContext中的队列（仅保留任务队列）
     * 5. 更新陪同任务状态即开始时间并save
     * 6. 写日志
     * 失败的可能：
     * 1. 机器人处于充电中，且电量小于50%
     * 2. 机器人状态非法
     *
     * @param robotId    机器人ID
     * @param taskEscort 正在执行的陪同任务
     * @throws ServiceException 机器人状态非法、机器人电量不足
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startToVisit(long robotId, TaskEscort taskEscort) {
        this.startToVisit(robotId, taskEscort, false);
    }

    @Override
    public boolean planPathAndNavigation(long robotId, long taskEscortId, Position p, boolean alwaysForward, boolean avoidVisitor) {
        RobotContext rc = RobotContext.get(robotId);
        try {
            // 避开身后的访客
            IPath tail = null;
            if (avoidVisitor) {
                tail = rc.getCompletedPaths().getTail();
                if (tail == null) {
                    tail = rc.getHisCompletedPaths().getTail();
                }
            }

            Optional<PathChain<IPath>> pathChain =
                    dispatchService.planPath(rc.getRobotId(), rc.getPosition(), p,
                            alwaysForward, tail == null ? null : tail.getXmapId());

            // 陪同任务只有正走，不存在倒走
            if (alwaysForward) {
                if (pathChain.isPresent() && Chain.isNotEmpty(pathChain.get())) {
                    pathChain.get().forEach(path -> ((Path) path).setDirection(DirectionEnum.FORWARD.getCode()));
                }
            }
            pathChain.ifPresent(pc -> dispatchService.navigate(rc.getRobotId(), taskEscortId, pc));
            return pathChain.isPresent() && Chain.isNotEmpty(pathChain.get());
        } catch (Exception e) {
            String msg = "无法执行陪同任务：" + e.getMessage();
            throw new ServiceException(msg, HttpStatus.ERROR);
        }
    }

    /**
     * 开始陪同任务：可能是去充电
     * 由调度中心自动触发
     *
     * @param toCharge 是否去充电
     * @throws ServiceException 无空闲充电桩、找不到规划路径、地图点位不存在、机器人状态非法、机器人电量不足
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startToVisit(long robotId, TaskEscort taskEscort, boolean toCharge) {

        RobotContext rc = RobotContext.get(robotId);
        try {
            if (!toCharge) {

                // 立即执行，去出发点
                // 1. 检查机器人状态
                // 2. 如果处于充电中，且电量大于50%，则停止充电
                // 3. 停止机器人当前普通任务
                // 4. 清空RobotContext中的队列（仅保留任务队列）
                // 5. 更新陪同任务状态即开始时间并save
                // 6. 写日志
                // throws: 失败的可能（未写log）：
                // 1. 机器人处于充电中，且电量小于50%
                // 2. 机器人状态非法
                __startToVisit(rc.getRobotId(), taskEscort);

                // 顺序必须在这里，否则可能还没停止充电，就出发了！！
                planPathAndNavigation(rc.getRobotId(), taskEscort.getId(),
                        __getXmapPosition(taskEscort.getWaitPointId(), rc),
                        false, false);

            } else {
                // 去充电
                // 1.找最近的充电桩
                // 2.如果充电桩都在忙则找最少预约且最近的旁边的停车位
                // 3.开始导航
                // 4.预约充电桩
                ChargingPilePathPlan pile = dispatchService.navigateToCharge(rc.getRobotId());

                // 5.写日志
                String content = StringUtils.format("前往充电：陪同任务“{}”", taskEscort.getTaskName());
                if (StringUtils.isNotBlank(pile.getChargingPile().getLocation())) {
                    content += "，充电桩“" + pile.getChargingPile().getLocation() + "”";
                }
                if (!pile.isPile() && Chain.isNotEmpty(pile.getPathChain())) {
                    IPosition parkPos = pile.getPathChain().getTail().getEndPos();
                    content += StringUtils.format("旁的停车位({},{})排队", parkPos.getX(), parkPos.getY());
                }
                LogUtil.addRobotLog(LogRobotTypeEnum.CHARGE, content, rc.getRobotId(), "system");
            }

            // 检查是否在上下文中，推入上下文
            boolean exist = TaskEscortContext.exists(taskEscort.getId());
            if (!exist) {
                Position waitPos = __getXmapPosition(taskEscort.getWaitPointId(), rc);
                Position arrivePos = __getXmapPosition(taskEscort.getArrivePointId(), rc);
                TaskEscortContext.put(taskEscort, waitPos, arrivePos);
            }
        } catch (ServiceException e) {
            log.error("机器人[{}]开始陪同任务异常", rc.getRobotName(), e);
            LogUtil.addEscortLog(robotId, taskEscort.getId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("机器人[{}]开始陪同任务异常", rc.getRobotName(), e);
            LogUtil.addEscortLog(robotId, taskEscort.getId(), "开始陪同任务异常: " + e.getMessage());
            throw new ServiceException(e.getMessage(), HttpStatus.ERROR);
        }

        // 确保无任何异常再：
        // 更新RobotContext状态
        rc.setWorkStatus(toCharge ? ESCORT_CHARGE : ESCORT_TASK);
        rc.setStatus(RobotStatusEnum.WORKING);
    }

    /**
     * 访客抵达，去目标地点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean escortConfirmVisit(Long robotId, Long taskEscortId) {
        TaskEscortContext tec = TaskEscortContext.get(taskEscortId);
        TaskEscort taskEscort = tec.getTaskEscort();
        taskEscort.setStatus(ESCORTING.getCode()); // 201
        taskEscortMapper.updateById(taskEscort);

        boolean hasPath = planPathAndNavigation(robotId, taskEscortId, tec.getArrivePosition(), true, false);

        // 写日志
        LogUtil.addEscortLog(robotId, taskEscort.getId(), "接到访客，前往目的地");
        return hasPath;
    }

    /**
     * 机器人到达等待点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void arriveWaitPoint(Long taskEscortId) {
        TaskEscortContext tec = TaskEscortContext.get(taskEscortId);
        TaskEscort taskEscort = tec.getTaskEscort();
        taskEscort.setStatus(WAIT_VISITOR_ARRIVE.getCode());
        taskEscortMapper.updateById(taskEscort);

        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), "抵达等待点，等待访客");
        tec.setWaitArriveTime(DateUtils.getNowDate());
    }

    /**
     * 访客完成任务，送访客到出口
     * <p>
     * 401=>402
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void arriveExitPoint(Long taskEscortId) {
        TaskEscortContext tec = TaskEscortContext.get(taskEscortId);
        TaskEscort taskEscort = tec.getTaskEscort();
        taskEscort.setStatus(WAIT_VISITOR_LEAVE.getCode()); // 402
        taskEscortMapper.updateById(taskEscort);

        tec.setOutOfRangeTime(null);

        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), "抵达出口，等待访客离开");
        tec.setWaitArriveTime(DateUtils.getNowDate());
    }

    /**
     * 机器人到达目标点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void arriveTargetPoint(Long taskEscortId) {
        TaskEscortContext tec = TaskEscortContext.get(taskEscortId);
        TaskEscort taskEscort = tec.getTaskEscort();
        taskEscort.setStatus(ARRIVED_DESTINATION.getCode());
        taskEscort.setRecoveryTime(null);
        taskEscort.setPauseMin(null);
        taskEscortMapper.updateById(taskEscort);

        LogUtil.addEscortLog(taskEscort.getRobotId(), taskEscort.getId(), "抵达目的地，等待访客完成工作");
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreAlarm(TaskEscort taskEscort) {
        taskEscort.setStatus(ARRIVED_DESTINATION.getCode()); // 301-抵达目的地
        taskEscort.setPauseMin(null);
        taskEscort.setRecoveryTime(null);
        taskEscort.setUpdateBy(SecurityUtils.getUserIdOrSystem());
        taskEscort.setUpdateTime(new Date());
        updateById(taskEscort);
    }

    @NonNull
    private Position __getXmapPosition(Long mapPointId, RobotContext rc) {
        return mapPointService.getXmapPosition(mapPointId, rc.getMapId());
    }

    private void __startToVisit(long robotId, TaskEscort taskEscort) {
        RobotContext rc = RobotContext.get(robotId);
        String reason = taskService.canWorkForEscort(rc);
        if (!StringUtils.EMPTY.equals(reason)) {
            throw new ServiceException(reason, HttpStatus.ERROR);
        }

        // 充电中！！！
        if (rc.isCharging()) {
            // 小于50%就报错了！！
            if (rc.getBattery() < 0.5) {
                String msg = "机器人电量不足50%，无法执行陪同任务！";
                throw new ServiceException(msg, HttpStatus.ERROR);
            }
            // 电量大于50%，直接去接人点
            robotDrivenService.stopCharging(robotId, "陪同任务开始，停止充电");
            // 等待3秒钟
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                // ignore
            }
        }

        // 停止机器人一切任务
        taskInstanceService.pauseTask(robotId, StringUtils.format("陪同任务开始：{}", taskEscort.getTaskName()));

        // 更新陪同任务状态
        taskEscort.setRobotId(robotId);
        taskEscort.setStatus(TaskEscortStatusEnum.GOTO_VISITOR.getCode());
        if (taskEscort.getStartTime() == null) {
            taskEscort.setStartTime(new Date());
        }
        taskEscort.setUpdateBy(SecurityUtils.getUserIdOrSystem());
        taskEscort.setUpdateTime(new Date());
        taskEscortMapper.updateById(taskEscort);

        // 清空RobotContext中的队列（仅保留任务队列）
        rc.getTaskDirectiveChain().clear();
        rc.clearPathChain();

        // 写陪同日志
        LogUtil.addEscortLog(robotId, taskEscort.getId(), "陪同任务开始，前往接人点");
    }

    // endregion


}




