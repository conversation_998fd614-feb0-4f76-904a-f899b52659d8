package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.TaskRobot;
import com.honichang.point.service.TaskRobotService;
import com.honichang.point.mapper.TaskRobotMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TaskRobotServiceImpl extends ServiceImpl<TaskRobotMapper, TaskRobot>
    implements TaskRobotService{

    @Override
    public List<TaskRobot> getByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskRobot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskRobot::getTaskId, taskId);
        return list(queryWrapper);
    }

    @Override
    public void deleteByTaskId(Long taskId) {

        LambdaQueryWrapper<TaskRobot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskRobot::getTaskId, taskId);
        this.remove(queryWrapper);

    }
}




