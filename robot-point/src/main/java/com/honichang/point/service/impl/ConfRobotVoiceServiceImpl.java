package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.ConfRobotVoice;
import com.honichang.point.domain.ConfRobotVoicePublish;
import com.honichang.point.domain.Robot;
import com.honichang.point.service.ConfRobotVoicePublishService;
import com.honichang.point.service.ConfRobotVoiceService;
import com.honichang.point.mapper.ConfRobotVoiceMapper;
import com.honichang.point.service.RobotService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class ConfRobotVoiceServiceImpl extends ServiceImpl<ConfRobotVoiceMapper, ConfRobotVoice>
    implements ConfRobotVoiceService{

    @Resource
    private RobotService robotService;
    @Resource
    private ConfRobotVoicePublishService confRobotVoicePublishService;

    @Override
    @Transactional
    public boolean add(ConfRobotVoice confRobotVoice) {
        boolean save = save(confRobotVoice);
        confRobotVoicePublishAdd(confRobotVoice);
        return save;
    }

    private void confRobotVoicePublishAdd(ConfRobotVoice confRobotVoice) {
        List<Robot> robotList = null;
        boolean contains0 = false;
        for (Long robotId : confRobotVoice.getRobotIds()) {
            if (robotId.toString().equals("0")){
                contains0 = true;
            }
        }
        if (contains0) {
            robotList = robotService.list();
        } else if(confRobotVoice.getRobotIds().size() > 0) {
            robotList = robotService.listByIds(confRobotVoice.getRobotIds());
        }
        if(robotList != null) {
            for (Robot robot : robotList) {
                ConfRobotVoicePublish confRobotVoicePublish = new ConfRobotVoicePublish();
                confRobotVoicePublish.setRobotVoidId(confRobotVoice.getId());
                confRobotVoicePublish.setVersion(confRobotVoice.getVersion());
                confRobotVoicePublish.setCompleteTime(confRobotVoice.getCreateTime());
                confRobotVoicePublish.setCreateBy(confRobotVoice.getCreateBy());
                confRobotVoicePublish.setCreateTime(confRobotVoice.getCreateTime());
                confRobotVoicePublish.setRemark(confRobotVoice.getRemark());
                confRobotVoicePublish.setRobotId(robot.getId());
                confRobotVoicePublishService.save(confRobotVoicePublish);
            }
        }
    }

    @Override
    @Transactional
    public boolean edit(ConfRobotVoice confRobotVoice) {
        boolean b = updateById(confRobotVoice);
        LambdaQueryWrapper<ConfRobotVoicePublish> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfRobotVoicePublish::getRobotVoidId, confRobotVoice.getId());
        confRobotVoicePublishService.removeById(wrapper);
        confRobotVoicePublishAdd(confRobotVoice);
        return b;
    }
}




