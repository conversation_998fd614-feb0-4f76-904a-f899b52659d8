package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.Robot;
import com.honichang.point.mapper.RobotMapper;
import com.honichang.point.service.MapMainService;
import com.honichang.point.service.RobotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class RobotServiceImpl extends ServiceImpl<RobotMapper, Robot>
    implements RobotService{


    @Autowired
    private RobotMapper robotMapper;

    @Resource
    private MapMainService mapMainService;

    @Override
    public List<Robot> getRobotList() {
       return robotMapper.getRobotList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRobot(Robot robot) {
        robot.setCreateBy(SecurityUtils.getUserId().toString());
        robot.setCreateTime(DateUtils.getNowDate());
        robot.setUpdateBy(robot.getCreateBy());
        robot.setUpdateTime(robot.getCreateTime());
        robot.setMapId(mapMainService.getLatest().getId());
        robot.setWorkMode(101);
        robot.setStatus("8");
        return save(robot);
    }
}




