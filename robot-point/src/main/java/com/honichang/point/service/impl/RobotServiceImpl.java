package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.Robot;
import com.honichang.point.service.RobotService;
import com.honichang.point.mapper.RobotMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class RobotServiceImpl extends ServiceImpl<RobotMapper, Robot>
    implements RobotService{


    @Autowired
    private RobotMapper robotMapper;

    @Override
    public List<Robot> getRobotList() {
       return robotMapper.getRobotList();
    }
}




