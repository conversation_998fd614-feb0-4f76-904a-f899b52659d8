package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.TaskDef;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskRobot;
import com.honichang.point.mapper.TaskDefMapper;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.TaskDefNodeService;
import com.honichang.point.service.TaskDefService;
import com.honichang.point.service.TaskRobotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class TaskDefServiceImpl extends ServiceImpl<TaskDefMapper, TaskDef>
    implements TaskDefService{

    @Autowired
    private TaskDefMapper taskDefMapper;

    @Autowired
    private TaskDefNodeService taskDefNodeService;

    @Autowired
    private TaskRobotService taskRobotService;

    @Autowired
    private BizPointService bizPointService;

    @Override
    public List<TaskDef> selectList(TaskDef taskDef) {
        return taskDefMapper.selectList(taskDef);
    }

    // TODO: @广伟：添加和修改任务定义的时候，需要验证，taskDef.getTaskRobotList()至少有一个主机器人
    @Transactional
    @Override
    public void add(TaskDef taskDef) {

        List<Long> bizPointIds = taskDef.getBizPointIds();
        if(bizPointIds == null || bizPointIds.size() <= 0){
            throw new ServiceException("请选择点位！", HttpStatus.ERROR);
        }
        List<TaskRobot> taskRobotList = taskDef.getTaskRobotList();
        if(taskRobotList == null || taskRobotList.size() <= 0){
            throw new ServiceException("请添加机器人！", HttpStatus.ERROR);
        }

//        List<TaskRobot> robots = taskRobotList.stream().filter(b-> b.getMasterFlag().equals("1")).collect(Collectors.toList());
//        if(robots == null || robots.size() != 1){
//            throw new ServiceException("请添加一个主机器人！", HttpStatus.ERROR);
//        }


        Integer planType = taskDef.getPlanType();
        if(planType == 0){

//            taskDef.setCycleType(null);
            taskDef.setCycleTrigger(null);
            taskDef.setCycleScope(null);
//            taskDef.setCycleInterval(null);
        }
        if(planType == 1){

            taskDef.setBeginTime(null);
            taskDef.setEndTime(null);

        }

        taskDef.setStatus("0");
        taskDef.setDelFlag("0");
        taskDef.setCreateTime(DateUtils.getNowDate());
        taskDef.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(taskDef);


        for(Long bizPointId : bizPointIds){

            BizPoint bizPoint = bizPointService.getById(bizPointId);
            if(bizPoint != null) {

                TaskDefNode taskDefNode = new TaskDefNode();
                taskDefNode.setBizPointId(bizPointId);
                taskDefNode.setTaskId(taskDef.getId());
                taskDefNode.setStatus("0");
                taskDefNode.setDelFlag("0");
                taskDefNode.setCreateTime(DateUtils.getNowDate());
                taskDefNode.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
                taskDefNodeService.save(taskDefNode);
            }

        }


        for(TaskRobot taskRobot : taskRobotList){

            taskRobot.setTaskId(taskDef.getId());
            taskRobotService.save(taskRobot);
        }

    }

    @Transactional
    @Override
    public void update(TaskDef taskDef) {

        if (taskDef == null) {
            throw new ServiceException("任务为空！", HttpStatus.ERROR);
        }

        TaskDef taskDefEntity = this.getById(taskDef.getId());
        if (taskDefEntity == null) {
            throw new ServiceException("任务id为空！", HttpStatus.ERROR);
        }


        List<Long> bizPointIds = taskDef.getBizPointIds();
        if(bizPointIds == null || bizPointIds.size() <= 0){
            throw new ServiceException("请选择点位！", HttpStatus.ERROR);
        }
        List<TaskRobot> taskRobotList = taskDef.getTaskRobotList();
        if(taskRobotList == null || taskRobotList.size() <= 0){
            throw new ServiceException("请添加机器人！", HttpStatus.ERROR);
        }

//        List<TaskRobot> robots = taskRobotList.stream().filter(b-> b.getMasterFlag().equals("1")).collect(Collectors.toList());
//        if(robots == null || robots.size() != 1){
//            throw new ServiceException("请添加一个主机器人！", HttpStatus.ERROR);
//        }


        this.lambdaUpdate()
                .set(TaskDef::getTaskName, taskDef.getTaskName())
                .set(TaskDef::getPriorty, taskDef.getPriorty())
                .set(TaskDef::getDeptId, taskDef.getDeptId())
                .set(TaskDef::getRemark, taskDef.getRemark())

                .set(TaskDef::getPlanType, taskDef.getPlanType())

                .set(TaskDef::getBeginTime, taskDef.getPlanType().equals("0") ? taskDef.getBeginTime() : null)
                .set(TaskDef::getEndTime, taskDef.getPlanType().equals("0") ? taskDef.getEndTime() : null)

//                .set(TaskDef::getCycleType, taskDef.getPlanType().equals("1") ? taskDef.getCycleType() : null)
                .set(TaskDef::getCycleTrigger, taskDef.getPlanType().equals("1") ? taskDef.getCycleTrigger() : null)
                .set(TaskDef::getCycleScope, taskDef.getPlanType().equals("1") ? taskDef.getCycleScope() : null)
//                .set(TaskDef::getCycleInterval, taskDef.getPlanType().equals("1") ? taskDef.getCycleInterval() : null)

                .set(TaskDef::getUpdateTime, DateUtils.getNowDate())
                .set(TaskDef::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(TaskDef::getId, taskDef.getId())
                .update();

        taskDefNodeService.deleteByTaskId(taskDef.getId());
        taskRobotService.deleteByTaskId(taskDef.getId());


        for(Long bizPointId : bizPointIds){

            TaskDefNode taskDefNode = new TaskDefNode();
            taskDefNode.setBizPointId(bizPointId);
            taskDefNode.setTaskId(taskDef.getId());
            taskDefNode.setStatus("0");
            taskDefNode.setDelFlag("0");
            taskDefNode.setCreateTime(DateUtils.getNowDate());
            taskDefNode.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
            taskDefNodeService.save(taskDefNode);
        }


        for(TaskRobot taskRobot : taskRobotList){

            taskRobot.setTaskId(taskDef.getId());
            taskRobotService.save(taskRobot);
        }

    }

    @Transactional
    @Override
    public void delete(Long id) {

        TaskDef taskDef = this.getById(id);
        if (taskDef == null) {
            throw new ServiceException("任务id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(TaskDef::getDelFlag, "2")
                .set(TaskDef::getUpdateTime, DateUtils.getNowDate())
                .set(TaskDef::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(TaskDef::getId, id)
                .update();

        taskDefNodeService.deleteByTaskId(taskDef.getId());
        taskRobotService.deleteByTaskId(taskDef.getId());
    }

    @Override
    public List<TaskDef> selectAllCycleTask() {
        Date now = DateUtils.getNowDate();
        List<TaskDef> defs = taskDefMapper.selectAllCycleTask(now);
        __congestion(defs);
        return defs;
    }

    @Override
    public List<TaskDef> selectAllPeriodTask() {
        List<TaskDef> defs = taskDefMapper.selectAllPeriodTaskInValidScope();
        __congestion(defs);
        return defs;
    }

    private void __congestion(@NonNull List<TaskDef> defs) {
        defs.forEach(def -> {
            def.setTaskRobotList(taskRobotService.getByTaskId(def.getId()));
            def.setRobotNum(def.getTaskRobotList().size());
        });
    }

    @Override
    public List<TaskDef> selectAllPeriodTask(long robotId) {
        return taskDefMapper.selectAllPeriodTask(robotId);
    }

}




