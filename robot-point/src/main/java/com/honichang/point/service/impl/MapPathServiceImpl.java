package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.MapPath;
import com.honichang.point.service.MapPathService;
import com.honichang.point.mapper.MapPathMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 *
 */
@Service
public class MapPathServiceImpl extends ServiceImpl<MapPathMapper, MapPath>
    implements MapPathService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByMapId(Long mapId) {
        this.lambdaUpdate()
                .set(MapPath::getDelFlag, "2")
                .set(MapPath::getUpdateTime, DateUtils.getNowDate())
                .set(MapPath::getUpdateBy, SecurityUtils.getUserId().toString())
                .eq(MapPath::getMapId, mapId)
                .update();
    }

    @Override
    public List<MapPath> getPathsByMapId(Long mapId) {
        return list(new LambdaQueryWrapper<MapPath>()
                .eq(MapPath::getMapId, mapId)
                .eq(MapPath::getDelFlag, "0"));
    }
}




