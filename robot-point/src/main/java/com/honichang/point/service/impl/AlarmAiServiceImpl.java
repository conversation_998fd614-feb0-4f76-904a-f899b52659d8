package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.event.DispatchEvent;
import com.honichang.dispactch.event.DispatchEventType;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.TaskDirectiveChain;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.point.domain.*;
import com.honichang.point.domain.dto.AbnormalReceptionParams;
import com.honichang.point.mapper.AlarmAiMapper;
import com.honichang.point.service.AlarmAiService;
import com.honichang.point.service.MediaFileService;
import com.honichang.point.service.RobotHardwareService;
import com.honichang.point.service.TaskDefService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;

/**
 *
 */
@Slf4j
@Service
public class AlarmAiServiceImpl extends ServiceImpl<AlarmAiMapper, AlarmAi>
    implements AlarmAiService{

    @Autowired
    private AlarmAiMapper alarmAiMapper;
    @Resource
    private RobotHardwareService robotHardwareService;
    @Resource
    private DispatchService dispatchService;
    @Resource
    private TaskDefService taskDefService;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private MediaFileService mediaFileService;

    @Override
    public List<AlarmAi> selectPageList(AlarmAi alarmAi) {
        return alarmAiMapper.selectPageList(alarmAi);
    }

    @Override
    public void confirm(AlarmAi alarmAi) {

        AlarmAi alarmAiEntity = getById(alarmAi.getId());
        if(alarmAiEntity == null) {
            throw new ServiceException("报警查询为空！", HttpStatus.ERROR);
        }

        if(alarmAiEntity.getConfirmStatus().equals("1")){
            throw new ServiceException("当前报警已确认！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(AlarmAi::getConfirmStatus, "1")
                .set(AlarmAi::getConfirmPerson, SecurityUtils.getLoginUser().getUser().getUserId().intValue())
                .set(AlarmAi::getConfirmContent, alarmAi.getConfirmContent())
                .set(AlarmAi::getConfirmTime, DateUtils.getNowDate())
                .eq(AlarmAi::getId, alarmAi.getId())
                .update();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abnormalReception(AbnormalReceptionParams param) {
        AlarmAi entity = new AlarmAi();
        entity.setIdenType(2); // AI
        entity.setSignType(1); // 发现点
        entity.setIdenResult(param.getAlarm_type()); // 漏液、障碍、异物
        entity.setAlarmLevel("1");
        try {
            entity.setTriggerTime(DateUtils.parseDate(param.getAlarm_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        } catch (ParseException e) {
            log.error("AI报警时间解析失败：{}", param.getAlarm_time(), e);
            entity.setTriggerTime(DateUtils.getNowDate());
        }

        // 根据摄像头ip确定是哪个机器人
        RobotHardware hardware = robotHardwareService.getRobotIdByIpAndPort(param.getCamera_ip(), param.getCamera_port());
        if (hardware == null) {
            throw new ServiceException("根据摄像头ip和端口未找到对应的机器人！", HttpStatus.ERROR);
        }
        RobotContext rc = RobotContext.get(hardware.getRobotId());
        entity.setRobotId(hardware.getRobotId());

        // 获取机器人当前点位信息
        Position position = rc.getPosition();
        entity.setPosX(position.getX());
        entity.setPosY(position.getY());

        // 计算机器人最近地图点位
        BizPoint bizPoint = dispatchService.findNearestBizPoint(hardware.getRobotId());
        if (bizPoint == null) {
            log.error("机器人最近点位计算失败，机器人id={}, pos=({},{})", hardware.getRobotId(), rc.getPosition().getX(), rc.getPosition().getY());
            entity.setAlarmName(StringUtils.format("{}报警", entity.getIdenResult()));
        } else {
            entity.setNearPointId(bizPoint.getId());
            entity.setAlarmName(StringUtils.format("最近点位（{}）{}报警", bizPoint.getInstanceName(), entity.getIdenResult()));
        }

        // 点位是否在任务中，如果是提取所属部门
        TaskDirectiveChain directiveChain = rc.getTaskDirectiveChain();
        if (directiveChain != null && directiveChain.getTaskInstance() != null) {
            TaskInstance taskInstance = directiveChain.getTaskInstance();
            TaskDef def = taskDefService.getById(taskInstance.getTaskId());
            entity.setDeptId(def.getDeptId());
            entity.setTaskInstanceId(taskInstance.getId());
        }

        entity.setConfirmStatus("0");
        entity.setStatus("0");
        entity.setDelFlag("0");
        entity.setCreateBy(SecurityUtils.getUserIdOrSystem());
        entity.setCreateTime(DateUtils.getNowDate());
        entity.setUpdateBy(entity.getCreateBy());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setRemark(StringUtils.EMPTY);
        save(entity);

        // 保存报警图片
        mediaFileService.saveMediaFile(entity.getId(), "alarm_ai", param.getPicture());

        // 漏液、障碍、异物
        // 如果是漏液，直接汇报障碍，不需要确认
        if ("漏液".equals(param.getAlarm_type())) {
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.BLOCK_EVENT,
                    "AlarmAiService.abnormalReception",
                    ImmutableMap.of("robotId", hardware.getRobotId(), "reason", "AI检测到漏液"),
                    true));
        }

        // 如果是障碍，需要确认
        if ("障碍".equals(param.getAlarm_type())) {
            eventPublisher.publishEvent(new DispatchEvent(
                    DispatchEventType.BLOCK_EVENT,
                    "AlarmAiService.abnormalReception",
                    ImmutableMap.of("robotId", hardware.getRobotId(), "reason", "AI检测到障碍"),
                    true));
        }

        // 其他不需要，直接压过去！
    }
}




