package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.MapPoint;
import com.honichang.point.mapper.MapPointMapper;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.MapPointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Service
public class MapPointServiceImpl extends ServiceImpl<MapPointMapper, MapPoint>
    implements MapPointService{

    @Resource
    private BizPointService bizPointService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByMapId(Long mapId) {
        // 清空
        this.lambdaUpdate()
                .set(MapPoint::getDelFlag, "2")
                .set(MapPoint::getUpdateTime, DateUtils.getNowDate())
                .set(MapPoint::getUpdateBy, SecurityUtils.getUserId().toString())
                .eq(MapPoint::getMapId, mapId)
                .update();
    }

    @Override
    public List<MapPoint> getPointsByMapId(Long mapId) {

        List<MapPoint> mapPoints = this.lambdaQuery()
                .eq(MapPoint::getMapId, mapId)
                .eq(MapPoint::getDelFlag, "0")
                .list();

        List<BizPoint> bizPoints = bizPointService.list();

        mapPoints.forEach(_p -> {
            BizPoint bizPoint = bizPoints.stream()
                    .filter(b -> Objects.equals(b.getMapPointId(), _p.getId())
                            && PointClassNameEnum.CROSSROADS.getCode().equals(b.getClassName()))
                    .findFirst()
                    .orElse(null);

            if (bizPoint == null) {
                bizPoint = bizPoints.stream()
                        .filter(b -> Objects.equals(b.getMapPointId(), _p.getId()))
                        .findFirst()
                        .orElse(null);
            }

            _p.setBizClassName(bizPoint == null ? null : bizPoint.getClassName());
        });

        return mapPoints;
    }
}




