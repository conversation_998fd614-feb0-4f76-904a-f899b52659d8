package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.MapPoint;
import com.honichang.point.mapper.MapPointMapper;
import com.honichang.point.service.MapPointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 *
 */
@Service
public class MapPointServiceImpl extends ServiceImpl<MapPointMapper, MapPoint>
    implements MapPointService{


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByMapId(Long mapId) {
        // 清空
        this.lambdaUpdate()
                .set(MapPoint::getDelFlag, "2")
                .set(MapPoint::getUpdateTime, DateUtils.getNowDate())
                .set(MapPoint::getUpdateBy, SecurityUtils.getUserId().toString())
                .eq(MapPoint::getMapId, mapId)
                .update();
    }

    @Override
    public List<MapPoint> getPointsByMapId(Long mapId) {
        return list(new LambdaQueryWrapper<MapPoint>()
                .eq(MapPoint::getMapId, mapId)
                .eq(MapPoint::getDelFlag, "0"));
    }
}




