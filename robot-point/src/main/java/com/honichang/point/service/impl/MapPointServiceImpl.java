package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.model.Position;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.MapPoint;
import com.honichang.point.mapper.MapPointMapper;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.MapPointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.honichang.dispactch.model.enums.PointClassNameEnum.CROSSROADS;

/**
 *
 */
@Service
public class MapPointServiceImpl extends ServiceImpl<MapPointMapper, MapPoint>
    implements MapPointService{

    @Resource
    private BizPointService bizPointService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByMapId(Long mapId) {
        // 清空
        this.lambdaUpdate()
                .set(MapPoint::getDelFlag, "2")
                .set(MapPoint::getUpdateTime, DateUtils.getNowDate())
                .set(MapPoint::getUpdateBy, SecurityUtils.getUserId().toString())
                .eq(MapPoint::getMapId, mapId)
                .update();
    }

    @Override
    public List<MapPoint> getPointsByMapId(Long mapId) {

        List<MapPoint> mapPoints = this.lambdaQuery()
                .eq(MapPoint::getMapId, mapId)
                .eq(MapPoint::getDelFlag, "0")
                .list();

        List<BizPoint> allBizPoints = bizPointService.list();

        mapPoints.forEach(_p -> {
            List<BizPoint> bizPoints = allBizPoints.stream()
                    .filter(b -> Objects.equals(b.getMapPointId(), _p.getId()))
                    .collect(Collectors.toList());

            if (!bizPoints.isEmpty()) {
                BizPoint bp = bizPoints.get(0);
                _p.setBizClassName(bp.getClassName());
                _p.setBizPointId(bp.getId());
                _p.setBizInstanceName(bp.getInstanceName());
                _p.setIsCrossroads(bizPoints.stream()
                        .anyMatch(b -> CROSSROADS.getCode().equals(b.getClassName())));
            }
        });

        return mapPoints;
    }

    /**
     * 根据map_point.id从MapContext中获取Xmap点位
     * @param mapPointId map_point.id
     * @param mapId 地图id
     * @return Xmap点位
     * @throws ServiceException 地图点位不存在；地图不匹配，点位不存在
     */
    @Override
    public Position getXmapPosition(Long mapPointId, long mapId) {
        MapPoint mp = getById(mapPointId);
        if (mp == null) {
            throw new ServiceException("地图点位不存在：" + mapPointId, HttpStatus.ERROR);
        }
        Position p = MapContext.getXmapPoint(mp.getXmapId(), mapId);
        if (p == null) {
            throw new ServiceException("地图不匹配，点位不存在：" + mp.getXmapId(), HttpStatus.ERROR);
        }
        return p;
    }
}




