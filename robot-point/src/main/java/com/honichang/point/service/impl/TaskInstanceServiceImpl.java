package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.service.TaskInstanceNodeResultService;
import com.honichang.point.service.TaskInstanceNodeService;
import com.honichang.point.service.TaskInstanceService;
import com.honichang.point.mapper.TaskInstanceMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class TaskInstanceServiceImpl extends ServiceImpl<TaskInstanceMapper, TaskInstance>
    implements TaskInstanceService{

    @Resource
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;

    @Override
    @Transactional
    public boolean auditDataEdit(List<TaskInstanceNode> taskInstanceNode) {
        List<Long> taskInstanceIds = taskInstanceNode.stream().map(TaskInstanceNode::getTaskInstanceId).distinct().collect(Collectors.toList());
        for (Long taskInstanceId : taskInstanceIds) {
            TaskInstance taskInstance = new TaskInstance();
            taskInstance.setId(taskInstanceId);
            taskInstance.setAuditStatus("2");
            updateById(taskInstance);
        }
        boolean b = taskInstanceNodeService.updateBatchById(taskInstanceNode);
        for (TaskInstanceNode instanceNode : taskInstanceNode) {
            taskInstanceNodeResultService.updateBatchById(instanceNode.getTaskInstanceNodeResultList());
        }
        return b;
    }
}




