package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.service.TaskInstanceNodeResultService;
import com.honichang.point.service.TaskInstanceNodeService;
import com.honichang.point.service.TaskInstanceService;
import com.honichang.point.mapper.TaskInstanceMapper;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class TaskInstanceServiceImpl extends ServiceImpl<TaskInstanceMapper, TaskInstance>
    implements TaskInstanceService{

    @Resource
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;

    @Override
    @Transactional
    public boolean auditDataEdit(List<TaskInstanceNode> taskInstanceNode) {
        List<Long> taskInstanceIds = taskInstanceNode.stream().map(TaskInstanceNode::getTaskInstanceId).distinct().collect(Collectors.toList());
        for (Long taskInstanceId : taskInstanceIds) {
            TaskInstance taskInstance = new TaskInstance();
            taskInstance.setId(taskInstanceId);
            taskInstance.setAuditStatus("2");
            updateById(taskInstance);
        }
        boolean b = taskInstanceNodeService.updateBatchById(taskInstanceNode);
        for (TaskInstanceNode instanceNode : taskInstanceNode) {
            taskInstanceNodeResultService.updateBatchById(instanceNode.getTaskInstanceNodeResultList());
        }
        return b;
    }

    // region 创建空的任务实例

    @Override
    public TaskInstance createTaskByDef(long taskDefId, @NonNull List<TaskDefNode> taskDefNodes, long mapId) {

        if (taskDefNodes.isEmpty()) {
            throw new ServiceException("任务节点不存在！");
        }

        TaskInstance taskInstance = new TaskInstance();
        taskInstance.setTaskId(taskDefId);
        taskInstance.setMapId(mapId);
        taskInstance.setStartTime(DateUtils.getNowDate());
        taskInstance.setPointCount(taskDefNodes.size());
        taskInstance.setAiAlarmNum(0);
        taskInstance.setPointAlarmNum(0);
        taskInstance.setPointHealthNum(0);
        taskInstance.setUnreachableNum(0);
        taskInstance.setAbnormalNum(0);
        taskInstance.setAuditStatus("0");
        taskInstance.setStatus("1");
        taskInstance.setHisFlag("0");
        taskInstance.setDelFlag("0");
        taskInstance.setCreateTime(DateUtils.getNowDate());
        taskInstance.setCreateBy(SecurityUtils.getUserId().toString());
        this.save(taskInstance);
        return taskInstance;
    }

    // endregion


}




