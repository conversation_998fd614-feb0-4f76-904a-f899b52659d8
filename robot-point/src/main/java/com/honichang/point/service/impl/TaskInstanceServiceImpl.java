package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.RobotContext;
import com.honichang.dispactch.model.Task;
import com.honichang.dispactch.model.TaskDirectiveChain;
import com.honichang.dispactch.model.TaskNode;
import com.honichang.dispactch.model.base.Chain;
import com.honichang.dispactch.model.enums.LogRobotTypeEnum;
import com.honichang.dispactch.model.enums.RobotStatusEnum;
import com.honichang.dispactch.model.enums.RobotWorkStatusEnum;
import com.honichang.dispactch.model.enums.TaskStatusEnum;
import com.honichang.dispactch.service.DispatchService;
import com.honichang.dispactch.service.TaskService;
import com.honichang.dispactch.util.LogUtil;
import com.honichang.exception.NoReachablePathException;
import com.honichang.point.domain.TaskDef;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.domain.TaskInstance;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.mapper.TaskInstanceMapper;
import com.honichang.point.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.CYCLE_TASK;
import static com.honichang.dispactch.model.enums.RobotWorkStatusEnum.PERIOD_TASK;

/**
 *
 */
@Slf4j
@Service
public class TaskInstanceServiceImpl extends ServiceImpl<TaskInstanceMapper, TaskInstance>
        implements TaskInstanceService {

    @Resource
    private TaskInstanceNodeResultService taskInstanceNodeResultService;
    @Resource
    private TaskInstanceNodeService taskInstanceNodeService;
    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    @Resource
    private TaskDefNodeService taskDefNodeService;
    @Resource
    private TaskDefService taskDefService;
    @Resource
    @Lazy
    private TaskService taskService;
    @Resource
    private DispatchService dispatchService;

    @Override
    @Transactional
    public boolean auditDataEdit(List<TaskInstanceNode> taskInstanceNode) {
        List<Long> taskInstanceIds = taskInstanceNode.stream().map(TaskInstanceNode::getTaskInstanceId).distinct().collect(Collectors.toList());
        for (Long taskInstanceId : taskInstanceIds) {
            TaskInstance taskInstance = new TaskInstance();
            taskInstance.setId(taskInstanceId);
            taskInstance.setAuditStatus("2");
            updateById(taskInstance);
        }
        boolean b = taskInstanceNodeService.updateBatchById(taskInstanceNode);
        for (TaskInstanceNode instanceNode : taskInstanceNode) {
            taskInstanceNodeResultService.updateBatchById(instanceNode.getTaskInstanceNodeResultList());
        }
        return b;
    }

    // region 创建空的任务实例

    @Override
    public TaskInstance createTaskByDef(long taskDefId, long mapId) {

        TaskDef taskDef = taskDefService.getById(taskDefId);
        if (taskDef == null || !"0".equals(taskDef.getDelFlag())) {
            throw new ServiceException("任务不存在！", HttpStatus.ERROR);
        }

        List<TaskDefNode> taskDefNodes = taskDefNodeService.getByTaskId(taskDefId);
        if (taskDefNodes.isEmpty()) {
            throw new ServiceException("任务节点不存在！", HttpStatus.ERROR);
        }

        TaskInstance taskInstance = new TaskInstance();
        taskInstance.setTaskId(taskDefId);
        taskInstance.setMapId(mapId);
        taskInstance.setStartTime(null); // 还未正式开始
        taskInstance.setPointCount(taskDefNodes.size());
        taskInstance.setAiAlarmNum(0);
        taskInstance.setPointAlarmNum(0);
        taskInstance.setPointHealthNum(0);
        taskInstance.setUnreachableNum(0);
        taskInstance.setAbnormalNum(0);
        taskInstance.setAuditStatus("0");
        taskInstance.setStatus(TaskStatusEnum.PAUSED.getCode()); // 默认暂停
        taskInstance.setHisFlag("0");
        taskInstance.setDelFlag("0");
        taskInstance.setCreateTime(DateUtils.getNowDate());
        taskInstance.setCreateBy(SecurityUtils.getUserIdOrSystem());

        taskInstance.setPlanType(taskDef.getPlanType());
        taskInstance.setTaskName(taskDef.getTaskName());
        return taskInstance;
    }

    @Override
    public List<TaskInstance> getList(TaskInstance taskInstance) {
        return taskInstanceMapper.getList(taskInstance);
    }

    // endregion


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(@NonNull TaskInstance taskInstance, @NonNull TaskStatusEnum status, String reason) {
        taskInstance.setStatus(status.getCode());
        if (StringUtils.isNoneBlank(reason)) {
            taskInstance.setTerminationReason(reason);
        }
        updateById(taskInstance);
    }

    /**
     * 获取机器人负责的未完任务
     * ①按计划类型排序，周期任务在前，普通任务在后；②按优先级排序，优先级高的在前
     *
     * @param robotId 机器人ID
     * @return 任务列表
     */
    @Override
    public List<TaskInstance> getUnfinishedTasks(long robotId) {
        return taskInstanceMapper.getUnfinishedTasks(robotId);
    }

    /**
     * 终止任务
     *
     * @param robotId 机器人ID
     * @param reason  终止原因
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abortTask(long robotId, String reason) {
        RobotContext rc = RobotContext.get(robotId);
        TaskDirectiveChain tdc = rc.getTaskDirectiveChain();
        if (Chain.isEmpty(tdc) || tdc.getTaskInstance() == null) {
            return;
        }

        TaskInstance taskInstance = tdc.getTaskInstance();
        taskInstance.setRobotId(robotId); // 最后负责的机器人
        taskInstance.setFinishTime(DateUtils.getNowDate()); // 结束时间
        taskInstance.setHisFlag("1"); // 历史标记
        updateTaskStatus(taskInstance, TaskStatusEnum.TERMINATED, reason);

        // 取消导航
        try {
            dispatchService.abortNavigation(robotId);
        } catch (Exception e) {
            // ignore
        }
        // 清空指令队列
        tdc.clear();
        // 状态复位
        if (rc.getStatus() == RobotStatusEnum.WORKING) {
            rc.setStatus(RobotStatusEnum.STANDBY);
        }
        if (rc.getWorkStatus() == CYCLE_TASK || rc.getWorkStatus() == PERIOD_TASK) {
            rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);
        }
        if (rc.getTaskQueue().getHead() != null) {
            if (taskInstance.getId().equals(rc.getTaskQueue().getHead().getTaskInstance().getTaskId())) {
                rc.getTaskQueue().popHead();
            }
        }

        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.TASK,
                "终止当前任务，原因：" + reason,
                robotId, SecurityUtils.getUserIdOrSystem());
    }


    /**
     * 暂停任务
     *
     * @param robotId 机器人ID
     * @param reason  暂停原因
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseTask(long robotId, String reason) {
        RobotContext rc = RobotContext.get(robotId);
        TaskDirectiveChain tdc = rc.getTaskDirectiveChain();
        if (Chain.isEmpty(tdc) || tdc.getTaskInstance() == null || TaskStatusEnum.PAUSED.getCode().equals(tdc.getStatus())) {
            // 没有任务或已经是暂停状态
            return;
        }

        TaskInstance taskInstance = tdc.getTaskInstance();
        taskInstance.setRobotId(robotId); // 最后负责的机器人
        updateTaskStatus(taskInstance, TaskStatusEnum.PAUSED, reason);

        // 取消导航
        dispatchService.abortNavigation(robotId);
        // 清空指令队列
        tdc.clear();
        // 状态复位
        if (rc.getStatus() == RobotStatusEnum.WORKING) {
            rc.setStatus(RobotStatusEnum.STANDBY);
        }
        if (rc.getWorkStatus() == CYCLE_TASK || rc.getWorkStatus() == PERIOD_TASK) {
            rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);
        }

        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.TASK,
                "暂停当前任务，原因：" + reason,
                robotId, SecurityUtils.getUserIdOrSystem());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startOrResume(Task task, long robotId, String reason) {
        RobotContext rc = RobotContext.get(robotId);
        // 一定是空的directiveChain
        TaskDirectiveChain directiveChain = rc.getTaskDirectiveChain();

        TaskInstance taskInstance = task.getTaskInstance();
        // 待安排的节点
        List<TaskInstanceNode> pendingNodes = task.getTaskNodes().stream()
                .map(TaskNode::getTaskInstanceNode).collect(Collectors.toList());

        taskInstance.setRobotId(robotId); // 最后负责的机器人
        if (taskInstance.getStartTime() == null) {
            taskInstance.setStartTime(DateUtils.getNowDate()); // 开始时间
        }
        taskInstance.setStatus(TaskStatusEnum.EXECUTING.getCode()); // 执行中

        if (taskInstance.getId() == null) {
            // new to start
            save(taskInstance);
            // 重新设置id
            pendingNodes.forEach(n -> n.setTaskInstanceId(taskInstance.getId()));
            taskInstanceNodeService.saveBatch(pendingNodes);
        } else {
            // resume
            // 更新状态
            updateById(taskInstance);

            List<TaskInstanceNode> entityNodes = taskInstanceNodeService.getListByTaskInstance(taskInstance);
            // 过滤，只要未完成的节点
            pendingNodes = entityNodes.stream()
                    // 0未抵达 | 8不可达
                    .filter(n -> "0".equals(n.getStatus()) || "8".equals(n.getStatus()))
                    .collect(Collectors.toList());
        }

        if (pendingNodes.isEmpty()) {
            // TODO: 所有节点都完成了
            return false;
        }

        // 安排所有未完成的节点
        try {
            TaskDirectiveChain planChain = taskService.planTaskNodeOrders(rc, pendingNodes);
            if (planChain.isEmpty()) {
                // 全部都是不可达！！
                return false;
            }
            // 不可达列表
            //final List<TaskInstanceNode> unreachableNodes = pendingNodes.stream()
            //        .filter(x -> UNREACHABLE.getCode().equals(x.getStatus()))
            //        .collect(Collectors.toList());
            // 最后完成或终止时再统一计算和回写不可达数量
            //if (unreachableNodes.size() > 0) {
            //    taskInstance.setUnreachableNum(unreachableNodes.size());
            //    updateById(taskInstance);
            //    taskInstanceNodeService.updateBatchById(unreachableNodes);
            //}
            // 保存到RobotContext
            directiveChain.clear();
            directiveChain.setTaskInstance(taskInstance);
            directiveChain.append(planChain);
        } catch (NoReachablePathException e) {
            log.error("机器人[{}]规划任务节点路径异常", rc.getRobotName(), e);
            return false;
        }

        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.TASK, reason, robotId, SecurityUtils.getUserIdOrSystem());
        return true;
    }

    @Override
    public boolean periodTaskPassedToday(long defId, long robotId) {
        try {
            Date now = DateUtils.getNowDate();
            Date d1 = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, now), DateUtils.YYYY_MM_DD);
            Date d2 = DateUtils.addDays(d1, 1);
            return lambdaQuery()
                    .eq(TaskInstance::getTaskId, defId)
                    .eq(TaskInstance::getRobotId, robotId)
                    .between(TaskInstance::getStartTime, d1, d2)
                    .eq(TaskInstance::getDelFlag, "0")
                    .exists();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public long periodTaskPassedNum(long defId) {
        try {
            Date now = DateUtils.getNowDate();
            Date d1 = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, now), DateUtils.YYYY_MM_DD);
            Date d2 = DateUtils.addDays(d1, 1);
            return lambdaQuery()
                    .eq(TaskInstance::getTaskId, defId)
                    .between(TaskInstance::getStartTime, d1, d2)
                    .eq(TaskInstance::getDelFlag, "0")
                    .count();
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishedTask(long robotId, Long taskInstanceId) {
        RobotContext rc = RobotContext.get(robotId);
        TaskDirectiveChain tdc = rc.getTaskDirectiveChain();
        boolean inContext = Chain.isEmpty(tdc) || tdc.getTaskInstance() == null;

        TaskInstance taskInstance = getById(taskInstanceId);
        taskInstance.setRobotId(robotId); // 最后负责的机器人
        taskInstance.setFinishTime(DateUtils.getNowDate()); // 结束时间
        taskInstance.setHisFlag("1"); // 历史标记

        List<TaskInstanceNode> nodes = taskInstanceNodeService.getListByTaskInstance(taskInstance);
        taskInstance.setUnreachableNum((int) nodes.stream().filter(x -> "8".equals(x.getStatus())).count());
        taskInstance.setAbnormalNum((int) nodes.stream().filter(x -> "7".equals(x.getStatus())).count());
        taskInstance.setPointHealthNum((int) nodes.stream().filter(x -> "3".equals(x.getStatus())).count());
        taskInstance.setPointAlarmNum((int) nodes.stream().filter(x -> "9".equals(x.getStatus())).count());
        updateTaskStatus(taskInstance, TaskStatusEnum.COMPLETED, null);

        // 取消导航
        try {
            dispatchService.abortNavigation(robotId);
        } catch (Exception e) {
            // ignore
        }

        if (!inContext) {
            // 从RobotContext中删除
            tdc.clear();
            // 状态复位
            if (rc.getStatus() == RobotStatusEnum.WORKING) {
                rc.setStatus(RobotStatusEnum.STANDBY);
            }
            if (rc.getWorkStatus() == CYCLE_TASK || rc.getWorkStatus() == PERIOD_TASK) {
                rc.setWorkStatus(RobotWorkStatusEnum.STANDBY);
            }
        }
        if (rc.getTaskQueue().getHead() != null) {
            if (taskInstance.getId().equals(rc.getTaskQueue().getHead().getTaskInstance().getTaskId())) {
                rc.getTaskQueue().popHead();
            }
        }

        // 写日志
        LogUtil.addRobotLog(LogRobotTypeEnum.TASK,
                "完成任务",
                robotId, SecurityUtils.getUserIdOrSystem());
    }

    /**
     * 先从RobotContext中获取，如果没有则从DB中获取
     */
    private TaskInstance __findTaskInstanceEntity(long taskInstanceId) {
        TaskInstance result = null;
        for (RobotContext rc : RobotContext.values()) {
            TaskInstance ti = rc.getTaskDirectiveChain().getTaskInstance();
            if (ti != null && taskInstanceId == ti.getId()) {
                result = ti;
                break;
            }
        }
        if (result == null) {
            result = getById(taskInstanceId);
        }
        return result;
    }

    /**
     * 先从RobotContext中获取，如果没有则从DB中获取
     */
    private TaskInstance __findTaskInstanceEntity(long robotId, long taskInstanceId) {
        TaskInstance result = null;
        if (RobotContext.exists(robotId)) {
            TaskInstance ti = RobotContext.get(robotId).getTaskDirectiveChain().getTaskInstance();
            if (ti != null && taskInstanceId == ti.getId()) {
                result = ti;
            }
        }
        if (result == null) {
            result = getById(taskInstanceId);
        }
        return result;
    }


}




