package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.point.domain.*;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.MapPointService;
import com.honichang.point.service.RobotChargingPileService;
import com.honichang.point.mapper.RobotChargingPileMapper;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class RobotChargingPileServiceImpl extends ServiceImpl<RobotChargingPileMapper, RobotChargingPile>
    implements RobotChargingPileService{

    @Autowired
    private RobotChargingPileMapper robotChargingPileMapper;

    @Autowired
    private BizPointService bizPointService;

    @Autowired
    private MapPointService mapPointService;

    @Override
    public List<RobotChargingPile> selectPageList(RobotChargingPile robotChargingPile) {
        return robotChargingPileMapper.selectPageList(robotChargingPile);

    }

    @Override
    public void add(RobotChargingPile robotChargingPile) {


        BizPoint bizPoint = bizPointService.getById(robotChargingPile.getBizPointId());
        if (bizPoint == null) {
            throw new ServiceException("请选择充电桩点位！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<RobotChargingPile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobotChargingPile::getDelFlag, "0");
        List<RobotChargingPile> list = this.list(queryWrapper);
        if (list.stream().filter(b ->b.getBizPointId().intValue() == bizPoint.getId().intValue()).findAny().orElse(null) != null) {
            throw new ServiceException("充电桩已存在！", HttpStatus.ERROR);
        }


        MapPoint mapPoint = mapPointService.getById(bizPoint.getMapPointId());
        if (mapPoint == null) {
            throw new ServiceException("地图点位查询失败！", HttpStatus.ERROR);
        }

        robotChargingPile.setXmapId(mapPoint.getXmapId());
        robotChargingPile.setMapId(mapPoint.getMapId());
        robotChargingPile.setStatus("0");
        robotChargingPile.setDelFlag("0");
        robotChargingPile.setCreateTime(DateUtils.getNowDate());
        robotChargingPile.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        this.save(robotChargingPile);

    }

    @Override
    public void update(RobotChargingPile robotChargingPile) {

        if (robotChargingPile == null) {
            throw new ServiceException("充电桩为空！", HttpStatus.ERROR);
        }

        RobotChargingPile robotChargingPileEntity = this.getById(robotChargingPile.getId());
        if (robotChargingPileEntity == null) {
            throw new ServiceException("充电桩id为空！", HttpStatus.ERROR);
        }

        BizPoint bizPoint = bizPointService.getById(robotChargingPile.getBizPointId());
        if (bizPoint == null) {
            throw new ServiceException("请选择充电桩点位！", HttpStatus.ERROR);
        }

        MapPoint mapPoint = mapPointService.getById(bizPoint.getMapPointId());
        if (mapPoint == null) {
            throw new ServiceException("地图点位查询失败！", HttpStatus.ERROR);
        }

        LambdaQueryWrapper<RobotChargingPile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobotChargingPile::getDelFlag, "0");
        List<RobotChargingPile> list = this.list(queryWrapper);
        if (list.stream().filter(b -> b.getBizPointId().intValue() == bizPoint.getId().intValue() && b.getId().intValue() != robotChargingPile.getId().intValue() ).findAny().orElse(null) != null) {
            throw new ServiceException("充电桩已存在！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(RobotChargingPile::getIp, robotChargingPile.getIp())
                .set(RobotChargingPile::getPort, robotChargingPile.getPort())
                .set(RobotChargingPile::getLocation, robotChargingPile.getLocation())
                .set(RobotChargingPile::getBizPointId, bizPoint.getId())
                .set(RobotChargingPile::getXmapId, mapPoint.getXmapId())
                .set(RobotChargingPile::getMapId, mapPoint.getMapId())
                .set(RobotChargingPile::getRemark, robotChargingPile.getRemark())
                .set(RobotChargingPile::getUpdateTime, DateUtils.getNowDate())
                .set(RobotChargingPile::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(RobotChargingPile::getId, robotChargingPile.getId())
                .update();

    }

    @Override
    public void delete(Long id) {

        RobotChargingPile robotChargingPile = this.getById(id);
        if (robotChargingPile == null) {
            throw new ServiceException("充电桩id为空！", HttpStatus.ERROR);
        }

        this.lambdaUpdate()
                .set(RobotChargingPile::getDelFlag, "2")
                .set(RobotChargingPile::getUpdateTime, DateUtils.getNowDate())
                .set(RobotChargingPile::getUpdateBy, SecurityUtils.getLoginUser().getUser().getUserId().toString())
                .eq(RobotChargingPile::getId, id)
                .update();
    }
}




