package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.IdenRuleExpr;
import com.honichang.point.mapper.IdenRuleExprMapper;
import com.honichang.point.service.IdenRuleExprService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【iden_rule_expr(固定死：rule_result=1~3级报警，其他的不报警)】的数据库操作Service实现
* @createDate 2025-05-26 09:50:47
*/
@Service
public class IdenRuleExprServiceImpl extends ServiceImpl<IdenRuleExprMapper, IdenRuleExpr>
    implements IdenRuleExprService {

}




