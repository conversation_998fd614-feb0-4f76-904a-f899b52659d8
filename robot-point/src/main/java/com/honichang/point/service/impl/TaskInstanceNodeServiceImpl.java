package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.point.domain.*;
import com.honichang.point.mapper.TaskInstanceNodeMapper;
import com.honichang.point.service.*;
import com.honichang.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class TaskInstanceNodeServiceImpl extends ServiceImpl<TaskInstanceNodeMapper, TaskInstanceNode>
        implements TaskInstanceNodeService {

    @Resource
    private BizPointService bizPointService;

    @Resource
    private IdenModelService idenModelService;

    @Resource
    private BizPointAiParamService bizPointAiParamService;

    @Resource
    private BizPointIdenConfService bizPointIdenConfService;

    @Resource
    private IdenModelParamService idenModelParamService;
    @Autowired
    private ISysNoticeService iSysNoticeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskInstanceNode> createTaskNodesByDef(long taskDefId, TaskInstance taskInstance, List<TaskDefNode> taskDefNodes) {

        List<TaskInstanceNode> result = new ArrayList<>();

        taskDefNodes.forEach(taskDefNode -> {

            TaskInstanceNode taskInstanceNode = new TaskInstanceNode();
            taskInstanceNode.setTaskId(taskDefId);
            taskInstanceNode.setTaskInstanceId(taskInstance.getId());
            // 机器人先不保存，执行时再保存
            // rule_result等，也是执行时再保存
            taskInstanceNode.setAuditStatus("0");   // 未审核
            taskInstanceNode.setStatus("0");        // 未执行
            taskInstanceNode.setDelFlag("0");
            taskInstanceNode.setCreateTime(DateUtils.getNowDate());
            taskInstanceNode.setCreateBy(SecurityUtils.getUserId().toString());

            taskInstanceNode.setBizPointId(taskDefNode.getBizPointId());
            congested(taskInstanceNode, taskInstance);

            this.save(taskInstanceNode);
            result.add(taskInstanceNode);
        });

        return result;
    }

    /**
     * 充血模型
     * @param taskNode 任务节点
     */
    @Override
    public void congested(@NonNull TaskInstanceNode taskNode, @NonNull TaskInstance taskInstance) {

        // 👇业务点位
        BizPoint bizPoint = bizPointService.getById(taskNode.getBizPointId());
        if (bizPoint == null)
            throw new ServiceException("点位不存在！");

        taskNode.setBizPoint(bizPoint);
        taskNode.setDeptId(bizPoint.getDeptId());
        taskNode.setToolGroupId(bizPoint.getToolGroupId());
        taskNode.setToolId(bizPoint.getToolId());
        taskNode.setPointName(bizPoint.getInstanceName());
        taskNode.setIdenModelId(bizPoint.getIdenModelId());

        // 👇识别模型
        IdenModel idenModel = idenModelService.getById(bizPoint.getIdenModelId());
        if (idenModel == null)
            throw new ServiceException("识别模型不存在！");
        taskNode.setIdenModelName(idenModel.getIdenModelName());

        // 👇AI参数
        List<BizPointAiParam> aiParams = bizPointAiParamService.getListByBizPointId(taskNode.getBizPointId());
        taskNode.setAiParams(aiParams);

        // 👇区域画线配置
        List<BizPointIdenConf> idenConfs = bizPointIdenConfService.getListByPointId(taskNode.getBizPointId());
        taskNode.setIdenConfs(idenConfs);

        idenConfs.forEach(conf -> {
            List<IdenModelParam> idenModelParams = idenModelParamService.selectParamListByIdenConf(conf);
            conf.setIdenModelParams(idenModelParams);
        });

        // 👇地图点位
        bizPoint.setMapPoint(MapContext.getXmapPoint(bizPoint.getXmapId(), taskInstance.getMapId()));
        if (bizPoint.getMapPoint() == null) {
            throw new ServiceException(StringUtils.format("地图点位{}不存在！", bizPoint.getXmapId()));
        }
    }
}




