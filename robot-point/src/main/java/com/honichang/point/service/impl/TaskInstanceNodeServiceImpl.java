package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.common.annotation.DataScope;
import com.honichang.common.constant.HttpStatus;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.DateUtils;
import com.honichang.common.utils.SecurityUtils;
import com.honichang.common.utils.StringUtils;
import com.honichang.dispactch.context.MapContext;
import com.honichang.dispactch.model.enums.PointClassNameEnum;
import com.honichang.machine.domain.HistoryDto;
import com.honichang.point.domain.*;
import com.honichang.point.mapper.TaskInstanceNodeMapper;
import com.honichang.point.model.dto.TaskInstanceNodeDto;
import com.honichang.point.model.vo.TaskInstanceNodeVo;
import com.honichang.point.service.*;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class TaskInstanceNodeServiceImpl extends ServiceImpl<TaskInstanceNodeMapper, TaskInstanceNode>
        implements TaskInstanceNodeService {

    @Resource
    private TaskInstanceNodeMapper taskInstanceNodeMapper;

    @Resource
    private BizPointService bizPointService;

    @Resource
    private IdenModelService idenModelService;

    @Resource
    private BizPointAiParamService bizPointAiParamService;

    @Resource
    private BizPointIdenConfService bizPointIdenConfService;

    @Resource
    private IdenModelParamService idenModelParamService;

    @Resource
    private TaskDefNodeService taskDefNodeService;


    @Override
    public List<TaskInstanceNode> createTaskNodesByDef(long taskDefId, long mapId) {

        List<TaskDefNode> taskDefNodes = taskDefNodeService.getByTaskId(taskDefId);
        if (taskDefNodes.isEmpty()) {
            throw new ServiceException("任务节点不存在！", HttpStatus.ERROR);
        }

        List<TaskInstanceNode> result = new ArrayList<>();

        taskDefNodes.forEach(taskDefNode -> {

            TaskInstanceNode taskInstanceNode = new TaskInstanceNode();
            taskInstanceNode.setTaskId(taskDefId);
//            taskInstanceNode.setTaskInstanceId();
            // 机器人先不保存，执行时再保存
            // rule_result等，也是执行时再保存
            taskInstanceNode.setAuditStatus("0");   // 未审核
            taskInstanceNode.setStatus("0");        // 未执行
            taskInstanceNode.setDelFlag("0");
            taskInstanceNode.setCreateTime(DateUtils.getNowDate());
            taskInstanceNode.setCreateBy(SecurityUtils.getUserIdOrSystem());

            taskInstanceNode.setBizPointId(taskDefNode.getBizPointId());

            boolean checkResult = congestedAndCheck(taskInstanceNode, mapId);
            if (checkResult) {
                result.add(taskInstanceNode);
            }
        });

        return result;
    }

    /**
     * 充血模型TaskInstanceNode
     * @param taskNode 任务点位
     * @param mapId    地图id
     * @return 任务点位校验失败是false，成功是true
     */
    @Override
    public boolean congestedAndCheck(@NonNull TaskInstanceNode taskNode, long mapId) {

        // 👇业务点位
        BizPoint bizPoint = bizPointService.getById(taskNode.getBizPointId());
        if (bizPoint == null) {
            throw new ServiceException("点位不存在！", HttpStatus.ERROR);
        }
        // 检查点位是否合法
        if (StringUtils.isBlank(bizPoint.getXmapId())) {
            log.error(StringUtils.format("点位[{}]没有对应的xmapId，无法执行任务！", bizPoint.getInstanceName()));
            return false;
        }

        // 👇地图点位
        bizPoint.setMapPoint(MapContext.getXmapPoint(bizPoint.getXmapId(), mapId));
        if (bizPoint.getMapPoint() == null) {
            log.error(StringUtils.format("地图点位{}不存在！", bizPoint.getXmapId()));
            return false;
        }

        // 基本信息
        taskNode.setBizPoint(bizPoint);
        taskNode.setDeptId(bizPoint.getDeptId());
        taskNode.setToolGroupId(bizPoint.getToolGroupId());
        taskNode.setToolId(bizPoint.getToolId());
        taskNode.setPointName(bizPoint.getInstanceName());
        taskNode.setIdenModelId(bizPoint.getIdenModelId());

        // 👇识别模型
        if (PointClassNameEnum.IDEN_POINT.getCode().equals(bizPoint.getClassName())) {
            IdenModel idenModel;
            // 识别点位必须有识别模型和点位规则
            if (bizPoint.getIdenModelId() == null || (idenModel = idenModelService.getById(bizPoint.getIdenModelId())) == null) {
                log.error(StringUtils.format("点位[{}]没有对应的识别模型，无法执行任务！", bizPoint.getInstanceName()));
                return false;
            }
            if (bizPoint.getIdenRuleId() == null) {
                log.error(StringUtils.format("点位[{}]没有对应的识别规则，无法执行任务！", bizPoint.getInstanceName()));
                return false;
            }
            taskNode.setIdenModelName(idenModel.getIdenModelName());

            // 👇AI参数
            taskNode.setAiParams(bizPointAiParamService.getListByBizPointId(taskNode.getBizPointId()));

            // 👇区域画线配置
            taskNode.setIdenConfs(bizPointIdenConfService.getListByPointId(taskNode.getBizPointId()));
            if (taskNode.getIdenConfs().isEmpty()) {
                log.error(StringUtils.format("点位[{}]没有对应的区域画线配置，无法执行任务！", bizPoint.getInstanceName()));
                return false;
            }
            // 点位画线配置对应的识别参数
            for (BizPointIdenConf conf : taskNode.getIdenConfs()) {
                conf.setIdenModelParams(idenModelParamService.selectParamListByIdenConf(conf));
                if (conf.getIdenModelParams().isEmpty()) {
                    log.error(StringUtils.format("点位[{}]的区域画线配置[{}]没有对应的识别参数，无法执行任务！",
                            bizPoint.getInstanceName(),
                            conf.getIdenName()));
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 根据任务实例获取任务实例节点
     * @param instance 任务实例
     * @return 任务实例节点列表（充血）
     */
    @Override
    public List<TaskInstanceNode> getListByTaskInstance(TaskInstance instance) {

        List<BizPoint> points = bizPointService.getPointsByTaskDef(instance.getTaskId());

        List<TaskInstanceNode> nodes = this.lambdaQuery()
                .eq(TaskInstanceNode::getDelFlag, "0")
                .eq(TaskInstanceNode::getTaskInstanceId, instance.getId())
                .list();

        List<TaskInstanceNode> result = new ArrayList<>();

        for (TaskInstanceNode node : nodes) {
            node.setBizPoint(points.stream()
                    .filter(x -> x.getId().equals(node.getBizPointId()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException("任务点位不存在！")));

            if (congestedAndCheck(node, instance.getMapId())) {
                result.add(node);
            }
        }

        return result;
    }

    /**
     * 合并属性，并保存：<p/>
     * 到达时间、离开时间、状态
     * @param taskNodes RobotContext.taskDirectiveChain中的缓存数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeAndReflush(@NonNull List<TaskInstanceNode> taskNodes) {
        if (taskNodes.isEmpty()) return;
        final List<TaskInstanceNode> entities = taskInstanceNodeMapper.selectBatchIds(
                taskNodes.stream().map(TaskInstanceNode::getId).collect(Collectors.toList()));
        final List<TaskInstanceNode> upt = new ArrayList<>();

        entities.forEach(entity -> {
            TaskInstanceNode node = taskNodes.stream()
                    .filter(x -> x.getId().equals(entity.getId())).findFirst().orElse(null);
            if (node == null) return;
            boolean b = false;
            if (entity.getArriveTime() == null && node.getArriveTime() != null) {
                entity.setArriveTime(node.getArriveTime());
                b = true;
            }
            if (entity.getLeaveTime() == null && node.getLeaveTime() != null) {
                entity.setLeaveTime(node.getLeaveTime());
                b = true;
            }
            if (!node.getStatus().equals(entity.getStatus())) {
                entity.setStatus(node.getStatus());
                b = true;
            }
            if (b) {
                upt.add(entity);
            }
        });

        if (!upt.isEmpty()) {
            updateBatchById(upt);
        }
    }

    @Override
    public List<TaskInstanceNode> getHistory(HistoryDto historyDto) {
        return taskInstanceNodeMapper.getHistory(historyDto);
    }

    @DataScope(deviceAlias = "d")
    @Override
    public List<TaskInstanceNodeVo> selectAlarmPageList(TaskInstanceNodeDto taskInstanceNodeDto) {
        return taskInstanceNodeMapper.selectAlarmPageList(taskInstanceNodeDto);
    }
}




