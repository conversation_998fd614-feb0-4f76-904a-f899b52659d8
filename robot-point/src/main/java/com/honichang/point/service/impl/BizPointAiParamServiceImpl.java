package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.point.domain.BizPointAiParam;
import com.honichang.point.mapper.BizPointAiParamMapper;
import com.honichang.point.service.BizPointAiParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class BizPointAiParamServiceImpl extends ServiceImpl<BizPointAiParamMapper, BizPointAiParam>
    implements BizPointAiParamService{

    @Resource
    private BizPointAiParamMapper bizPointAiParamMapper;

    @Override
    @Transactional
    public boolean paramListSave(List<BizPointAiParam> bizPointAiParamList) {
        LambdaQueryWrapper<BizPointAiParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPointAiParam::getBizPointId, bizPointAiParamList.get(0).getBizPointId());
        remove(wrapper);
        return saveBatch(bizPointAiParamList);
    }

    @Override
    public List<BizPointAiParam> getListByBizPointId(Long bizPointId) {
        return bizPointAiParamMapper.selectList(new LambdaQueryWrapper<BizPointAiParam>()
                .eq(BizPointAiParam::getBizPointId, bizPointId)
                .eq(BizPointAiParam::getStatus, "0")
                .eq(BizPointAiParam::getDelFlag, "0"));
    }
}




