package com.honichang.point.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.googlecode.aviator.AviatorEvaluator;
import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.point.domain.*;
import com.honichang.point.domain.dto.AiResult;
import com.honichang.point.domain.dto.AlarmJson;
import com.honichang.point.domain.dto.ExpressionJson;
import com.honichang.point.domain.dto.NodeResultDto;
import com.honichang.point.mapper.TaskInstanceNodeResultMapper;
import com.honichang.point.model.dto.AIRecognitionParam;
import com.honichang.point.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class TaskInstanceNodeResultServiceImpl extends ServiceImpl<TaskInstanceNodeResultMapper, TaskInstanceNodeResult>
        implements TaskInstanceNodeResultService {

    @Autowired
    private BizPointService bizPointService;

    @Autowired
    private TaskInstanceService taskInstanceService;

    @Autowired
    private TaskInstanceNodeService taskInstanceNodeService;

    @Autowired
    private TaskInstanceNodeResultMapper taskInstanceNodeResultMapper;

    @Autowired
    private BizPointIdenConfService bizPointIdenConfService;

    @Autowired
    private BizPointAiParamService bizPointAiParamService;

    @Autowired
    private IdenRuleService idenRuleService;

    @Autowired
    private IdenRuleExprService idenRuleExprService;
    @Autowired
    private IdenModelService idenModelService;
    @Autowired
    private IdenModelParamService idenModelParamService;

    @Override
    public List<TaskInstanceNodeResult> getHistoryCurve(HistoryCurveDto historyCurveDto) {
        return taskInstanceNodeResultMapper.getHistoryCurve(historyCurveDto);
    }


    @Override
    public void nodeResultHandler(NodeResultDto nodeResultDto) {
        //任务点位
        LambdaQueryWrapper<TaskInstanceNode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TaskInstanceNode::getTaskInstanceId, nodeResultDto.getId());
        lambdaQueryWrapper.eq(TaskInstanceNode::getBizPointId, nodeResultDto.getBid());
        TaskInstanceNode taskInstanceNode = taskInstanceNodeService.getOne(lambdaQueryWrapper);
        //点位
        LambdaQueryWrapper<BizPoint> bizPointLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bizPointLambdaQueryWrapper.eq(BizPoint::getId, taskInstanceNode.getBizPointId());
        bizPointLambdaQueryWrapper.eq(BizPoint::getDelFlag, '0');
        BizPoint bizPoint = bizPointService.getById(taskInstanceNode.getBizPointId());

        //模型
        LambdaQueryWrapper<IdenModel> idenModelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        idenModelLambdaQueryWrapper.eq(IdenModel::getId, taskInstanceNode.getIdenModelId());
        idenModelLambdaQueryWrapper.eq(IdenModel::getDelFlag, '0');
        IdenModel idenModel = idenModelService.getOne(idenModelLambdaQueryWrapper);

        //模型参数
        LambdaQueryWrapper<IdenModelParam> idenModelParamLambdaQueryWrapper = new LambdaQueryWrapper<>();
        idenModelParamLambdaQueryWrapper.eq(IdenModelParam::getIdenModelId, taskInstanceNode.getIdenModelId());
        idenModelParamLambdaQueryWrapper.eq(IdenModelParam::getDelFlag, '0');
        idenModelParamLambdaQueryWrapper.orderByDesc(IdenModelParam::getId);
        List<IdenModelParam> idenModelParamList = idenModelParamService.list(idenModelParamLambdaQueryWrapper);


        //不可达
        if (bizPoint == null) {
            taskInstanceNode.setStatus("8");
        } else {
            if (bizPoint.getClassName().equals("IdenPoint")) {
                if (idenModel == null || idenModelParamList.size() == 0) {
                    taskInstanceNode.setStatus("8");
                } else {
                    //参数结果表
                    List<TaskInstanceNodeResult> taskInstanceNodeResultList = getAiResult(bizPoint, idenModel, idenModelParamList, taskInstanceNode, nodeResultDto);
                    //报警处理
                    generateAlarm(bizPoint, taskInstanceNode, taskInstanceNodeResultList);
                    if (taskInstanceNodeResultList.size() > 0) {
                        saveOrUpdateBatch(taskInstanceNodeResultList);
                    }
                }
            }else{

            }
        }
        //入库
        taskInstanceNode.setLeaveTime(new Date());
        taskInstanceNode.setUpdateBy("1");
        taskInstanceNode.setUpdateTime(new Date());
        taskInstanceNodeService.updateById(taskInstanceNode);

    }

    /**
     * 报警处理
     *
     * @param bizPoint
     */
    private void generateAlarm(BizPoint bizPoint, TaskInstanceNode taskInstanceNode, List<TaskInstanceNodeResult> taskInstanceNodeResultList) {

        LambdaQueryWrapper<IdenRule> idenRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        idenRuleLambdaQueryWrapper.eq(IdenRule::getId, bizPoint.getIdenRuleId());
        idenRuleLambdaQueryWrapper.eq(IdenRule::getDelFlag, '0');
        IdenRule idenRule = idenRuleService.getOne(idenRuleLambdaQueryWrapper);

        LambdaQueryWrapper<IdenRuleExpr> idenRuleExpQueryWrapper = new LambdaQueryWrapper<>();
        idenRuleExpQueryWrapper.eq(IdenRuleExpr::getDelFlag, '0');
        idenRuleExpQueryWrapper.orderByDesc(IdenRuleExpr::getId);
        List<IdenRuleExpr> idenRuleExprList = idenRuleExprService.list(idenRuleExpQueryWrapper);
        if (idenRule == null || idenRuleExprList.size() == 0) {
            return;
        }
        //执行结果
        List<AlarmJson> alarmJsonList = new ArrayList<>();
        for (IdenRuleExpr idenRuleExpr : idenRuleExprList) {
            List<ExpressionJson> expressionJsons = checkAlarmParams(idenRuleExpr, taskInstanceNodeResultList);
            if (expressionJsons != null) {
                //拼接表达式
                String expression = expressionJsons.stream()
                        .map(item -> item.getType() == 2
                                ? (item.getValue() instanceof Double
                                ? item.getValue().toString()
                                : "'" + item.getValue() + "'")
                                : item.getValue().toString())
                        .collect(Collectors.joining(""));
                try {
                    Boolean alarm = (Boolean) AviatorEvaluator.getInstance().execute(expression);
                    if (alarm) {
                        //触发报警
                        AlarmJson alarmJson = new AlarmJson();
                        alarmJson.setLv(Integer.parseInt(idenRuleExpr.getRuleResult()));
                        alarmJson.setParamsExpression(idenRuleExpr.getExpressionText());
                    }
                } catch (Exception e) {
                    log.info("规则表达式执行异常,异常信息:{}", e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        //处理结果集
        if (alarmJsonList.size() > 0) {
            alarmJsonList.sort(Comparator.comparingInt(AlarmJson::getLv));
            taskInstanceNode.setRuleResult(alarmJsonList.get(0).getLv().toString());
            List<String> list = alarmJsonList.stream().filter(item -> item.getLv() == 1 || item.getLv() == 2 || item.getLv() == 3).collect(Collectors.toList()).stream().map(AlarmJson::getParamsExpression).collect(Collectors.toList());
            taskInstanceNode.setRuleExpressionText(JSONArray.toJSONString(list));
        }
    }

    /**
     * 核验报警参数是否跳过
     *
     * @param idenRuleExpr
     * @param taskInstanceNodeResultList
     * @return
     */
    private List<ExpressionJson> checkAlarmParams(IdenRuleExpr idenRuleExpr, List<TaskInstanceNodeResult> taskInstanceNodeResultList) {
        List<ExpressionJson> expressionJsons = JSONArray.parseArray(idenRuleExpr.getExpressionJson(), ExpressionJson.class);
        // 是否跳过
        boolean stepFlag = false;
        for (ExpressionJson expressionJson : expressionJsons) {
            // 仅对 type == 2 的表达式进行处理
            if (expressionJson.getType() == 2) {
                Long paramId = Long.parseLong(expressionJson.getValue().toString());
                // 使用 filter 和 findFirst 来获取任务实例结果
                TaskInstanceNodeResult taskInstanceNodeResult = taskInstanceNodeResultList.stream()
                        .filter(item -> item.getIdenModelParamId().equals(paramId)
                                && ((item.getDataType() == 1 && item.getFloatValue() != null)
                                || (item.getDataType() == 2 && item.getEnumValue() != null)))
                        .findFirst()
                        .orElse(null);

                // 如果没有找到对应的结果，跳过该表达式并标记跳过
                if (taskInstanceNodeResult == null) {
                    log.info("表达式参数不存在或AI识别结果为空, 跳过执行该表达式");
                    stepFlag = true;
                    break;  // 跳出循环
                }

                // 设置 expressionJson 的值
                expressionJson.setValue(taskInstanceNodeResult.getDataType() == 1
                        ? taskInstanceNodeResult.getFloatValue()
                        : taskInstanceNodeResult.getEnumValue());
            }
        }
        return stepFlag ? null : expressionJsons;
    }

    /**
     * 匹配AI结果
     */
    private List<TaskInstanceNodeResult> getAiResult(BizPoint bizPoint, IdenModel idenModel, List<IdenModelParam> idenModelParamList, TaskInstanceNode taskInstanceNode, NodeResultDto nodeResultDto) {
        List<TaskInstanceNodeResult> resultList = new ArrayList<>();
        try {

            //conf
            LambdaQueryWrapper<BizPointIdenConf> bizPointIdenConfLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bizPointIdenConfLambdaQueryWrapper.eq(BizPointIdenConf::getPointId, bizPoint.getId());
            bizPointIdenConfLambdaQueryWrapper.eq(BizPointIdenConf::getDelFlag, '0');
            List<BizPointIdenConf> bizPointIdenConfList = bizPointIdenConfService.list(bizPointIdenConfLambdaQueryWrapper);
            //param
            LambdaQueryWrapper<BizPointAiParam> bizPointAiParamLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bizPointAiParamLambdaQueryWrapper.eq(BizPointAiParam::getBizPointId, bizPoint.getId());
            bizPointAiParamLambdaQueryWrapper.eq(BizPointAiParam::getDelFlag, '0');
            List<BizPointAiParam> bizPointAiParamList = bizPointAiParamService.list(bizPointAiParamLambdaQueryWrapper);

            //ai
            byte[] imageBytes = WebClient.create().get().uri(nodeResultDto.getPic().get(0)).retrieve().bodyToMono(byte[].class).block();
            String base64 = Base64.getEncoder().encodeToString(imageBytes);
            AIRecognitionParam aiRecognitionParam = new AIRecognitionParam();
            aiRecognitionParam.setIdenModelId(bizPoint.getIdenModelId().intValue());
            aiRecognitionParam.setPhotoBase64Str(base64);
            aiRecognitionParam.setBizPointIdenConfList(bizPointIdenConfList);
            aiRecognitionParam.setBizPointAiParamList(bizPointAiParamList);
            AiResult aiResult = JSONObject.parseObject(bizPointService.AIRecognition(aiRecognitionParam).toJSONString(), AiResult.class);

            //构建结果
            resultList = idenModelParamList.stream().map(idenModelParam -> {
                TaskInstanceNodeResult taskInstanceNodeResult = new TaskInstanceNodeResult();
                taskInstanceNodeResult.setIdenModelParamName(idenModelParam.getParamName());
                taskInstanceNodeResult.setIdenModelParamId(idenModelParam.getId());
                taskInstanceNodeResult.setTaskInstanceId(taskInstanceNode.getTaskInstanceId());
                taskInstanceNodeResult.setTaskInstanceNodeId(taskInstanceNode.getId());
                taskInstanceNodeResult.setTaskId(taskInstanceNodeResult.getTaskId());
                taskInstanceNodeResult.setDataType(Integer.parseInt(idenModelParam.getDataType()));
                taskInstanceNodeResult.setStatus("0");
                taskInstanceNodeResult.setDelFlag("0");
                taskInstanceNodeResult.setCreateBy("1");
                taskInstanceNodeResult.setCreateTime(new Date());
                if (aiResult.getCode() <= 201) {
                    AiResult.ResultDataParams resultDataParams = aiResult.findResultDataParams(idenModel.getIdenModelName(), idenModelParam.getParamName());
                    if (resultDataParams != null) {
                        if (taskInstanceNodeResult.getDataType() == 1) {
                            taskInstanceNodeResult.setFloatValue(resultDataParams.getValue() == null ? null : Double.valueOf(resultDataParams.getValue()));
                        } else {
                            taskInstanceNodeResult.setEnumKey(idenModelParam.getParamName());
                            taskInstanceNodeResult.setEnumValue(resultDataParams.getValue() == null ? null : resultDataParams.getValue());
                        }
                    }
                    taskInstanceNode.setStatus("3");
                } else {
                    taskInstanceNode.setStatus("7");
                }
                return taskInstanceNodeResult;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("AI识别结果转换异常：{}", e.getMessage());
            taskInstanceNode.setStatus("7");
        }
        return resultList;
    }

}




