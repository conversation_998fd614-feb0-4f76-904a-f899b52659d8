package com.honichang.point.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.honichang.machine.domain.HistoryCurveDto;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.domain.TaskInstanceNodeResult;
import com.honichang.point.service.TaskInstanceNodeResultService;
import com.honichang.point.mapper.TaskInstanceNodeResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class TaskInstanceNodeResultServiceImpl extends ServiceImpl<TaskInstanceNodeResultMapper, TaskInstanceNodeResult>
        implements TaskInstanceNodeResultService {


    @Autowired
    private TaskInstanceNodeResultMapper taskInstanceNodeResultMapper;

    @Override
    public List<TaskInstanceNodeResult> getHistoryCurve(HistoryCurveDto historyCurveDto) {
        return taskInstanceNodeResultMapper.getHistoryCurve(historyCurveDto);
    }
}




