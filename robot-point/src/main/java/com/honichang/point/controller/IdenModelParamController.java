package com.honichang.point.controller;


import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.service.IdenModelParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/iden/model/param")
public class IdenModelParamController extends BaseController {

    @Autowired
    private IdenModelParamService idenModelParamService;

    @GetMapping("/list")
    public TableDataInfo list(IdenModelParam idenModelParam)
    {
        startPage();
        List<IdenModelParam> list = idenModelParamService.selectParamList(idenModelParam);
        return getDataTable(list);
    }


    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        IdenModelParam idenModelParam = idenModelParamService.getById(id);
        return AjaxResult.success(idenModelParam);
    }

    @Log(title = "识别模型参数管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IdenModelParam idenModelParam) {
        idenModelParamService.add(idenModelParam);
        return AjaxResult.success();
    }

    @Log(title = "识别模型参数管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody IdenModelParam idenModelParam) {
        idenModelParamService.update(idenModelParam);
        return AjaxResult.success();
    }


    @Log(title = "识别模型参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            idenModelParamService.delete(id);
        }
        return AjaxResult.success();
    }

    @PutMapping("/updateParamStatus")
    public AjaxResult updateParamStatus(@RequestBody IdenModelParam idenModelParam)
    {
        idenModelParamService.updateParamStatus(idenModelParam);
        return AjaxResult.success();
    }



}
