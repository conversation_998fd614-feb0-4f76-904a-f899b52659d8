package com.honichang.point.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.IdenType;
import com.honichang.point.service.IdenTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/iden/type")
public class IdenTypeController extends BaseController {

    @Autowired
    private IdenTypeService idenTypeService;

    @GetMapping("/getIdenTypeTree")
    public AjaxResult getIdenTypeTree()
    {
        List<IdenType> list = idenTypeService.getIdenTypeTree();
        return AjaxResult.success(list);
    }



    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        IdenType idenType = idenTypeService.getById(id);
        return AjaxResult.success(idenType);
    }

    @Log(title = "识别类型管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IdenType idenType) {
        idenTypeService.add(idenType);
        return AjaxResult.success();
    }

    @Log(title = "识别类型管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody IdenType idenType) {
        idenTypeService.update(idenType);
        return AjaxResult.success();
    }


    @Log(title = "识别类型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            idenTypeService.delete(id);
        }
        return AjaxResult.success();
    }


    @GetMapping("/getAll")
    public AjaxResult getAll() {
        LambdaQueryWrapper<IdenType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenType::getDelFlag, "0");
        queryWrapper.eq(IdenType::getStatus, "0");
        queryWrapper.orderByDesc(IdenType::getId);
        List<IdenType> list = idenTypeService.list(queryWrapper);
        return AjaxResult.success(list);
    }


}
