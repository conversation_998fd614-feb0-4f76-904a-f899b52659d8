package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysDept;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.*;
import com.honichang.point.domain.Robot;
import com.honichang.point.model.vo.CommonTreeData;
import com.honichang.point.service.*;
import com.honichang.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/task/def")
public class TaskDefController extends BaseController {

    @Autowired
    private TaskDefService taskDefService;

    @Autowired
    private RobotService robotService;

    @Autowired
    private BizPointService bizPointService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private BizDeviceTreeService bizDeviceTreeService;

    @Autowired
    private TaskDefNodeService taskDefNodeService;

    @Autowired
    private TaskRobotService taskRobotService;

    @GetMapping("/list")
    public TableDataInfo list(TaskDef taskDef)
    {
        startPage();
        List<TaskDef> list = taskDefService.selectList(taskDef);
        return getDataTable(list);
    }

    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        TaskDef taskDef = taskDefService.getById(id);

        List<TaskDefNode> taskDefNodeList = taskDefNodeService.getByTaskId(id);
        List<TaskRobot> taskRobotList = taskRobotService.getByTaskId(id);

        List<Long> bizPointIds = new ArrayList<>();
        taskDefNodeList.stream().forEach(b->{
            bizPointIds.add(b.getBizPointId());
        });
        taskDef.setBizPointIds(bizPointIds);
        taskDef.setTaskRobotList(taskRobotList);
        return AjaxResult.success(taskDef);
    }

    @Log(title = "任务管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TaskDef taskDef) {
        taskDefService.add(taskDef);
        return AjaxResult.success();
    }

    @Log(title = "任务管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody TaskDef taskDef) {
        taskDefService.update(taskDef);
        return AjaxResult.success();
    }


    @Log(title = "任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            taskDefService.delete(id);
        }
        return AjaxResult.success();
    }

    /**
     * 根据部门获取点位树（一共三级，1级是部门，2级是toolGroup，3级是点位）
     * @return
     */
    @GetMapping("/getPointTree/{deptId}")
    public AjaxResult getPointTree(@PathVariable("deptId") Long deptId) {

        List<CommonTreeData> treeDataList = new ArrayList<>();

        BizDeviceTree deptBizDeviceTree = bizDeviceTreeService.getById(deptId);
        if(deptBizDeviceTree != null) {

            LambdaQueryWrapper<BizDeviceTree> deviceTreeQueryWrapper2 = new LambdaQueryWrapper<>();
            deviceTreeQueryWrapper2.eq(BizDeviceTree::getDelFlag, "0");
            deviceTreeQueryWrapper2.eq(BizDeviceTree::getStatus, "0");
            deviceTreeQueryWrapper2.eq(BizDeviceTree::getParentId, deptBizDeviceTree.getId());
            List<BizDeviceTree> deptBizDeviceTreeList2 = bizDeviceTreeService.list(deviceTreeQueryWrapper2);


            List<CommonTreeData> notToolGroupTreeNodeList = new ArrayList<>();//只有部门，没有toolGroup的点位
            List<CommonTreeData> toolGroupTreeNodeList = new ArrayList<>();

            for(BizDeviceTree toolGroupBizDeviceTree : deptBizDeviceTreeList2){

                //点位
                List<CommonTreeData> pointTreeNodeList = new ArrayList<>();

                LambdaQueryWrapper<BizPoint> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BizPoint::getDelFlag, "0");
                queryWrapper.eq(BizPoint::getStatus, "0");
                queryWrapper.eq(BizPoint::getDeptId, deptBizDeviceTree.getId());
                queryWrapper.eq(BizPoint::getToolGroupId, toolGroupBizDeviceTree.getId());
                List<BizPoint> bizPointList = bizPointService.list(queryWrapper);
                for(BizPoint bizPoint : bizPointList){

                    CommonTreeData bizPointNode = new CommonTreeData();
                    bizPointNode.setId(bizPoint.getId());
                    bizPointNode.setName(bizPoint.getInstanceName());
                    pointTreeNodeList.add(bizPointNode);
                }

                //toolGroup
                CommonTreeData toolGroupNode = new CommonTreeData();
                toolGroupNode.setId(toolGroupBizDeviceTree.getId());
                toolGroupNode.setName(toolGroupBizDeviceTree.getDeviceName());
                toolGroupNode.setChildren(pointTreeNodeList);
                toolGroupTreeNodeList.add(toolGroupNode);
            }


            //查询只有部门，没有toolGroup的点位

            LambdaQueryWrapper<BizPoint> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizPoint::getDelFlag, "0");
            queryWrapper.eq(BizPoint::getStatus, "0");
            queryWrapper.eq(BizPoint::getDeptId, deptBizDeviceTree.getId());
            queryWrapper.isNull(BizPoint::getToolGroupId);
            List<BizPoint> bizPointList = bizPointService.list(queryWrapper);

            for(BizPoint bizPoint : bizPointList){

                CommonTreeData bizPointNode = new CommonTreeData();
                bizPointNode.setId(bizPoint.getId());
                bizPointNode.setName(bizPoint.getInstanceName());
                notToolGroupTreeNodeList.add(bizPointNode);
            }

            toolGroupTreeNodeList.addAll(notToolGroupTreeNodeList);

            CommonTreeData deptTreeNode = new CommonTreeData();
            deptTreeNode.setId(deptBizDeviceTree.getId());
            deptTreeNode.setName(deptBizDeviceTree.getDeviceName());
            deptTreeNode.setChildren(toolGroupTreeNodeList);
            treeDataList.add(deptTreeNode);

        }


        return AjaxResult.success(treeDataList);
    }

    /**
     * 查询全部机器人
     * @return
     */
    @GetMapping("/getAllRobot")
    public AjaxResult getAllRobot() {
        LambdaQueryWrapper<Robot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Robot::getDelFlag, "0");
//        queryWrapper.eq(Robot::getStatus, "0");
        List<Robot> list = robotService.list(queryWrapper);
        return AjaxResult.success(list);
    }


    /**
     * 查询设备树的一级（相当于部门）
     * @return
     */
    @GetMapping("/getAllDept")
    public AjaxResult getAllDept() {
        LambdaQueryWrapper<BizDeviceTree> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizDeviceTree::getDelFlag, "0");
        queryWrapper.eq(BizDeviceTree::getStatus, "0");
        queryWrapper.eq(BizDeviceTree::getNodeType, 0);
        List<BizDeviceTree> list = bizDeviceTreeService.list(queryWrapper);
        return AjaxResult.success(list);
    }


}
