package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.common.utils.StringUtils;
import com.honichang.point.domain.*;
import com.honichang.point.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点位规则
 */
@Slf4j
@RestController
@RequestMapping("/iden/rule")
public class IdenRuleController extends BaseController {


    @Autowired
    private IdenRuleService idenRuleService;

    @Autowired
    private IdenTypeService idenTypeService;
    @Autowired
    private IdenModelService idenModelService;

    @Autowired
    private IdenRuleExprService idenRuleExprService;

    @Autowired
    private BizPointService bizPointService;

    @GetMapping("/idenModelList")
    public AjaxResult idenModelList() {
        LambdaQueryWrapper<IdenModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModel::getDelFlag, "0");
        queryWrapper.orderByDesc(IdenModel::getCreateTime);
        List<IdenModel> list = idenModelService.list(queryWrapper);
        return AjaxResult.success(list);
    }

    @PreAuthorize("@ss.hasPermi('iden:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(IdenRule idenRule) {
        startPage();
        LambdaQueryWrapper<IdenRule> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(idenRule.getIdenRuleName())) {
            queryWrapper.like(IdenRule::getIdenRuleName, idenRule.getIdenRuleName());
        }
        if (idenRule.getIdenRuleType() != null) {
            queryWrapper.eq(IdenRule::getIdenRuleType, idenRule.getIdenRuleType());
        }
        if (idenRule.getIdenModelId() != null) {
            queryWrapper.eq(IdenRule::getIdenModelId, idenRule.getIdenModelId());
        }


        queryWrapper.eq(IdenRule::getDelFlag, "0");
        queryWrapper.orderByDesc(IdenRule::getCreateTime);
        List<IdenRule> list = idenRuleService.list(queryWrapper);
        return getDataTable(list);
    }

    @GetMapping("/getAll")
    public AjaxResult getAll(IdenRule idenRule) {
        LambdaQueryWrapper<IdenRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdenRule::getDelFlag, '0');
        return AjaxResult.success(idenRuleService.list(wrapper));
    }

    @Log(title = "点位规则管理", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('iden:rule:add')")
    @PostMapping("/addIdenRule")
    public AjaxResult addIdenRule(@RequestBody IdenRule idenRule) {
        LambdaQueryWrapper<IdenRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenRule::getIdenRuleName, idenRule.getIdenRuleName().trim());
        queryWrapper.eq(IdenRule::getDelFlag, "0");
        List<IdenRule> list = idenRuleService.list(queryWrapper);
        if (list.size() > 0) {
            return AjaxResult.error("规则名称重复,请重新输入");
        } else {
            idenRule.setDelFlag("0");
            idenRule.setRuleNum(0);
            idenRule.setCreateBy(getUserId().toString());
            idenRule.setCreateTime(new Date());
            idenRuleService.save(idenRule);
        }
        return AjaxResult.success();
    }

    @Log(title = "点位规则管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('iden:rule:update')")
    @PostMapping("/updateIdenRule")
    public AjaxResult updateIdenRule(@RequestBody IdenRule idenRule) {
        IdenRule idenRuleNow = idenRuleService.getById(idenRule.getId());
        if (!idenRuleNow.getIdenModelId().equals(idenRule.getIdenModelId())) {
            LambdaUpdateWrapper<IdenRuleExpr> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(IdenRuleExpr::getIdenRuleId, idenRule.getId());
            queryWrapper.set(IdenRuleExpr::getDelFlag, "2");
            idenRuleExprService.update(queryWrapper);
            idenRuleNow.setRuleNum(0);
        }
        idenRuleNow.setUpdateBy(getUserId().toString());
        idenRuleNow.setUpdateTime(new Date());
        idenRuleNow.setIdenRuleName(idenRule.getIdenRuleName().trim());
        idenRuleNow.setIdenRuleType(idenRule.getIdenRuleType());
        idenRuleNow.setIdenModelId(idenRule.getIdenModelId());
        LambdaUpdateWrapper<IdenRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IdenRule::getId, idenRule.getId());
        idenRuleService.update(idenRule, updateWrapper);
        return AjaxResult.success();
    }


    @Log(title = "点位规则管理", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('iden:rule:remove')")
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        LambdaUpdateWrapper<BizPoint> pointLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        pointLambdaUpdateWrapper.eq(BizPoint::getDelFlag,'0');
        pointLambdaUpdateWrapper.in(BizPoint::getIdenRuleId,id);
        List<BizPoint> list =  bizPointService.list(pointLambdaUpdateWrapper);
        if (list.size()>0){
            return AjaxResult.error("规则模型已被点位引用,无法执行删除！");
        }
        LambdaUpdateWrapper<IdenRule> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(IdenRule::getId, id);
        queryWrapper.set(IdenRule::getDelFlag, "2");
        idenRuleService.update(queryWrapper);
        return AjaxResult.success();
    }


    @Log(title = "点位规则管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeAll")
    public AjaxResult removeAll(@RequestBody String[] ids) {
        if (ids.length > 0) {
            List<Long> idList = Arrays.asList(ids).stream().map(item -> Long.parseLong(item)).collect(Collectors.toList());
            LambdaUpdateWrapper<BizPoint> pointLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            pointLambdaUpdateWrapper.eq(BizPoint::getDelFlag,'0');
            pointLambdaUpdateWrapper.in(BizPoint::getIdenRuleId,idList);
            List<BizPoint> list =  bizPointService.list(pointLambdaUpdateWrapper);
            if (list.size()>0){
                return AjaxResult.error("规则模型已被点位引用,无法执行删除！");
            }else{
                LambdaUpdateWrapper<IdenRule> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.in(IdenRule::getId, idList);
                lambdaUpdateWrapper.set(IdenRule::getDelFlag, "2");
                idenRuleService.update(lambdaUpdateWrapper);
            }
        }
        return AjaxResult.success();
    }

    @GetMapping("/getByModelId/{modelId}")
    public AjaxResult getByModelId(@PathVariable Long modelId) {
        LambdaQueryWrapper<IdenRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdenRule::getIdenModelId, modelId);
        wrapper.eq(IdenRule::getDelFlag, '0');
        return AjaxResult.success(idenRuleService.list(wrapper));
    }

}
