package com.honichang.point.controller;


import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.IdenModelParam;
import com.honichang.point.domain.IdenModelParamGroup;
import com.honichang.point.service.IdenModelParamGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/iden/model/param/group")
public class IdenModelParamGroupController extends BaseController {

    @Autowired
    private IdenModelParamGroupService idenModelParamGroupService;

    @GetMapping("/list")
    public TableDataInfo list(IdenModelParamGroup idenModelParamGroup)
    {
        startPage();
        List<IdenModelParamGroup> list = idenModelParamGroupService.selectParamGroupList(idenModelParamGroup);
        return getDataTable(list);
    }

    @Log(title = "识别模型参数组管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IdenModelParamGroup idenModelParamGroup) {
        idenModelParamGroupService.add(idenModelParamGroup);
        return AjaxResult.success();
    }


    @Log(title = "识别模型参数组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            idenModelParamGroupService.delete(id);
        }
        return AjaxResult.success();
    }

}
