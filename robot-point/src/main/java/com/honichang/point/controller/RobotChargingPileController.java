package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.Robot;
import com.honichang.point.domain.RobotChargingPile;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.RobotChargingPileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/robot/charging/pile")
public class RobotChargingPileController extends BaseController {

    @Autowired
    private RobotChargingPileService robotChargingPileService;

    @Autowired
    private BizPointService bizPointService;

    @GetMapping("/list")
    public TableDataInfo list(RobotChargingPile robotChargingPile)
    {
        startPage();
        List<RobotChargingPile> list = robotChargingPileService.selectPageList(robotChargingPile);
        return getDataTable(list);
    }


    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        RobotChargingPile robotChargingPile = robotChargingPileService.getById(id);
        return AjaxResult.success(robotChargingPile);
    }

    @Log(title = "充电桩管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody RobotChargingPile robotChargingPile) {
        robotChargingPileService.add(robotChargingPile);
        return AjaxResult.success();
    }

    @Log(title = "充电桩管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody RobotChargingPile robotChargingPile) {
        robotChargingPileService.update(robotChargingPile);
        return AjaxResult.success();
    }


    @Log(title = "充电桩管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            robotChargingPileService.delete(id);
        }
        return AjaxResult.success();
    }

    /**
     * 查询充电桩类型的点位
     * @return
     */
    @GetMapping("/getBizPoint")
    public AjaxResult getBizPoint() {

        LambdaQueryWrapper<BizPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizPoint::getDelFlag, "0");
        queryWrapper.eq(BizPoint::getStatus, "0");
//        queryWrapper.eq(BizPoint::getClassName, "0");
        List<BizPoint> list = bizPointService.list(queryWrapper);
        return AjaxResult.success(list);

    }

}
