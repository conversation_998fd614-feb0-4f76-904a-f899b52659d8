package com.honichang.point.controller;

import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.IdenModel;
import com.honichang.point.service.IdenModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/iden/model")
public class IdenModelController extends BaseController {

    @Autowired
    private IdenModelService idenModelService;

    @GetMapping("/getIdenModelTree")
    public AjaxResult getIdenModelTree()
    {
        List<IdenModel> list = idenModelService.getIdenModelTree();
        return AjaxResult.success(list);
    }

    @GetMapping("/getAll")
    public AjaxResult getAll()
    {
        return AjaxResult.success(idenModelService.list());
    }

    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        IdenModel idenModel = idenModelService.getById(id);
        return AjaxResult.success(idenModel);
    }

    @Log(title = "识别模型管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IdenModel idenModel) {
        idenModelService.add(idenModel);
        return AjaxResult.success();
    }

    @Log(title = "识别模型管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody IdenModel idenModel) {
        idenModelService.update(idenModel);
        return AjaxResult.success();
    }


    @Log(title = "识别模型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            idenModelService.delete(id);
        }
        return AjaxResult.success();
    }


}
