package com.honichang.point.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.IdenTypeParam;
import com.honichang.point.service.IdenTypeParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.honichang.common.annotation.Log;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/iden/type/param")
public class IdenTypeParamController extends BaseController {

    @Autowired
    private IdenTypeParamService idenTypeParamService;

    @GetMapping("/list")
    public TableDataInfo list(IdenTypeParam idenTypeParam)
    {
        startPage();
        List<IdenTypeParam> list = idenTypeParamService.selectParamList(idenTypeParam);
        return getDataTable(list);
    }


    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        IdenTypeParam idenTypeParam = idenTypeParamService.getById(id);
        if(idenTypeParam.getDataType().equals("1")){
            JSONObject jsonObject = JSONObject.parseObject(idenTypeParam.getValueScopeJson());
            idenTypeParam.setMinValue(jsonObject.getDoubleValue("min"));
            idenTypeParam.setMaxValue(jsonObject.getDoubleValue("max"));
        }
        if(idenTypeParam.getDataType().equals("2")){
            List<String> str = JSONArray.parseArray(idenTypeParam.getValueScopeJson(), String.class);
            idenTypeParam.setEnumValue(String.join(",", str));
        }
        return AjaxResult.success(idenTypeParam);
    }

    @Log(title = "识别类型参数管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IdenTypeParam idenTypeParam) {
        idenTypeParamService.add(idenTypeParam);
        return AjaxResult.success();
    }

    @Log(title = "识别类型参数管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody IdenTypeParam idenTypeParam) {
        idenTypeParamService.update(idenTypeParam);
        return AjaxResult.success();
    }


    @Log(title = "识别类型参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        for(Long id : ids){
            idenTypeParamService.delete(id);
        }
        return AjaxResult.success();
    }

    @PutMapping("/updateParamStatus")
    public AjaxResult updateParamStatus(@RequestBody IdenTypeParam idenTypeParam)
    {
        idenTypeParamService.updateParamStatus(idenTypeParam);
        return AjaxResult.success();
    }

}
