package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.point.domain.AlarmAi;
import com.honichang.point.domain.MediaFile;
import com.honichang.point.domain.dto.AbnormalReceptionParams;
import com.honichang.point.service.AlarmAiService;
import com.honichang.point.service.MediaFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/alarm/ai")
public class AlarmAiController extends BaseController {

    @Autowired
    private AlarmAiService alarmAiService;

    @Autowired
    private MediaFileService mediaFileService;

    @GetMapping("/list")
    public TableDataInfo list(AlarmAi alarmAi)
    {
        startPage();
        List<AlarmAi> list = alarmAiService.selectPageList(alarmAi);
        list.stream().forEach(b->{
            LambdaQueryWrapper<MediaFile> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(MediaFile::getBizType, "alarm_ai");
            queryWrapper.eq(MediaFile::getBizId, b.getId());
            queryWrapper.eq(MediaFile::getDelFlag, "0");
            queryWrapper.eq(MediaFile::getStatus, "0");
            List<MediaFile> mediaFileList = mediaFileService.list(queryWrapper);
            b.setMediaFileList(mediaFileList);

            if(b.getConfirmPerson() != null){

            }

        });
        return getDataTable(list);
    }

    @PutMapping("/confirm")
    public AjaxResult confirm(@RequestBody AlarmAi alarmAi) {
        alarmAiService.confirm(alarmAi);
        return AjaxResult.success();
    }

    @RequestMapping("/abnormalReception")
    public AjaxResult abnormalReception(@RequestBody AbnormalReceptionParams abnormalReceptionParams) {
        alarmAiService.abnormalReception(abnormalReceptionParams);
        return AjaxResult.success("报警信息接收成功");
    }

}
