package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.point.domain.MediaFile;
import com.honichang.point.domain.TaskInstanceNode;
import com.honichang.point.model.dto.TaskInstanceNodeDto;
import com.honichang.point.model.vo.TaskInstanceNodeVo;
import com.honichang.point.service.MediaFileService;
import com.honichang.point.service.TaskInstanceNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/task/instance/node")
public class TaskInstanceNodeController extends BaseController {

    @Autowired
    private TaskInstanceNodeService taskInstanceNodeService;

    @Autowired
    private MediaFileService mediaFileService;

    @GetMapping("/list")
    public TableDataInfo list(TaskInstanceNodeDto taskInstanceNodeDto)
    {
        startPage();
        List<TaskInstanceNodeVo> list = taskInstanceNodeService.selectAlarmPageList(taskInstanceNodeDto);
        list.stream().forEach(b->{
            LambdaQueryWrapper<MediaFile> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(MediaFile::getBizType, "task_instance_node");
            queryWrapper.eq(MediaFile::getBizId, b.getId());
            queryWrapper.eq(MediaFile::getDelFlag, "0");
            queryWrapper.eq(MediaFile::getStatus, "0");
            List<MediaFile> mediaFileList = mediaFileService.list(queryWrapper);
            b.setMediaFileList(mediaFileList);

        });
        return getDataTable(list);
    }

}
