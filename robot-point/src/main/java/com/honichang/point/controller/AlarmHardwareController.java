package com.honichang.point.controller;

import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.point.domain.AlarmHardware;
import com.honichang.point.service.AlarmHardwareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/alarm/hardware")
public class AlarmHardwareController extends BaseController {

    @Autowired
    private AlarmHardwareService alarmHardwareService;

    @GetMapping("/list")
    public TableDataInfo list(AlarmHardware alarmHardware)
    {
        startPage();
        List<AlarmHardware> list = alarmHardwareService.selectPageList(alarmHardware);
        return getDataTable(list);
    }
}
