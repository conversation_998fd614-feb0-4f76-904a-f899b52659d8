package com.honichang.point.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.core.domain.model.LoginUser;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.exception.ServiceException;
import com.honichang.common.utils.file.FileUploadUtils;
import com.honichang.common.utils.file.MimeTypeUtils;
import com.honichang.common.utils.poi.ExcelUtil;
import com.honichang.point.domain.BizDeviceTree;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.domain.BizPointAiParam;
import com.honichang.point.domain.TaskDefNode;
import com.honichang.point.service.BizDeviceTreeService;
import com.honichang.point.service.BizPointAiParamService;
import com.honichang.point.service.BizPointService;
import com.honichang.point.service.TaskDefNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/point")
public class pointController extends BaseController {

    @Resource
    private BizPointService bizPointService;
    @Resource
    private BizDeviceTreeService bizDeviceTreeService;
    @Resource
    private TaskDefNodeService taskDefNodeService;
    @Resource
    private BizPointAiParamService bizPointAiParamService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody BizPoint bizPoint) {

        return toAjax(bizPointService.add(bizPoint));
    }

    @RequestMapping("/paramListSave")
    public AjaxResult paramListSave(@RequestBody List<BizPointAiParam> bizPointAiParamList) {

        return toAjax(bizPointAiParamService.paramListSave(bizPointAiParamList));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        LambdaQueryWrapper<TaskDefNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TaskDefNode::getBizPointId, ids);
        List<TaskDefNode> defNodeList = taskDefNodeService.list(wrapper);
        if (defNodeList.size() > 0) {
            throw new ServiceException("点位已被任务引用无法删除");
        }
        LambdaQueryWrapper<BizPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizPoint::getDelFlag, 2);
        queryWrapper.in(BizPoint::getId, ids);
        return toAjax(bizPointService.update(queryWrapper));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody BizPoint bizPoint) {

        return toAjax(bizPointService.edit(bizPoint));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(BizPoint bizPoint) {
        startPage();
        LambdaQueryWrapper<BizPoint> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(BizPoint::getId);
        List<BizPoint> list = bizPointService.list(lambdaQueryWrapper);
        for (BizPoint point : list) {
            LambdaQueryWrapper<BizDeviceTree> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BizDeviceTree::getId, point.getDeptId());
            BizDeviceTree deviceTree = bizDeviceTreeService.getOne(wrapper);
            point.setDeptName(deviceTree.getDeviceName());

            if (point.getToolGroupId() != null) {
                LambdaQueryWrapper<BizDeviceTree> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(BizDeviceTree::getId, point.getToolGroupId());
                BizDeviceTree deviceTree1 = bizDeviceTreeService.getOne(wrapper1);
                point.setToolGroupName(deviceTree1.getDeviceName());
            }

            LambdaQueryWrapper<BizPointAiParam> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BizPointAiParam::getBizPointId, point.getId());
            point.setBizPointAiParamList(bizPointAiParamService.list(queryWrapper));
        }
        return getDataTable(list);
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(BizPoint bizPoint) {
        return AjaxResult.success(bizPointService.list());
    }

    @RequestMapping("/getDeviceTree")
    public AjaxResult getDeviceTree(BizDeviceTree bizDeviceTree) {
        return AjaxResult.success(bizDeviceTreeService.list());
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(bizPointService.getById(id));
    }

    @PostMapping("/uploadImg")
    public AjaxResult uploadImg(@RequestParam("image") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            return error("上传图片异常，请联系管理员");
        }
        String imgUrl = FileUploadUtils.upload(RuoYiConfig.getUploadPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
        return AjaxResult.success(imgUrl);
    }

    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BizPoint> util = new ExcelUtil<BizPoint>(BizPoint.class);
        List<BizPoint> bizPointList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = bizPointService.importBizPoint(bizPointList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BizPoint> util = new ExcelUtil<BizPoint>(BizPoint.class);
        util.importTemplateExcel(response, "点位数据");
    }
}
