package com.honichang.point.controller;

import com.honichang.common.config.RuoYiConfig;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.domain.entity.SysUser;
import com.honichang.common.core.domain.model.LoginUser;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.utils.file.FileUploadUtils;
import com.honichang.common.utils.file.MimeTypeUtils;
import com.honichang.common.utils.poi.ExcelUtil;
import com.honichang.point.domain.BizPoint;
import com.honichang.point.service.BizPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/point")
public class pointController extends BaseController {

    @Resource
    private BizPointService bizPointService;

    @RequestMapping("/add")
    public AjaxResult add(@RequestBody BizPoint bizPoint) {
        return toAjax(bizPointService.save(bizPoint));
    }

    @RequestMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(bizPointService.removeByIds(Arrays.asList(ids)));
    }

    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody BizPoint bizPoint) {
        return toAjax(bizPointService.updateById(bizPoint));
    }

    @RequestMapping("/getList")
    public TableDataInfo getList(BizPoint bizPoint) {
        startPage();
        return getDataTable(bizPointService.list());
    }

    @RequestMapping("/getAll")
    public AjaxResult getAll(BizPoint bizPoint) {
        return AjaxResult.success(bizPointService.list());
    }

    @RequestMapping("/getOne/{id}")
    public AjaxResult getOne(@PathVariable Integer id) {
        return AjaxResult.success(bizPointService.getById(id));
    }

    @PostMapping("/uploadImg")
    public AjaxResult uploadImg(@RequestParam("image") MultipartFile file) throws Exception {
        if (file.isEmpty())
        {
            return error("上传图片异常，请联系管理员");
        }
        String imgUrl = FileUploadUtils.upload(RuoYiConfig.getUploadPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
        return AjaxResult.success(imgUrl);
    }

    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BizPoint> util = new ExcelUtil<BizPoint>(BizPoint.class);
        List<BizPoint> bizPointList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = bizPointService.importBizPoint(bizPointList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BizPoint> util = new ExcelUtil<BizPoint>(BizPoint.class);
        util.importTemplateExcel(response, "点位数据");
    }
}
