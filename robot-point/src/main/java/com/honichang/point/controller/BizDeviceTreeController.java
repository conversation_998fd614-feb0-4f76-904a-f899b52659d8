package com.honichang.point.controller;

import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.BizDeviceTree;
import com.honichang.point.service.BizDeviceTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

@RestController
@RequestMapping("/biz/device/tree")
public class BizDeviceTreeController extends BaseController {

    @Autowired
    private BizDeviceTreeService bizDeviceTreeService;

    @GetMapping("/list")
    public AjaxResult list(BizDeviceTree bizDeviceTree)
    {
        List<BizDeviceTree> bizDeviceTrees = bizDeviceTreeService.selectDeviceTreeList(bizDeviceTree);
        return success(bizDeviceTrees);
    }

    @GetMapping("/getById/{id}")
    public AjaxResult detailById(@PathVariable("id") Long id) {
        BizDeviceTree bizDeviceTree = bizDeviceTreeService.getById(id);
        if(bizDeviceTree.getParentId() != null && bizDeviceTree.getParentId().intValue() != 0){
            bizDeviceTree.setParent(bizDeviceTreeService.getById(bizDeviceTree.getParentId()));
        }
        return AjaxResult.success(bizDeviceTree);
    }

    @Log(title = "设备树管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BizDeviceTree bizDeviceTree) {
        bizDeviceTreeService.insertDeviceTree(bizDeviceTree);
        return AjaxResult.success();
    }

    @Log(title = "设备树管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody BizDeviceTree bizDeviceTree) {
        bizDeviceTreeService.updateDeviceTree(bizDeviceTree);
        return AjaxResult.success();
    }

    @Log(title = "设备树管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        bizDeviceTreeService.deleteDeviceTreeById(id);
        return AjaxResult.success();
    }

    @Log(title = "设备树管理", businessType = BusinessType.IMPORT)
    @PostMapping("import")
    public AjaxResult importFile(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, @RequestParam("file") MultipartFile file) throws IOException {
        bizDeviceTreeService.importFile(file);
        return AjaxResult.success();
    }

    @PostMapping("downloadTemplate")
    public void downloadTemplate(Integer id, HttpServletRequest httpServletRequest, HttpServletResponse response) {
        String fileName = "设备树导入模板.xlsx";
        InputStream inputStream = null;
        OutputStream out = null;
        try {

            response.reset();
            response.setContentType("application/force-download");
            // 支持中文名称文件,需要对header进行单独设置，不然下载的文件名会出现乱码或者无法显示的情况
            // 设置响应头，控制浏览器下载该文件
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            //通过文件路径获得File对象
            ClassPathResource classPathResource = new ClassPathResource("template/" + fileName);
            inputStream = classPathResource.getInputStream();

            //通过response获取ServletOutputStream对象(out)
            out = response.getOutputStream();
            int length = 0;
            byte[] buffer = new byte[1024];
            while ((length = inputStream.read(buffer)) != -1) {
                //4.写到输出流(out)中
                out.write(buffer, 0, length);
            }
            inputStream.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
