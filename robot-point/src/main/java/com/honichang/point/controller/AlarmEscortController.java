package com.honichang.point.controller;

import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.point.domain.AlarmEscort;
import com.honichang.point.service.AlarmEscortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/alarm/escort")
public class AlarmEscortController extends BaseController {

    @Autowired
    private AlarmEscortService alarmEscortService;

    @GetMapping("/list")
    public TableDataInfo list(AlarmEscort alarmEscort)
    {
        startPage();
        List<AlarmEscort> list = alarmEscortService.selectPageList(alarmEscort);
        return getDataTable(list);
    }
}
