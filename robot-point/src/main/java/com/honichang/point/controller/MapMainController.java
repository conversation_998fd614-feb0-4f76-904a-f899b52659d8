package com.honichang.point.controller;

import com.honichang.common.annotation.Log;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.enums.BusinessType;
import com.honichang.point.domain.MapMain;
import com.honichang.point.dto.cartography.MapMainGetByIdResponse;
import com.honichang.point.dto.cartography.MapMainGetLatestResponse;
import com.honichang.point.dto.cartography.MapMainUpdateRequest;
import com.honichang.point.dto.cartography.MapResponseBase;
import com.honichang.point.service.MapMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/map/main")
public class MapMainController {

    @Resource
    private MapMainService mapMainService;

    /**
     * 只能修改名称和比例尺
     * url[put]: /map/main/update
     */
    @Log(title = "地图信息更新", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@Validated @RequestBody MapMainUpdateRequest request) {
        mapMainService.updateNameAndScale(request.getMapId(), request.getMapName(), request.getScale());
        return AjaxResult.success();
    }

    /**
     * 获取最近修改的地图，用于前端默认展示
     * url[get]: /map/main/latest
     */
    @GetMapping("/latest")
    public AjaxResult getLatest() {
        MapMain mapMain = mapMainService.getLatest();
        if (mapMain != null) {
            MapMainGetLatestResponse response = MapResponseBase.convertFrom(MapMainGetLatestResponse.class, mapMain);
            __loadPointsAndPaths(response);
            return AjaxResult.success(response);
        } else {
            return AjaxResult.error("未找到地图");
        }
    }

    /**
     * 根据ID获取地图信息
     * url[get]: /map/main/{id}
     */
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable("id") Long id) {
        MapMain mapMain = mapMainService.getById(id);
        if (mapMain != null) {
            MapMainGetByIdResponse response = MapResponseBase.convertFrom(MapMainGetByIdResponse.class, mapMain);
            __loadPointsAndPaths(response);
            return AjaxResult.success(response);
        } else {
            return AjaxResult.error("未找到地图");
        }
    }

    private <T extends MapResponseBase> void __loadPointsAndPaths(T response) {
        mapMainService.loadPointsAndPaths(response);
        mapMainService.loadObstacles(response);
    }

}
