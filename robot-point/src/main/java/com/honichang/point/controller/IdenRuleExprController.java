package com.honichang.point.controller;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.honichang.common.annotation.Log;
import com.honichang.common.core.controller.BaseController;
import com.honichang.common.core.domain.AjaxResult;
import com.honichang.common.core.page.TableDataInfo;
import com.honichang.common.enums.BusinessType;
import com.honichang.common.utils.StringUtils;
import com.honichang.point.domain.*;
import com.honichang.point.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 点位规则
 */
@Slf4j
@RestController
@RequestMapping("/iden/ruleExpr")
public class IdenRuleExprController extends BaseController {


    @Autowired
    private IdenRuleExprService idenRuleExprService;

    @Autowired
    private IdenRuleService idenRuleService;

    @Autowired
    private IdenModelService idenModelService;

    @Autowired
    private IdenModelParamService idenModelParamService;

    @Autowired
    private IdenTypeService idenTypeService;

    @Autowired
    private IdenTypeParamService idenTypeParamService;

    @GetMapping("/list")
    public AjaxResult list(IdenRuleExpr idenRuleExpr) {
        LambdaQueryWrapper<IdenRuleExpr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenRuleExpr::getIdenRuleId, idenRuleExpr.getIdenRuleId());
        queryWrapper.eq(IdenRuleExpr::getDelFlag, '0');
        List<IdenRuleExpr> list = idenRuleExprService.list(queryWrapper);
        return AjaxResult.success(list);
    }


    @GetMapping("/getIdenParam")
    public AjaxResult getIdenParam(IdenRuleExpr idenRuleExpr) {
        List<Map<String, Object>> result = new ArrayList<>();
        IdenRule idenRule = idenRuleService.getById(idenRuleExpr.getIdenRuleId());
        IdenModel idenModel = idenModelService.getById(idenRule.getIdenModelId());
        LambdaQueryWrapper<IdenModelParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdenModelParam::getIdenModelId, idenModel.getId());
        queryWrapper.eq(IdenModelParam::getDelFlag, '0');
        List<IdenModelParam> idenModelParamList = idenModelParamService.list(queryWrapper);
        if (idenModelParamList != null && idenModelParamList.size() > 0) {
            idenModelParamList.forEach(idenModelParam -> {
                IdenTypeParam idenTypeParam = idenTypeParamService.getById(idenModelParam.getIdenTypeParamId());
                Map<String, Object> map = new HashMap<>();
                map.put("id", idenModelParam.getId());
                map.put("paramName", idenModelParam.getParamName());
                map.put("idenTypeName", idenModelParam.getIdenTypeName());
                map.put("dataType", idenTypeParam.getDataType());
                map.put("valueScopeJson", idenTypeParam.getValueScopeJson());
                result.add(map);
            });
        }
        return AjaxResult.success(result);
    }

    @Log(title = "点位规则管理-规则", businessType = BusinessType.INSERT)
    @PostMapping("/addIdenRuleExpr")
    public AjaxResult addIdenRuleExpr(@RequestBody IdenRuleExpr idenRuleExpr) {
        IdenRuleExpr expr = new IdenRuleExpr();
        List<Map> list = JSONArray.parseArray(idenRuleExpr.getExpressionJson(), Map.class);
        String expressionText = "";
        for (Map map : list) {
            if (map.get("type").toString().equals("1")) {
                expressionText += map.get("value").toString();
            } else if (map.get("type").toString().equals("2")) {
                expressionText += map.get("idenTypeName").toString();
            } else if (map.get("type").toString().equals("3")) {
                expressionText += map.get("value").toString();
            }
        }
        expr.setRuleResult(idenRuleExpr.getRuleResult());
        expr.setExpressionText(expressionText);
        expr.setExpressionJson(idenRuleExpr.getExpressionJson());
        expr.setIdenRuleId(idenRuleExpr.getIdenRuleId());
        expr.setDelFlag("0");
        expr.setCreateBy(getUserId().toString());
        expr.setCreateTime(new Date());
        idenRuleExprService.save(expr);

        IdenRule idenRule = idenRuleService.getById(idenRuleExpr.getIdenRuleId());
        idenRule.setRuleNum(idenRule.getRuleNum() + 1);
        idenRule.setUpdateBy(getUserId().toString());
        idenRule.setUpdateTime(new Date());
        idenRuleService.updateById(idenRule);
        return AjaxResult.success();
    }


    @Log(title = "点位规则管理-规则", businessType = BusinessType.UPDATE)
    @PostMapping("/updateIdenRuleExpr")
    public AjaxResult updateIdenRuleExpr(@RequestBody IdenRuleExpr idenRuleExpr) {
        List<Map> list = JSONArray.parseArray(idenRuleExpr.getExpressionJson(), Map.class);
        String expressionText = "";
        for (Map map : list) {
            if (map.get("type").toString().equals("1")) {
                expressionText += map.get("value").toString();
            } else if (map.get("type").toString().equals("2")) {
                expressionText += map.get("idenTypeName").toString();
            } else if (map.get("type").toString().equals("3")) {
                expressionText += map.get("value").toString();
            }
        }
        LambdaUpdateWrapper<IdenRuleExpr> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(IdenRuleExpr::getId, idenRuleExpr.getId());
        queryWrapper.set(IdenRuleExpr::getExpressionJson, idenRuleExpr.getExpressionJson());
        queryWrapper.set(IdenRuleExpr::getExpressionText, expressionText);
        queryWrapper.set(IdenRuleExpr::getRuleResult, idenRuleExpr.getRuleResult());
        queryWrapper.set(IdenRuleExpr::getCreateBy, getUserId().toString());
        queryWrapper.set(IdenRuleExpr::getCreateTime, new Date());
        idenRuleExprService.update(queryWrapper);
        return AjaxResult.success();
    }

    @Log(title = "点位规则管理-规则", businessType = BusinessType.DELETE)
    @PostMapping("/removeIdenRuleExpr")
    public AjaxResult removeIdenRuleExpr(@RequestBody IdenRuleExpr idenRuleExpr) {
        LambdaUpdateWrapper<IdenRuleExpr> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(IdenRuleExpr::getId, idenRuleExpr.getId());
        queryWrapper.set(IdenRuleExpr::getDelFlag, "2");
        idenRuleExprService.update(queryWrapper);

        IdenRule idenRule = idenRuleService.getById(idenRuleExpr.getIdenRuleId());
        idenRule.setRuleNum(idenRule.getRuleNum() - 1);
        idenRule.setUpdateBy(getUserId().toString());
        idenRule.setUpdateTime(new Date());
        idenRuleService.updateById(idenRule);
        return AjaxResult.success();
    }

}


