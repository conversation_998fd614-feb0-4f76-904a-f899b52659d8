-- 初始化点位类型字典
insert into sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) values ('点位类型', 'point_class_name', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '点位类型');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (0, '标记点', 'LandMark', 'point_class_name', 'default', 'Y', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '标记点');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (1, '充电点', 'ChargePoint', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '充电点');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (2, '识别', 'IdenPoint', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '识别');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (3, '红外测温', 'InfraredDetection', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '红外测温');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (4, '十字路口', 'Crossroads', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '十字路口');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (5, '出口', 'Exit', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '出口');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (6, '电梯', 'Elevator', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '电梯');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (7, '紧急洗眼器', 'EyeWashStation', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '紧急洗眼器');
insert into sys_dict_data (dict_sort, dict_label, dict_value, dict_type, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) values (8, '停车位', 'Parking', 'point_class_name', 'default', 'N', '0', 'admin', '2025-06-01', 'admin', '2025-06-01', '停车位');


-- 初始化系统默认配置
insert into sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) values (7, '遥控模式idle超时恢复(分钟)', 'remoteControlTimeout', '10', 'Y', 'system', current_date, 'system', current_date, '');
insert into sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) values (8, '陪同任务报警阈值(UWB超出多少米)', 'uwbOutOfRangeDistance', '5', 'Y', 'system', current_date, 'system', current_date, '默认超出5米播放报警语音');
insert into sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) values (9, '陪同任务报警阈值(超出距离持续多少分钟)', 'uwbOutOfRangeMinutes', '60', 'Y', 'system', current_date, 'system', current_date, '访客超出一定范围连续多少分钟，视为陪同任务失败。');
